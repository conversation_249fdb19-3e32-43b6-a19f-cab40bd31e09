/**
 * @file timer_manager.cpp
 * @brief Implementation of timer management for ATTINY85 Control System
 */

#include "modules/timer_manager.h"
#include "modules/air_control.h"

// Initialize static members
volatile uint16_t TimerManager::interruptCount = 0;

/**
 * @brief Initialize Timer1 for timing the air pressure control
 */
void TimerManager::initTimer1() {
    cli();  // disable global interrupts
    TCCR1 = 0;  // set all bits in TCCR1 register to 0
    
    // 244 count, match every 0.5 secs, 8MHz clock, prescaller @ 16,384
    OCR1A = 244; 
    
    // set CS10, CS11, CS12, and CS13 bits for 16,384 prescaler
    TCCR1 |= (1 << CS10);
    TCCR1 |= (1 << CS11);
    TCCR1 |= (1 << CS12);
    TCCR1 |= (1 << CS13);
    
    // reset Timer/Counter prescaler
    GTCCR |= (1 << PSR1);

    // enable Timer1 compare interrupt
    TIMSK |= (1 << OCIE1A);

    // enable global interrupts
    sei();
}

/**
 * @brief Get the current interrupt count
 * @return The current interrupt count
 */
uint16_t TimerManager::getInterruptCount() {
    return interruptCount;
}

/**
 * @brief Set the interrupt count
 * @param count The value to set the interrupt count to
 */
void TimerManager::setInterruptCount(uint16_t count) {
    interruptCount = count;
}

/**
 * @brief Reset the timer counter
 */
void TimerManager::resetCounter() {
    TCNT1 = 0;
    interruptCount = 0;
}

/**
 * @brief Get the maximum interrupt count (for timeout)
 * @return The maximum interrupt count
 */
uint16_t TimerManager::getMaxInterruptCount() {
    return INTERRUPT_COUNT_MAX;
}

/**
 * @brief Timer1 Compare Match A Interrupt Service Routine
 * Used for timing the air pressure control
 */
#if defined(ATTINY85) && !defined(NATIVE_TEST) && !defined(EMULATION_MODE)
// For AVR, use the ISR macro
ISR(TIM1_COMPA_vect)
#else
// For testing on native platform
extern "C" void TIM1_COMPA_vect(void)
#endif
{
    if (TimerManager::getInterruptCount() < TimerManager::getMaxInterruptCount()) {
        TimerManager::setInterruptCount(TimerManager::getInterruptCount() + 1);
    }
    
    if (TimerManager::getInterruptCount() >= TimerManager::getMaxInterruptCount()) {
        TimerManager::setInterruptCount(TimerManager::getMaxInterruptCount() + 1);
        air_turnOff_from_ISR();
    }
} 