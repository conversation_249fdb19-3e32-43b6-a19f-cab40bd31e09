/**
 * @file system.cpp
 * @brief Implementation of system integration for ATTINY85 Control System
 */

#include "modules/system.h"
#include "modules/pin_config.h"
#include "modules/pwm_control.h"
#include "modules/timer_manager.h"
#include "modules/air_control.h"
#include "modules/pump_control.h"

/**
 * @brief Initialize the entire system
 */
void System::init() {
    // Initialize all modules in the correct order
    PinConfig::init();
    PwmControl::init();
    TimerManager::initTimer1();
    AirControl::init();
    PumpControl::init();
}

/**
 * @brief Process system control loop
 * This is called repeatedly in the main loop
 */
void System::process() {
    // Process PWM input and output
    byte dutyCycle = PwmControl::processInputOutput();
    
    // Process pump control based on PWM duty cycle
    PumpControl::process(dutyCycle);
    
    // Process air pressure control based on sonicator input
    AirControl::process();
    
    // Debug actions can be added here if needed
    #if DEBUG_MODE == 1
    // Add debug code here
    #endif
} 