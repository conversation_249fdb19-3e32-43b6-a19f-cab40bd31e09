/**
 * @file pwm_control.cpp
 * @brief Implementation of PWM signal processing for ATTINY85 Control System
 */

#include "modules/pwm_control.h"

// Initialize static members
byte PwmControl::cyclePercent = 0;
byte PwmControl::pwmOutVal = 0;

/**
 * @brief Initialize PWM control by setting up Timer0
 */
void PwmControl::init() {
    // pin COM0B1 in non-inverting mode
    TCCR0A |= (1 << WGM01) | (1 << WGM00) | (1 << COM0B1);

    // Clock Divider (using 8MHz ... PWM = 122hz)
    TCCR0B |= (1 << CS02) | (0 << CS01) | (0 << CS00);

    // Enable ports
    DDRB |= (1 << DDB0) | (1 << DDB1);
}

/**
 * @brief Get PWM duty cycle from a pin
 * @param pin The pin to read PWM from
 * @return The duty cycle as a percentage (0-100)
 */
byte PwmControl::getDutyCycle(byte pin) {
    unsigned long highTime = pulseIn(pin, HIGH, 50000UL);  // 50 millisecond timeout
    unsigned long lowTime = pulseIn(pin, LOW, 50000UL);    // 50 millisecond timeout
  
    // pulseIn() returns zero on timeout
    if (highTime == 0 || lowTime == 0)
        return digitalRead(pin) ? 100 : 0;  // HIGH == 100%, LOW = 0%
    
    return (100 * highTime) / (highTime + lowTime);  // highTime as percentage of total cycle time
}

/**
 * @brief Set PWM output duty cycle
 * @param percentage The duty cycle percentage (0-100)
 * @param cappedAt Maximum allowed duty cycle percentage (0-100)
 */
void PwmControl::setOutput(byte percentage, byte cappedAt) {
    // Get duty cycle and apply cap if needed
    byte appliedPercentage = percentage;
    if (percentage > cappedAt) {
        appliedPercentage = cappedAt;
    }
    
    // Convert percentage to PWM value (0-255)
    pwmOutVal = 255 * (appliedPercentage / 100.0);
    
    // Set PWM output register
    OCR0B = pwmOutVal;
}

/**
 * @brief Process input PWM and set output accordingly
 * @return The current duty cycle percentage
 */
byte PwmControl::processInputOutput() {
    // Read PWM input
    cyclePercent = getDutyCycle(PinConfig::PWM_IN);
    
    // Calculate raw PWM output value
    pwmOutVal = 255 * (cyclePercent / 100.0);
    
    /* Cap output PWM to 77% duty cycle */
    if (cyclePercent > 77) {
        pwmOutVal = 255.0 * 0.77;
    }
    
    /* Only change PWM signal if PWM_in is valid PWM range */
    if (cyclePercent < 80 && cyclePercent > 2) {
        OCR0B = pwmOutVal;
    }
    
    return cyclePercent;
} 