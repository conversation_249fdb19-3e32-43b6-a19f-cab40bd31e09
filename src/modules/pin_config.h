/**
 * @file pin_config.h
 * @brief Pin configuration for ATTINY85 Control System
 */

#ifndef PIN_CONFIG_H
#define PIN_CONFIG_H

/**
 * @namespace PinConfig
 * @brief Contains pin definitions for the ATTINY85 Control System
 */
namespace PinConfig {
    // ATtiny85 pins
    const int PWM_OUT = 0;     ///< PWM output pin (OC0A, physical pin 5)
    const int AIR_OUT = 1;     ///< Air solenoid control (physical pin 6)
    const int PUMP_OUT = 2;    ///< Pump control (physical pin 7)
    const int SONIC_IN = 3;    ///< Sonicator input (physical pin 2)
    const int BUTTON_IN = 4;   ///< Button input (physical pin 3)
}

#endif // PIN_CONFIG_H 