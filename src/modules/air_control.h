/**
 * @file air_control.h
 * @brief Header for air pressure control module
 */

#ifndef AIR_CONTROL_H
#define AIR_CONTROL_H

#include <stdint.h>

/**
 * @class AirControl
 * @brief Manages the air pressure control system
 */
class AirControl {
public:
    /**
     * @brief Initialize air control system
     */
    static void init();
    
    /**
     * @brief Turn air pressure on
     */
    static void turnOn();
    
    /**
     * @brief Turn air pressure off
     */
    static void turnOff();
    
    /**
     * @brief Check if air pressure is on
     * @return True if air is on, false otherwise
     */
    static bool isOn();
    
    /**
     * @brief For testing: check if air is currently running
     * @return True if air is running, false otherwise
     */
    static bool isRunning() {
        return airStatus;
    }
    
    /**
     * @brief Process air pressure control logic
     * This checks the sonicator and manages the air pressure accordingly
     */
    static void process();
    
    /**
     * @brief Set the air status
     * @param status The status to set (true = on, false = off)
     */
    static void setStatus(bool status);
    
private:
    /**
     * @brief Update air status in EEPROM if changed
     */
    static void updateEeprom();
    
    static bool airStatus;     ///< Current air status
    static bool prevAirStatus; ///< Previous air status (for EEPROM updates)
};

/**
 * @brief Function to turn off air pressure from ISR
 * This is needed because interrupt handlers can't call class member functions directly
 */
void air_turnOff_from_ISR();

#endif // AIR_CONTROL_H 