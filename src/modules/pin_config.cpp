/**
 * @file pin_config.cpp
 * @brief Implementation of pin configuration for ATTINY85 Control System
 */

#include "modules/pin_config.h"

/**
 * @brief Initialize all pins with appropriate modes
 */
void PinConfig::init() {
    pinMode(SONIC_IN, INPUT);
    pinMode(PWM_IN, INPUT);
    pinMode(PUMP_OUT, OUTPUT);
    pinMode(PWM_OUT, OUTPUT);
    pinMode(AIR_OUT, OUTPUT);
    pinMode(STATUS_OUT, OUTPUT);
} 