/**
 * @file pump_control.cpp
 * @brief Implementation of pump control for ATTINY85 Control System
 */

#include "modules/pump_control.h"

// Initialize static members
bool PumpControl::pumpStatus = false;

/**
 * @brief Initialize pump control
 */
void PumpControl::init() {
    // Initialize pump to off state
    turnOff();
}

/**
 * @brief Turn pump on
 * Note: LOW == ON for pump control
 */
void PumpControl::turnOn() {
    pumpStatus = true;
    digitalWrite(PinConfig::PUMP_OUT, LOW);
}

/**
 * @brief Turn pump off
 * Note: HIGH == OFF for pump control
 */
void PumpControl::turnOff() {
    pumpStatus = false;
    digitalWrite(PinConfig::PUMP_OUT, HIGH);
}

/**
 * @brief Check if pump is on
 * @return True if pump is on, false otherwise
 */
bool PumpControl::isOn() {
    return pumpStatus;
}

/**
 * @brief Process pump control based on duty cycle
 * @param dutyCycle The current PWM duty cycle
 */
void PumpControl::process(byte dutyCycle) {
    // Control Flow Pump based on duty cycle
    if (dutyCycle >= 94) {
        turnOff();
    } else if (dutyCycle <= 1) {
        turnOn();
    }
} 