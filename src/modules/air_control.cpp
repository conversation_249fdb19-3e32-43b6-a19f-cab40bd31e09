/**
 * @file air_control.cpp
 * @brief Implementation of air pressure control for ATTINY85 Control System
 */

#include "modules/air_control.h"
#include "modules/pin_config.h"
#include "modules/timer_manager.h"
#include <EEPROM.h>

// Initialize static members
bool AirControl::airStatus = false;
bool AirControl::prevAirStatus = false;

/**
 * @brief Initialize air control system
 */
void AirControl::init() {
    // Read status from EEPROM
    airStatus = !EEPROM.read(0);
    prevAirStatus = airStatus;
    
    // Set initial state
    if (airStatus) {
        turnOn();
    } else {
        turnOff();
    }
}

/**
 * @brief Turn air pressure on
 */
void AirControl::turnOn() {
    airStatus = true;
    updateEeprom();
    digitalWrite(PinConfig::AIR_OUT, HIGH);
}

/**
 * @brief Turn air pressure off
 */
void AirControl::turnOff() {
    airStatus = false;
    updateEeprom();
    digitalWrite(PinConfig::AIR_OUT, LOW);
}

/**
 * @brief Check if air pressure is on
 * @return True if air is on, false otherwise
 */
bool AirControl::isOn() {
    return airStatus;
}

/**
 * @brief Process air pressure control logic
 * This checks the sonicator and manages the air pressure accordingly
 */
void AirControl::process() {
    // Check sonicator status
    int sonicVal = digitalRead(PinConfig::SONIC_IN);
    
    // If sonicator is on (LOW), turn air pressure on and reset timer
    if (sonicVal == LOW) {
        turnOn();
        TimerManager::resetCounter();
    }
}

/**
 * @brief Set the air status
 * @param status The status to set (true = on, false = off)
 */
void AirControl::setStatus(bool status) {
    if (status) {
        turnOn();
    } else {
        turnOff();
    }
}

/**
 * @brief Update air status in EEPROM if changed
 */
void AirControl::updateEeprom() {
    if (prevAirStatus != airStatus) {
        EEPROM.write(0, !airStatus);
    }
    prevAirStatus = airStatus;
}

/**
 * @brief Function to turn off air pressure from ISR
 * This is needed because interrupt handlers can't call class member functions directly
 */
void air_turnOff_from_ISR() {
    AirControl::turnOff();
} 