/**
 * @file main.cpp
 * @brief ATTINY85 Control System
 * 
 * Functionality: 
 * - Reads in PWM signal on PIN 4 and passes signal through to PIN 1 capped at 66% duty cycle 
 * - Once 90% duty cycle is detected on input, threshold out is raised high to turn off the pump
 * - While sonicator in is LOW (Sonicator is ON) -> air pressure out == HIGH (Air pressure is on) 
 * - When sonicator in goes HIGH (Sonicator is OFF) -> air pressure out == LOW (Air pressure is off) after 5 minutes
 *
 * Notes:
 * - Air Control:  LOW == OFF, HIGH == ON 
 * - Pump Control: LOW == ON,  HIGH == OFF
 */

#include <Arduino.h>
#include "modules/system.h"

/**
 * @brief Arduino setup function
 */
void setup() {
    // Initialize the system
    System::init();
}

/**
 * @brief Arduino loop function
 */
void loop() {
    // Process the system control loop
    System::process();
} 