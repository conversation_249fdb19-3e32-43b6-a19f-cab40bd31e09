https://www.re-innovation.co.uk/docs/fast-pwm-on-attiny85/

https://forum.arduino.cc/t/attiny85-pwm-frequency-duty/426524   -- best one 

https://www.gadgetronicx.com/attiny85-pwm-tutorial-phase-correct-fast-mode/


https://www.bing.com/videos/riverview/relatedvideo?q=100+hz+pwm+attiny85&mid=8D5C5C0870E52A03EC378D5C5C0870E52A03EC37&FORM=VIRE


ATTINY85:

Two pins that can be used to generate PWM 
 - OC0A & OC0B pins


TIMER 0 / 1 FAST PWM 

https://www.re-innovation.co.uk/docs/fast-pwm-on-attiny85/
