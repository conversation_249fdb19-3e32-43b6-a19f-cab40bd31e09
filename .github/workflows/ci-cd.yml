name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install junit-xml

      - name: Set up C++ environment
        run: |
          sudo apt-get update
          sudo apt-get install -y build-essential g++
      
      - name: Download Unity Test Framework
        run: |
          mkdir -p test/unity
          wget -O test/unity/unity.c https://github.com/ThrowTheSwitch/Unity/raw/master/src/unity.c
          wget -O test/unity/unity.h https://github.com/ThrowTheSwitch/Unity/raw/master/src/unity.h
          wget -O test/unity/unity_internals.h https://github.com/ThrowTheSwitch/Unity/raw/master/src/unity_internals.h
            
      - name: Run unit tests
        run: |
          mkdir -p test-reports
          chmod +x test/run_unit_tests.sh
          ./test/run_unit_tests.sh | tee test-output.txt
          
      - name: Generate JUnit XML
        run: |
          python -c '
          import re
          import sys
          from junit_xml import TestSuite, TestCase
          
          test_cases = []
          current_test = None
          
          with open("test-output.txt", "r") as f:
              output = f.read()
          
          # Parse test results
          test_pattern = re.compile(r"(test_.+?):(\w+)")
          for match in test_pattern.finditer(output):
              test_name, result = match.groups()
              test_case = TestCase(test_name, "ATtiny85ControlTests")
              
              if result == "FAIL":
                  # Find the failure message
                  test_case.add_failure_info("Test failed")
                  
              test_cases.append(test_case)
          
          # If no tests were found, add a placeholder
          if not test_cases:
              test_case = TestCase("no_tests_run", "ATtiny85ControlTests")
              test_case.add_skipped_info("No tests were executed")
              test_cases.append(test_case)
          
          # Create test suite
          test_suite = TestSuite("unit-tests", test_cases)
          
          # Write to file
          with open("test-reports/unit-tests.xml", "w") as f:
              f.write(TestSuite.to_xml_string([test_suite]))
          '
          
      - name: Generate HTML report
        if: always()
        run: |
          if [ -f "test-reports/unit-tests.xml" ]; then
            # Simple XML to HTML conversion
            echo "<html><head><title>Unit Test Results</title></head><body>" > test-reports/unit-tests.html
            echo "<h1>Unit Test Results</h1>" >> test-reports/unit-tests.html
            echo "<pre>" >> test-reports/unit-tests.html
            cat test-output.txt >> test-reports/unit-tests.html
            echo "</pre>" >> test-reports/unit-tests.html
            echo "</body></html>" >> test-reports/unit-tests.html
          else
            echo "<html><body><h1>No test results available</h1></body></html>" > test-reports/unit-tests.html
          fi
          
      - name: Archive test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: unit-test-results
          path: |
            test-reports/
            test-output.txt
  
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install platformio junit-xml
          sudo apt-get update
          sudo apt-get install -y simulavr
          
      - name: Cache PlatformIO
        uses: actions/cache@v3
        with:
          path: ~/.platformio
          key: ${{ runner.os }}-platformio-${{ hashFiles('platformio.ini') }}
          restore-keys: |
            ${{ runner.os }}-platformio-
            
      - name: Install environment dependencies
        run: |
          platformio pkg install --environment emulator
          
      - name: Create test directories
        run: |
          mkdir -p test-reports
          mkdir -p test-traces
          mkdir -p .pio/build/emulator
          
      - name: Check SimulAVR installation
        run: |
          # Verify SimulAVR is available and log version
          echo "SimulAVR version info:"
          simulavr --version || echo "SimulAVR not available or version command failed"
          # Check available AVR MCU types
          simulavr --listmcucandidates || echo "Could not list MCU candidates"
          
      - name: Run integration tests
        run: |
          # Set up more verbose output and increased timeout
          export PLATFORMIO_BUILD_FLAGS="-DEMULATION_TIMEOUT=60000 -DSIMULATOR_DEBUG=1"
          # Capture full command output
          platformio test -e emulator -v 2>&1 | tee emulator-test-output.txt || echo "Tests completed with issues"
          
          # Check for common error patterns in the output
          echo "Checking for common SimulAVR issues..."
          grep -i "error" emulator-test-output.txt || echo "No explicit errors found"
          grep -i "exception" emulator-test-output.txt || echo "No exceptions found"
          grep -i "not found" emulator-test-output.txt || echo "No 'not found' errors detected"
          
          # Save all VCD trace files if they exist
          find .pio/build/emulator -name "*.vcd" -exec cp {} test-traces/ \; || echo "No VCD files found"
        
      - name: Generate test report
        if: always()
        run: |
          # Create a basic HTML report from the test output
          echo "<html><head><title>Integration Test Results</title></head><body>" > test-reports/integration-tests.html
          echo "<h1>Integration Test Results</h1>" >> test-reports/integration-tests.html
          echo "<h2>SimulAVR Information</h2>" >> test-reports/integration-tests.html
          echo "<pre>" >> test-reports/integration-tests.html
          simulavr --version 2>&1 >> test-reports/integration-tests.html || echo "SimulAVR not available" >> test-reports/integration-tests.html
          echo "</pre>" >> test-reports/integration-tests.html
          echo "<h2>Test Output</h2>" >> test-reports/integration-tests.html
          echo "<pre>" >> test-reports/integration-tests.html
          cat emulator-test-output.txt >> test-reports/integration-tests.html
          echo "</pre>" >> test-reports/integration-tests.html
          echo "</body></html>" >> test-reports/integration-tests.html
          
          # Generate JUnit XML for CI integration
          python -c '
          from junit_xml import TestSuite, TestCase
          import re, sys
          
          # Parse the test output for results
          with open("emulator-test-output.txt", "r") as f:
              content = f.read()
          
          # Extract test results using regex
          test_cases = []
          for match in re.finditer(r"Test\s+(\w+)\.+\s*(\w+)", content):
              test_name, result = match.groups()
              case = TestCase(test_name, "EmulatorTests")
              if result.lower() != "pass" and result.lower() != "ok":
                  case.add_failure_info(f"Test failed with status: {result}")
              test_cases.append(case)
          
          # If no tests found, add a placeholder
          if not test_cases:
              case = TestCase("emulator_tests", "EmulatorTests")
              case.add_skipped_info("No test results could be parsed from output")
              test_cases.append(case)
              
          # Create and write the test suite
          suite = TestSuite("integration-tests", test_cases)
          with open("test-reports/integration-tests.xml", "w") as f:
              f.write(TestSuite.to_xml_string([suite]))
          ' || echo "Failed to generate JUnit XML, creating empty file"
          
          # Create empty file if generation failed
          if [ ! -f "test-reports/integration-tests.xml" ]; then
            echo '<?xml version="1.0" encoding="UTF-8"?><testsuites><testsuite name="integration-tests" tests="0" failures="0" errors="1"><error message="Failed to parse test results"/></testsuite></testsuites>' > test-reports/integration-tests.xml
          fi
          
      - name: Archive test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: integration-test-results
          path: |
            test-reports/
            emulator-test-output.txt
          
      - name: Archive VCD trace files
        if: always()
        continue-on-error: true
        uses: actions/upload-artifact@v3
        with:
          name: vcd-traces
          path: test-traces/
  
  build:
    name: Build Firmware
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install platformio
          
      - name: Cache PlatformIO
        uses: actions/cache@v3
        with:
          path: ~/.platformio
          key: ${{ runner.os }}-platformio-${{ hashFiles('platformio.ini') }}
          restore-keys: |
            ${{ runner.os }}-platformio-
            
      - name: Install AVR toolchain
        run: |
          sudo apt-get update
          sudo apt-get install -y gcc-avr avr-libc
            
      - name: Build with PlatformIO
        run: |
          # Build the firmware with verbose output
          platformio run -e attiny85 -v 2>&1 | tee build-output.txt
          
          # Check if build was successful
          if [ $? -eq 0 ]; then
            echo "Build successful!"
          else
            echo "Build failed. See output for details."
            exit 1
          fi
          
      - name: Verify firmware size
        run: |
          echo "Firmware size information:"
          platformio run -e attiny85 --target size | tee size-output.txt
          
          # Extract size information
          PROGRAM_SIZE=$(grep "Program:" size-output.txt | awk '{print $2}')
          DATA_SIZE=$(grep "Data:" size-output.txt | awk '{print $2}')
          MAX_SIZE=$(grep "Maximum is" size-output.txt | awk '{print $3}')
          
          echo "Program size: $PROGRAM_SIZE bytes"
          echo "Data size: $DATA_SIZE bytes"
          echo "Maximum size: $MAX_SIZE bytes"
          
          # Check if firmware size exceeds flash size (8KB for ATtiny85)
          if [ -n "$PROGRAM_SIZE" ] && [ -n "$MAX_SIZE" ]; then
            if [ $PROGRAM_SIZE -gt $MAX_SIZE ]; then
              echo "Error: Firmware size ($PROGRAM_SIZE bytes) exceeds maximum size ($MAX_SIZE bytes)"
              exit 1
            else
              USAGE_PERCENT=$((PROGRAM_SIZE * 100 / MAX_SIZE))
              echo "Firmware uses $USAGE_PERCENT% of available flash"
              
              if [ $USAGE_PERCENT -gt 90 ]; then
                echo "Warning: Firmware is using more than 90% of available flash"
              fi
            fi
          else
            echo "Could not determine firmware size"
          fi
      
      - name: Generate build report
        run: |
          # Create a detailed build report
          echo "# ATtiny85 Control System Build Report" > build-report.md
          echo "" >> build-report.md
          echo "## Build Information" >> build-report.md
          echo "- Date: $(date)" >> build-report.md
          echo "- Commit: ${{ github.sha }}" >> build-report.md
          echo "- Branch: ${{ github.ref }}" >> build-report.md
          echo "" >> build-report.md
          
          echo "## Firmware Size" >> build-report.md
          echo '```' >> build-report.md
          cat size-output.txt >> build-report.md
          echo '```' >> build-report.md
          echo "" >> build-report.md
          
          echo "## Build Output" >> build-report.md
          echo '```' >> build-report.md
          cat build-output.txt >> build-report.md
          echo '```' >> build-report.md
          
          # Upload build report as artifact
          mkdir -p build-reports
          cp build-report.md build-reports/
          cp size-output.txt build-reports/
          cp build-output.txt build-reports/
            
      - name: Archive firmware artifacts
        uses: actions/upload-artifact@v3
        with:
          name: firmware
          path: |
            .pio/build/attiny85/firmware.hex
            .pio/build/attiny85/firmware.elf
            
      - name: Archive build reports
        uses: actions/upload-artifact@v3
        with:
          name: build-reports
          path: build-reports/
  
  publish-test-report:
    name: Publish Test Report
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: always()
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Create report directories
        run: |
          mkdir -p test-reports/unit
          mkdir -p test-reports/integration
          mkdir -p combined-report
      
      - name: Download unit test results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: unit-test-results
          path: test-reports/unit
          
      - name: Download integration test results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: integration-test-results
          path: test-reports/integration
          
      - name: Generate combined report
        run: |
          echo '<html><head><title>Test Reports</title></head><body>' > combined-report/index.html
          echo '<h1>Test Reports</h1>' >> combined-report/index.html
          
          if [ -f "test-reports/unit/unit-tests.html" ]; then
            echo '<h2>Unit Tests</h2>' >> combined-report/index.html
            echo '<iframe src="unit-tests.html" width="100%" height="500px"></iframe>' >> combined-report/index.html
            cp test-reports/unit/unit-tests.html combined-report/
          else
            echo '<h2>Unit Tests</h2><p>No unit test results available</p>' >> combined-report/index.html
          fi
          
          if [ -f "test-reports/integration/integration-tests.html" ]; then
            echo '<h2>Integration Tests</h2>' >> combined-report/index.html
            echo '<iframe src="integration-tests.html" width="100%" height="500px"></iframe>' >> combined-report/index.html
            cp test-reports/integration/integration-tests.html combined-report/
          else
            echo '<h2>Integration Tests</h2><p>No integration test results available</p>' >> combined-report/index.html
          fi
          
          echo '</body></html>' >> combined-report/index.html
          
      - name: Upload combined report
        uses: actions/upload-artifact@v3
        with:
          name: combined-test-report
          path: combined-report/ 

  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y cppcheck clang-format
          
      - name: Run cppcheck static analysis
        run: |
          echo "Running cppcheck static analysis..."
          mkdir -p analysis-reports
          cppcheck --enable=all --std=c++11 --suppress=missingIncludeSystem \
            --error-exitcode=1 src/ test/unit/ test/integration/ \
            2>&1 | tee analysis-reports/cppcheck-results.txt || echo "Cppcheck found issues"
            
      - name: Verify code formatting
        run: |
          echo "Verifying code formatting..."
          FORMAT_ISSUES=0
          FORMATTED_FILES=""
          
          # Find all C++ files
          CPP_FILES=$(find src test -name "*.cpp" -o -name "*.h" -type f)
          
          for file in $CPP_FILES; do
              # Check if the file is correctly formatted
              clang-format --dry-run --Werror "$file" 2>/dev/null
              if [ $? -ne 0 ]; then
                  echo "❌ $file is not properly formatted."
                  FORMAT_ISSUES=$((FORMAT_ISSUES + 1))
                  FORMATTED_FILES="$FORMATTED_FILES\n- $file"
              fi
          done
          
          echo "Found $FORMAT_ISSUES files with formatting issues"
          echo "$FORMATTED_FILES" > analysis-reports/format-issues.txt
          
      - name: Check header files for include guards
        run: |
          echo "Checking header files for include guards..."
          GUARD_ISSUES=0
          GUARD_FILES=""
          
          # Find all header files
          HEADER_FILES=$(find src test -name "*.h" -type f)
          
          for file in $HEADER_FILES; do
              # Extract filename for include guard
              filename=$(basename "$file" .h)
              dirname=$(dirname "$file" | tr / _ | tr '[:lower:]' '[:upper:]')
              expected_guard="${dirname}_${filename}_H"
              
              # Check for expected include guard
              if ! grep -q "#ifndef ${filename}_H" "$file" && ! grep -q "#ifndef ${expected_guard}" "$file"; then
                  echo "❌ $file is missing proper include guards."
                  GUARD_ISSUES=$((GUARD_ISSUES + 1))
                  GUARD_FILES="$GUARD_FILES\n- $file"
              fi
          done
          
          echo "Found $GUARD_ISSUES files with include guard issues"
          echo "$GUARD_FILES" > analysis-reports/guard-issues.txt
          
      - name: Generate quality report
        run: |
          # Determine status of each check
          if [ -s analysis-reports/cppcheck-results.txt ]; then
            CPPCHECK_STATUS="failed"
          else
            CPPCHECK_STATUS="passed"
          fi
          
          if [ -s analysis-reports/format-issues.txt ]; then
            FORMAT_STATUS="failed"
          else
            FORMAT_STATUS="passed"
          fi
          
          if [ -s analysis-reports/guard-issues.txt ]; then
            GUARD_STATUS="failed"
          else
            GUARD_STATUS="passed"
          fi
          
          # Generate markdown report
          cat > analysis-reports/analysis-report.md << EOF
          # Code Quality Analysis Report
          
          ## Overall Status
          
          | Check | Status |
          |-------|--------|
          | Static Analysis | $CPPCHECK_STATUS |
          | Code Formatting | $FORMAT_STATUS |
          | Include Guards | $GUARD_STATUS |
          
          ## Static Analysis Details
          
          \`\`\`
          $(cat analysis-reports/cppcheck-results.txt)
          \`\`\`
          
          EOF
          
          if [ -s analysis-reports/format-issues.txt ]; then
            cat >> analysis-reports/analysis-report.md << EOF
          ## Formatting Issues
          
          The following files have formatting issues:
          $(cat analysis-reports/format-issues.txt)
          
          To fix formatting issues, run:
          \`\`\`
          clang-format -i <filename>
          \`\`\`
          
          EOF
          fi
          
          if [ -s analysis-reports/guard-issues.txt ]; then
            cat >> analysis-reports/analysis-report.md << EOF
          ## Include Guard Issues
          
          The following files have include guard issues:
          $(cat analysis-reports/guard-issues.txt)
          
          Each header file should have proper include guards:
          
          \`\`\`cpp
          #ifndef FILENAME_H
          #define FILENAME_H
          
          // File contents
          
          #endif // FILENAME_H
          \`\`\`
          
          EOF
          fi
          
      - name: Archive analysis reports
        uses: actions/upload-artifact@v3
        with:
          name: code-quality-reports
          path: analysis-reports/

  code-coverage:
    name: Code Coverage Analysis
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
          
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y lcov gcc g++
          pip install gcovr
          
      - name: Download Unity Test Framework
        run: |
          mkdir -p test/unity
          wget -O test/unity/unity.c https://github.com/ThrowTheSwitch/Unity/raw/master/src/unity.c
          wget -O test/unity/unity.h https://github.com/ThrowTheSwitch/Unity/raw/master/src/unity.h
          wget -O test/unity/unity_internals.h https://github.com/ThrowTheSwitch/Unity/raw/master/src/unity_internals.h
            
      - name: Compile tests with coverage flags
        run: |
          mkdir -p test/build
          mkdir -p coverage-reports
          
          # Clean previous coverage data
          rm -f test/build/*.gcda test/build/*.gcno
          
          # Compile each test with coverage
          for test_file in test/unit/test_*.cpp; do
              test_name=$(basename $test_file .cpp)
              echo "Compiling test: $test_name"
              
              g++ -std=c++11 -o test/build/$test_name \
                  -I. \
                  -Isrc \
                  -Itest \
                  -Itest/unity \
                  -DNATIVE_TEST \
                  --coverage -fprofile-arcs -ftest-coverage \
                  $test_file \
                  test/mocks/Arduino.cpp \
                  test/unity/unity.c
                  
              if [ $? -ne 0 ]; then
                  echo "❌ Compilation failed: $test_name"
              fi
          done
          
      - name: Run tests with coverage
        run: |
          # Run all tests and collect coverage data
          echo "Running tests and collecting coverage data..."
          for test_exe in test/build/test_*; do
              if [ -x "$test_exe" ]; then
                  echo "Running test: $(basename $test_exe)"
                  $test_exe
              fi
          done
            
      - name: Generate coverage report
        run: |
          # Generate coverage report
          echo "Generating coverage report..."
          lcov --capture --directory . --output-file coverage-reports/coverage.info
          lcov --remove coverage-reports/coverage.info '*/test/*' '/usr/*' --output-file coverage-reports/coverage.info
          
          # Generate HTML report
          genhtml coverage-reports/coverage.info --output-directory coverage-reports/html
          
          # Get modules and generate markdown report
          modules=$(find src/modules -name "*.cpp" | sed 's|.*/||' | sed 's|\.cpp$||')
          
          echo "# Code Coverage Report" > coverage-reports/coverage-report.md
          echo "" >> coverage-reports/coverage-report.md
          echo "## Module Coverage" >> coverage-reports/coverage-report.md
          echo "" >> coverage-reports/coverage-report.md
          echo "| Module | Line Coverage | Function Coverage |" >> coverage-reports/coverage-report.md
          echo "|--------|---------------|-------------------|" >> coverage-reports/coverage-report.md
          
          # Use gcovr for summary
          gcovr --xml coverage-reports/coverage.xml
          
          # Extract overall stats
          total_lines=$(gcovr | grep TOTAL | awk '{print $3}')
          total_covered=$(gcovr | grep TOTAL | awk '{print $5}')
          total_percentage=$(gcovr | grep TOTAL | awk '{print $9}')
          
          # Extract per-module stats
          for module in $modules; do
              module_stats=$(gcovr --json -r . | grep -A 20 "$module.cpp")
              if [ -n "$module_stats" ]; then
                  line_cov=$(echo "$module_stats" | grep -oP '"line_covered":\s*\K\d+' | head -1)
                  line_total=$(echo "$module_stats" | grep -oP '"line_total":\s*\K\d+' | head -1)
                  if [ -n "$line_cov" ] && [ -n "$line_total" ] && [ "$line_total" -gt 0 ]; then
                      percentage=$(echo "scale=2; 100 * $line_cov / $line_total" | bc)
                      echo "| $module | ${percentage}% | - |" >> coverage-reports/coverage-report.md
                  else
                      echo "| $module | 0% | - |" >> coverage-reports/coverage-report.md
                  fi
              else
                  echo "| $module | No data | - |" >> coverage-reports/coverage-report.md
              fi
          done
          
          echo "" >> coverage-reports/coverage-report.md
          echo "## Overall Coverage" >> coverage-reports/coverage-report.md
          echo "" >> coverage-reports/coverage-report.md
          echo "| Metric | Coverage | Target |" >> coverage-reports/coverage-report.md
          echo "|--------|----------|--------|" >> coverage-reports/coverage-report.md
          echo "| Lines | $total_percentage | 90% |" >> coverage-reports/coverage-report.md
          
          echo "" >> coverage-reports/coverage-report.md
          echo "## Coverage Details" >> coverage-reports/coverage-report.md
          echo "" >> coverage-reports/coverage-report.md
          echo "Detailed coverage report is available in the HTML report." >> coverage-reports/coverage-report.md
          
      - name: Archive coverage reports
        uses: actions/upload-artifact@v3
        with:
          name: coverage-reports
          path: coverage-reports/
  
  pipeline-validation:
    name: Pipeline Validation
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, build, publish-test-report, code-quality, code-coverage]
    if: always()
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Create validation directories
        run: |
          mkdir -p pipeline-validation
          mkdir -p pipeline-validation/unit-tests
          mkdir -p pipeline-validation/integration-tests
          mkdir -p pipeline-validation/build
      
      - name: Download unit test results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: unit-test-results
          path: pipeline-validation/unit-tests
          
      - name: Download integration test results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: integration-test-results
          path: pipeline-validation/integration-tests
          
      - name: Download build results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: build-reports
          path: pipeline-validation/build
          
      - name: Download firmware artifacts
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: firmware
          path: pipeline-validation/firmware
          
      - name: Download code quality results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: code-quality-reports
          path: pipeline-validation/code-quality
          
      - name: Download coverage results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: coverage-reports
          path: pipeline-validation/coverage
          
      - name: Validate pipeline results
        run: |
          # Check the status of previous jobs
          UNIT_TESTS_STATUS="${{ needs.unit-tests.result }}"
          INTEGRATION_TESTS_STATUS="${{ needs.integration-tests.result }}"
          BUILD_STATUS="${{ needs.build.result }}"
          
          echo "Pipeline Job Status:"
          echo "--------------------"
          echo "Unit Tests: $UNIT_TESTS_STATUS"
          echo "Integration Tests: $INTEGRATION_TESTS_STATUS"
          echo "Build: $BUILD_STATUS"
          echo "--------------------"
          
          # Check firmware size
          if [ -f "pipeline-validation/build/size-output.txt" ]; then
            echo "Firmware size check:"
            cat pipeline-validation/build/size-output.txt
          else
            echo "Firmware size information not available"
          fi
          
          # Generate pipeline validation report
          echo "# Pipeline Validation Report" > pipeline-validation/report.md
          echo "" >> pipeline-validation/report.md
          echo "## Pipeline Status" >> pipeline-validation/report.md
          echo "| Job | Status |" >> pipeline-validation/report.md
          echo "|-----|--------|" >> pipeline-validation/report.md
          echo "| Unit Tests | $UNIT_TESTS_STATUS |" >> pipeline-validation/report.md
          echo "| Integration Tests | $INTEGRATION_TESTS_STATUS |" >> pipeline-validation/report.md
          echo "| Build | $BUILD_STATUS |" >> pipeline-validation/report.md
          echo "| Code Quality | $CODE_QUALITY_STATUS |" >> pipeline-validation/report.md
          echo "| Code Coverage | $CODE_COVERAGE_STATUS |" >> pipeline-validation/report.md
          echo "" >> pipeline-validation/report.md
          
          # Check for test result files
          echo "## Test Results Summary" >> pipeline-validation/report.md
          
          UNIT_TEST_COUNT=0
          UNIT_TEST_FAILURES=0
          if [ -f "pipeline-validation/unit-tests/test-output.txt" ]; then
            UNIT_TEST_COUNT=$(grep -c "test_.*:PASS" pipeline-validation/unit-tests/test-output.txt || echo 0)
            UNIT_TEST_FAILURES=$(grep -c "test_.*:FAIL" pipeline-validation/unit-tests/test-output.txt || echo 0)
            echo "Unit tests: $UNIT_TEST_COUNT tests, $UNIT_TEST_FAILURES failures" >> pipeline-validation/report.md
          else
            echo "Unit test results not available" >> pipeline-validation/report.md
          fi
          
          # Check if firmware was built
          echo "## Firmware Status" >> pipeline-validation/report.md
          if [ -f "pipeline-validation/firmware/firmware.hex" ]; then
            echo "✅ Firmware build successful" >> pipeline-validation/report.md
            HEX_SIZE=$(wc -c < pipeline-validation/firmware/firmware.hex)
            echo "Firmware hex file size: $HEX_SIZE bytes" >> pipeline-validation/report.md
          else
            echo "❌ Firmware build failed or artifacts not available" >> pipeline-validation/report.md
          fi
          
          # Check overall pipeline status
          echo "## Overall Status" >> pipeline-validation/report.md
          if [[ "$UNIT_TESTS_STATUS" == "success" && "$BUILD_STATUS" == "success" ]]; then
            echo "✅ Pipeline completed successfully" >> pipeline-validation/report.md
            OVERALL_STATUS="success"
          else
            echo "❌ Pipeline has issues that need attention" >> pipeline-validation/report.md
            OVERALL_STATUS="failure"
          fi
          
          # Create HTML report
          echo "<html><head><title>Pipeline Validation Report</title></head><body>" > pipeline-validation/report.html
          echo "<h1>Pipeline Validation Report</h1>" >> pipeline-validation/report.html
          
          echo "<h2>Pipeline Status</h2>" >> pipeline-validation/report.html
          echo "<table border='1'>" >> pipeline-validation/report.html
          echo "<tr><th>Job</th><th>Status</th></tr>" >> pipeline-validation/report.html
          echo "<tr><td>Unit Tests</td><td>$UNIT_TESTS_STATUS</td></tr>" >> pipeline-validation/report.html
          echo "<tr><td>Integration Tests</td><td>$INTEGRATION_TESTS_STATUS</td></tr>" >> pipeline-validation/report.html
          echo "<tr><td>Build</td><td>$BUILD_STATUS</td></tr>" >> pipeline-validation/report.html
          echo "<tr><td>Code Quality</td><td>$CODE_QUALITY_STATUS</td></tr>" >> pipeline-validation/report.html
          echo "<tr><td>Code Coverage</td><td>$CODE_COVERAGE_STATUS</td></tr>" >> pipeline-validation/report.html
          echo "</table>" >> pipeline-validation/report.html
          
          echo "<h2>Test Results Summary</h2>" >> pipeline-validation/report.html
          if [ -f "pipeline-validation/unit-tests/test-output.txt" ]; then
            echo "<p>Unit tests: $UNIT_TEST_COUNT tests, $UNIT_TEST_FAILURES failures</p>" >> pipeline-validation/report.html
            echo "<pre>" >> pipeline-validation/report.html
            cat pipeline-validation/unit-tests/test-output.txt >> pipeline-validation/report.html
            echo "</pre>" >> pipeline-validation/report.html
          else
            echo "<p>Unit test results not available</p>" >> pipeline-validation/report.html
          fi
          
          echo "<h2>Firmware Status</h2>" >> pipeline-validation/report.html
          if [ -f "pipeline-validation/firmware/firmware.hex" ]; then
            echo "<p>✅ Firmware build successful</p>" >> pipeline-validation/report.html
            echo "<p>Firmware hex file size: $HEX_SIZE bytes</p>" >> pipeline-validation/report.html
          else
            echo "<p>❌ Firmware build failed or artifacts not available</p>" >> pipeline-validation/report.html
          fi
          
          echo "<h2>Overall Status</h2>" >> pipeline-validation/report.html
          if [ "$OVERALL_STATUS" == "success" ]; then
            echo "<p>✅ Pipeline completed successfully</p>" >> pipeline-validation/report.html
          else
            echo "<p>❌ Pipeline has issues that need attention</p>" >> pipeline-validation/report.html
          fi
          
          echo "<h2>Code Quality</h2>" >> pipeline-validation/report.html
          if [ -f "pipeline-validation/code-quality/analysis-report.md" ]; then
            echo "<p>See the <a href='../code-quality-reports/analysis-report.md'>Code Quality Report</a> for details.</p>" >> pipeline-validation/report.html
          else
            echo "<p>Code quality report not available</p>" >> pipeline-validation/report.html
          fi
          
          echo "<h2>Code Coverage</h2>" >> pipeline-validation/report.html
          if [ -f "pipeline-validation/coverage/coverage-report.md" ]; then
            echo "<p>See the <a href='../coverage-reports/coverage-report.md'>Coverage Report</a> for details.</p>" >> pipeline-validation/report.html
            total_coverage=$(grep "Lines" pipeline-validation/coverage/coverage-report.md | awk '{print $3}')
            echo "<p>Overall line coverage: $total_coverage</p>" >> pipeline-validation/report.html
          else
            echo "<p>Coverage report not available</p>" >> pipeline-validation/report.html
          fi
          
          echo "</body></html>" >> pipeline-validation/report.html
          
          # Exit with status based on validation results
          if [ "$OVERALL_STATUS" == "success" ]; then
            echo "Pipeline validation passed!"
            exit 0
          else
            echo "Pipeline validation found issues."
            exit 1
          fi
      
      - name: Archive validation report
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: pipeline-validation-report
          path: pipeline-validation/ 