#!/bin/bash

# Script to program ATtiny85 using Arduino R4 WiFi as ISP

echo "=================================================="
echo "  ATtiny85 Programming Script"
echo "=================================================="
echo ""
echo "This script will program your ATtiny85 using Arduino R4 WiFi."
echo ""

# Check prerequisites
echo "Checking prerequisites..."

# Check if <PERSON><PERSON><PERSON><PERSON> has ArduinoISP sketch loaded
echo ""
echo "IMPORTANT: Before continuing, make sure:"
echo "1. The Arduino R4 WiFi has the ArduinoISP sketch loaded"
echo "2. The ATtiny85 is connected to the Arduino as per the diagram:"
echo "   - Arduino D13 -> ATtiny pin 7 (SCK)"
echo "   - Arduino D12 -> ATtiny pin 6 (MISO)"
echo "   - Arduino D11 -> ATtiny pin 5 (MOSI)"
echo "   - Arduino D10 -> ATtiny pin 1 (RESET)"
echo "   - Arduino 5V  -> ATtiny pin 8 (VCC)"
echo "   - Arduino GND -> ATtiny pin 4 (GND)"
echo ""

# Get confirmation
read -p "Is everything connected correctly? (y/n): " confirm
if [[ $confirm != "y" && $confirm != "Y" ]]; then
    echo "Please connect everything properly and try again."
    exit 1
fi

# Set the path to platformio
PLATFORMIO="$HOME/Library/Python/3.9/bin/platformio"

# Check if platformio exists at that location
if [ ! -f "$PLATFORMIO" ]; then
    echo "PlatformIO not found at $PLATFORMIO!"
    echo "Please install PlatformIO CLI with: pip3 install platformio"
    exit 1
fi

echo "PlatformIO found at: $PLATFORMIO"

# Verify Arduino R4 WiFi port
PORT="/dev/tty.usbmodemF412FA9B73C82"
if [ ! -e "$PORT" ]; then
    echo "Arduino port not found at $PORT"
    echo "Please check if Arduino is connected and update the script with the correct port."
    echo "Available ports:"
    ls /dev/tty.*
    exit 1
fi

echo "Arduino R4 WiFi found at $PORT"

# Proceed with upload
echo ""
echo "Starting ATtiny85 programming with Arduino as ISP..."
echo ""

# Build and upload firmware
echo "Building firmware..."
"$PLATFORMIO" run -e attiny85

if [ $? -ne 0 ]; then
    echo "Build failed! Please check the errors above."
    exit 1
fi

echo "Uploading firmware to ATtiny85..."
"$PLATFORMIO" run -e attiny85 -t upload

if [ $? -ne 0 ]; then
    echo "Upload failed! Please check the errors above."
    echo "Make sure the Arduino has the ArduinoISP sketch loaded and connections are correct."
    exit 1
fi

# Set fuses (optional)
echo ""
read -p "Do you want to set the fuses on the ATtiny85? (y/n): " confirm_fuses
if [[ $confirm_fuses == "y" || $confirm_fuses == "Y" ]]; then
    echo "Setting fuses..."
    "$PLATFORMIO" run -e attiny85 -t fuses
fi

echo ""
echo "=================================================="
echo "  Programming completed!"
echo "=================================================="
echo ""
echo "If there were no errors, your ATtiny85 has been"
echo "successfully programmed with the control system firmware." 