"""
Scripts to configure the native test environment for PlatformIO.
This script ensures the mock Arduino.h is found and compiles with native testing.
"""

import os

def adjust_cpp_flags(env):
    """Add necessary flags for C++ testing in native environment"""
    env.Append(CPPDEFINES=[
        "NATIVE_TEST",
        "UNIT_TEST",
        "UNITY_INCLUDE_CONFIG_H"
    ])
    
    # Make sure we prioritize our mock Arduino.h
    test_dir = os.path.join(env.get("PROJECT_DIR", "."), "test")
    mock_dir = os.path.join(test_dir, "mocks")
    env.Prepend(CPPPATH=[mock_dir])

# Hook for environment setup    
def process_test_options(env):
    """Process test options for native environment"""
    if env.get("PIOENV") == "native":
        print("Setting up native test environment...")
        adjust_cpp_flags(env)
        
        # Add Arduino.cpp mock to be compiled
        env.Append(
            CPPPATH=[os.path.join("$PROJECT_DIR", "test", "mocks")],
        )
        
        # Force mock files to be compiled in the test build
        arduino_mock = os.path.join("$PROJECT_DIR", "test", "mocks", "Arduino.cpp")
        air_control_mock = os.path.join("$PROJECT_DIR", "test", "mocks", "air_control_mock.cpp")
        
        env.Append(PIOBUILDFILES=[
            env.File(arduino_mock),
            env.File(air_control_mock)
        ])
        
        print("Native test environment configured.")

# Register with PlatformIO
try:
    Import("env")
    process_test_options(env)
except ImportError:
    # Not running within PlatformIO environment
    pass

# When run directly, print info
if __name__ == "__main__":
    print("Native test setup script")
    print("This script is intended to be used with PlatformIO.")
    print("Add it to your platformio.ini:")
    print("  extra_scripts = pre:scripts/native_test_setup.py") 