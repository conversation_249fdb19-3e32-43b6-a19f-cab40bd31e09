#!/usr/bin/env node

/**
 * Pipeline History Dashboard
 * 
 * A web dashboard to visualize CI/CD pipeline run data from Firestore.
 * 
 * Usage:
 *   node pipeline_dashboard.js [port]
 *   
 * Default port is 3000 if not specified.
 */

const express = require('express');
const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  getDocs, 
  query, 
  orderBy, 
  limit 
} = require('firebase/firestore');

// Configuration
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Initialize Express
const server = express();
const port = process.argv[2] || 3000;

// Serve static files
server.use(express.static('public'));

// Set view engine
server.set('view engine', 'ejs');
server.set('views', './views');

// Create views directory if it doesn't exist
const fs = require('fs');
const path = require('path');
const viewsDir = path.join(__dirname, 'views');
const publicDir = path.join(__dirname, 'public');

if (!fs.existsSync(viewsDir)) {
  fs.mkdirSync(viewsDir, { recursive: true });
}

if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Create EJS template for dashboard
const dashboardTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CI/CD Pipeline Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/styles.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <nav class="navbar navbar-dark bg-dark">
    <div class="container-fluid">
      <span class="navbar-brand mb-0 h1">ATtiny85 Control System - CI/CD Pipeline Dashboard</span>
    </div>
  </nav>

  <div class="container mt-4">
    <div class="row">
      <div class="col-md-6">
        <div class="card mb-4">
          <div class="card-header">
            Pipeline Success Rate
          </div>
          <div class="card-body">
            <canvas id="successRateChart"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card mb-4">
          <div class="card-header">
            Job Status
          </div>
          <div class="card-body">
            <canvas id="jobStatusChart"></canvas>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card mb-4">
      <div class="card-header">
        Recent Pipeline Runs
      </div>
      <div class="card-body">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Run #</th>
              <th>Status</th>
              <th>Branch</th>
              <th>Commit</th>
              <th>Date</th>
              <th>Duration</th>
              <th>Success Rate</th>
              <th>Details</th>
            </tr>
          </thead>
          <tbody>
            <% runs.forEach(run => { %>
              <tr class="<%= getRunRowClass(run) %>">
                <td><%= run.run_number %></td>
                <td>
                  <span class="badge <%= getStatusBadgeClass(run.conclusion) %>">
                    <%= run.conclusion || run.status %>
                  </span>
                </td>
                <td><%= run.branch %></td>
                <td title="<%= run.commit.message %>">
                  <%= run.commit.id.substring(0, 7) %>
                </td>
                <td><%= formatDate(run.created_at) %></td>
                <td><%= formatDuration(run) %></td>
                <td>
                  <div class="progress">
                    <div class="progress-bar <%= getProgressBarClass(run.stats.success_rate) %>" 
                         role="progressbar" 
                         style="width: <%= run.stats.success_rate %>%"
                         aria-valuenow="<%= run.stats.success_rate %>" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                      <%= run.stats.success_rate.toFixed(0) %>%
                    </div>
                  </div>
                </td>
                <td>
                  <a href="/runs/<%= run.id %>" class="btn btn-sm btn-primary">Details</a>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script>
    // Chart data
    const successRateData = {
      labels: <%- JSON.stringify(runs.map(r => '#' + r.run_number)) %>,
      datasets: [{
        label: 'Success Rate',
        data: <%- JSON.stringify(runs.map(r => r.stats.success_rate)) %>,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
        fill: false
      }]
    };

    const jobStatusData = {
      labels: ['Successful', 'Failed', 'Skipped'],
      datasets: [{
        label: 'Job Status',
        data: [
          <%= runs.reduce((sum, r) => sum + r.stats.successful_jobs, 0) %>,
          <%= runs.reduce((sum, r) => sum + r.stats.failed_jobs, 0) %>,
          <%= runs.reduce((sum, r) => sum + r.stats.skipped_jobs, 0) %>
        ],
        backgroundColor: [
          'rgba(75, 192, 192, 0.5)',
          'rgba(255, 99, 132, 0.5)',
          'rgba(201, 203, 207, 0.5)'
        ],
        borderColor: [
          'rgb(75, 192, 192)',
          'rgb(255, 99, 132)',
          'rgb(201, 203, 207)'
        ],
        borderWidth: 1
      }]
    };

    // Initialize charts
    document.addEventListener('DOMContentLoaded', function() {
      // Success rate chart
      const successRateCtx = document.getElementById('successRateChart').getContext('2d');
      new Chart(successRateCtx, {
        type: 'line',
        data: successRateData,
        options: {
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            }
          },
          plugins: {
            title: {
              display: true,
              text: 'Pipeline Success Rate (%)'
            }
          }
        }
      });

      // Job status chart
      const jobStatusCtx = document.getElementById('jobStatusChart').getContext('2d');
      new Chart(jobStatusCtx, {
        type: 'pie',
        data: jobStatusData,
        options: {
          plugins: {
            title: {
              display: true,
              text: 'Job Status Distribution'
            }
          }
        }
      });
    });
  </script>
</body>
</html>
`;

// Create EJS template for run details
const runDetailsTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Run #<%= run.run_number %> Details</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/styles.css">
</head>
<body>
  <nav class="navbar navbar-dark bg-dark">
    <div class="container-fluid">
      <span class="navbar-brand mb-0 h1">ATtiny85 Control System - Pipeline Run #<%= run.run_number %></span>
      <a href="/" class="btn btn-outline-light">← Back to Dashboard</a>
    </div>
  </nav>

  <div class="container mt-4">
    <div class="card mb-4">
      <div class="card-header">
        Run Information
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <dl class="row">
              <dt class="col-sm-4">Run Number</dt>
              <dd class="col-sm-8">#<%= run.run_number %></dd>
              
              <dt class="col-sm-4">Status</dt>
              <dd class="col-sm-8">
                <span class="badge <%= getStatusBadgeClass(run.conclusion) %>">
                  <%= run.conclusion || run.status %>
                </span>
              </dd>
              
              <dt class="col-sm-4">Branch</dt>
              <dd class="col-sm-8"><%= run.branch %></dd>
              
              <dt class="col-sm-4">Created</dt>
              <dd class="col-sm-8"><%= formatDate(run.created_at) %></dd>
              
              <dt class="col-sm-4">Duration</dt>
              <dd class="col-sm-8"><%= formatDuration(run) %></dd>
            </dl>
          </div>
          <div class="col-md-6">
            <dl class="row">
              <dt class="col-sm-4">Commit</dt>
              <dd class="col-sm-8"><%= run.commit.id %></dd>
              
              <dt class="col-sm-4">Author</dt>
              <dd class="col-sm-8"><%= run.commit.author %></dd>
              
              <dt class="col-sm-4">Message</dt>
              <dd class="col-sm-8"><%= run.commit.message %></dd>
              
              <dt class="col-sm-4">Success Rate</dt>
              <dd class="col-sm-8">
                <div class="progress">
                  <div class="progress-bar <%= getProgressBarClass(run.stats.success_rate) %>" 
                       role="progressbar" 
                       style="width: <%= run.stats.success_rate %>%"
                       aria-valuenow="<%= run.stats.success_rate %>" 
                       aria-valuemin="0" 
                       aria-valuemax="100">
                    <%= run.stats.success_rate.toFixed(0) %>%
                  </div>
                </div>
              </dd>
              
              <dt class="col-sm-4">GitHub</dt>
              <dd class="col-sm-8">
                <a href="<%= run.url %>" target="_blank" class="btn btn-sm btn-secondary">
                  View on GitHub
                </a>
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card mb-4">
      <div class="card-header">
        Jobs
      </div>
      <div class="card-body">
        <div class="accordion" id="jobsAccordion">
          <% run.jobs.forEach((job, index) => { %>
            <div class="accordion-item">
              <h2 class="accordion-header">
                <button class="accordion-button <%= job.conclusion === 'failure' ? '' : 'collapsed' %>" 
                        type="button" 
                        data-bs-toggle="collapse" 
                        data-bs-target="#collapse<%= index %>" 
                        aria-expanded="<%= job.conclusion === 'failure' %>" 
                        aria-controls="collapse<%= index %>">
                  <span class="badge <%= getStatusBadgeClass(job.conclusion) %> me-2">
                    <%= job.conclusion || job.status %>
                  </span>
                  <%= job.name %>
                  <% if (job.duration) { %>
                    <small class="ms-2 text-muted">(<%= formatJobDuration(job.duration) %>)</small>
                  <% } %>
                </button>
              </h2>
              <div id="collapse<%= index %>" 
                   class="accordion-collapse collapse <%= job.conclusion === 'failure' ? 'show' : '' %>" 
                   data-bs-parent="#jobsAccordion">
                <div class="accordion-body">
                  <% if (job.errors && job.errors.length > 0) { %>
                    <h5 class="text-danger">Errors</h5>
                    <ul class="list-group mb-3">
                      <% job.errors.forEach(error => { %>
                        <li class="list-group-item list-group-item-danger">
                          <strong>Step #<%= error.number %>:</strong> <%= error.name %>
                        </li>
                      <% }); %>
                    </ul>
                  <% } %>
                  
                  <dl class="row">
                    <dt class="col-sm-3">Started</dt>
                    <dd class="col-sm-9"><%= job.started_at ? formatDate(job.started_at) : 'N/A' %></dd>
                    
                    <dt class="col-sm-3">Completed</dt>
                    <dd class="col-sm-9"><%= job.completed_at ? formatDate(job.completed_at) : 'N/A' %></dd>
                    
                    <dt class="col-sm-3">Status</dt>
                    <dd class="col-sm-9"><%= job.status %></dd>
                    
                    <dt class="col-sm-3">Conclusion</dt>
                    <dd class="col-sm-9"><%= job.conclusion || 'Pending' %></dd>
                  </dl>
                  
                  <a href="<%= job.url %>" target="_blank" class="btn btn-sm btn-primary">
                    View Job on GitHub
                  </a>
                </div>
              </div>
            </div>
          <% }); %>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
`;

// Create CSS file
const cssContent = `
.bg-success-subtle {
  background-color: rgba(25, 135, 84, 0.15);
}
.bg-danger-subtle {
  background-color: rgba(220, 53, 69, 0.15);
}
.bg-warning-subtle {
  background-color: rgba(255, 193, 7, 0.15);
}
.badge.bg-pending {
  background-color: #6c757d;
}
`;

// Write template and CSS files
fs.writeFileSync(path.join(viewsDir, 'dashboard.ejs'), dashboardTemplate);
fs.writeFileSync(path.join(viewsDir, 'run-details.ejs'), runDetailsTemplate);
fs.writeFileSync(path.join(publicDir, 'styles.css'), cssContent);

// Helper functions
function getStatusBadgeClass(status) {
  if (!status) return 'bg-pending';
  switch (status.toLowerCase()) {
    case 'success': return 'bg-success';
    case 'failure': return 'bg-danger';
    case 'cancelled': return 'bg-warning';
    case 'skipped': return 'bg-secondary';
    default: return 'bg-info';
  }
}

function getProgressBarClass(rate) {
  if (rate >= 90) return 'bg-success';
  if (rate >= 60) return 'bg-warning';
  return 'bg-danger';
}

function getRunRowClass(run) {
  const status = run.conclusion || run.status;
  if (!status) return '';
  switch (status.toLowerCase()) {
    case 'success': return 'bg-success-subtle';
    case 'failure': return 'bg-danger-subtle';
    case 'cancelled': return 'bg-warning-subtle';
    default: return '';
  }
}

function formatDate(date) {
  if (!date) return 'N/A';
  const d = new Date(date);
  return d.toLocaleString();
}

function formatDuration(run) {
  if (!run.updated_at || !run.created_at) return 'N/A';
  
  const start = new Date(run.created_at);
  const end = new Date(run.updated_at);
  const durationMs = end - start;
  
  return formatJobDuration(durationMs / 1000);
}

function formatJobDuration(seconds) {
  if (!seconds) return 'N/A';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (minutes < 1) {
    return `${remainingSeconds}s`;
  }
  
  return `${minutes}m ${remainingSeconds}s`;
}

// Routes
server.get('/', async (req, res) => {
  try {
    // Get recent runs
    const runsQuery = query(
      collection(db, 'pipeline-runs'),
      orderBy('created_at', 'desc'),
      limit(20)
    );
    
    const querySnapshot = await getDocs(runsQuery);
    const runs = [];
    
    querySnapshot.forEach(doc => {
      runs.push(doc.data());
    });
    
    res.render('dashboard', {
      runs,
      getStatusBadgeClass,
      getProgressBarClass,
      getRunRowClass,
      formatDate,
      formatDuration
    });
  } catch (error) {
    console.error('Error fetching pipeline runs:', error);
    res.status(500).send('Error fetching pipeline runs: ' + error.message);
  }
});

server.get('/runs/:id', async (req, res) => {
  try {
    // Get all runs (to find the specific one)
    const runsQuery = query(
      collection(db, 'pipeline-runs')
    );
    
    const querySnapshot = await getDocs(runsQuery);
    let runData = null;
    
    querySnapshot.forEach(doc => {
      if (doc.id === req.params.id) {
        runData = doc.data();
      }
    });
    
    if (!runData) {
      return res.status(404).send('Run not found');
    }
    
    res.render('run-details', {
      run: runData,
      getStatusBadgeClass,
      getProgressBarClass,
      formatDate,
      formatDuration,
      formatJobDuration
    });
  } catch (error) {
    console.error('Error fetching run details:', error);
    res.status(500).send('Error fetching run details: ' + error.message);
  }
});

// Start server
server.listen(port, () => {
  console.log(`Pipeline dashboard running at http://localhost:${port}`);
}); 