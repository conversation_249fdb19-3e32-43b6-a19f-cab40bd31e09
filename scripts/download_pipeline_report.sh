#!/bin/bash
# Script to download pipeline reports from GitHub Actions

set -e

# Configuration
REPO="Cannasol-Tech/attiny-control"
OUTPUT_DIR="pipeline-reports"

# Create output directory
mkdir -p "${OUTPUT_DIR}"
echo "📁 Created output directory: ${OUTPUT_DIR}"

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) is not installed."
    echo "Please install it from: https://cli.github.com/"
    exit 1
fi

# Check login status
if ! gh auth status &> /dev/null; then
    echo "❌ You are not logged in to GitHub CLI."
    echo "Please run: gh auth login"
    exit 1
fi

# Get latest workflow run ID (or specific one if provided)
RUN_ID="$1"
if [ -z "$RUN_ID" ]; then
    echo "🔍 Finding latest workflow run..."
    RUN_ID=$(gh run list --repo "$REPO" --workflow "CI/CD Pipeline" --limit 1 --json databaseId --jq '.[0].databaseId')
fi

if [ -z "$RUN_ID" ]; then
    echo "❌ No workflow runs found."
    exit 1
fi

echo "📊 Downloading reports for workflow run: $RUN_ID"

# Download all artifacts from the run
echo "⬇️ Downloading artifacts..."
gh run download "$RUN_ID" --repo "$REPO" --dir "${OUTPUT_DIR}/run-${RUN_ID}"

# If the pipeline-validation-report artifact exists, it contains the summary
VALIDATION_REPORT="${OUTPUT_DIR}/run-${RUN_ID}/pipeline-validation-report"
if [ -d "$VALIDATION_REPORT" ]; then
    echo "✅ Found validation report."
    
    # Open the HTML report if it exists
    if [ -f "${VALIDATION_REPORT}/report.html" ]; then
        echo "📄 Pipeline validation report is at: ${VALIDATION_REPORT}/report.html"
        echo "🌐 Opening report in browser..."
        open "${VALIDATION_REPORT}/report.html" || echo "Could not open report automatically. Please open it manually."
    fi
else
    echo "⚠️ No validation report found. Looking for individual job reports..."
    
    # List downloaded artifacts
    find "${OUTPUT_DIR}/run-${RUN_ID}" -type d -mindepth 1 -maxdepth 1 | while read -r artifact; do
        echo "📦 Found artifact: $(basename "$artifact")"
        
        # Look for HTML or text reports
        REPORTS=$(find "$artifact" -name "*.html" -o -name "*.txt" -o -name "*.md" | sort)
        if [ -n "$REPORTS" ]; then
            echo "   📄 Reports in this artifact:"
            echo "$REPORTS" | sed 's/^/   - /'
        fi
    done
fi

echo "✨ Done! All available reports downloaded to: ${OUTPUT_DIR}/run-${RUN_ID}"
echo "Review the reports to identify the pipeline failures." 