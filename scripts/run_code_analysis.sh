#!/bin/bash

# Static Code Analysis Script for ATtiny85 Control System
# This script runs various code quality checks on the codebase

echo "========================================"
echo "Running Code Quality Analysis"
echo "========================================"

# Navigate to the project root directory
PROJECT_ROOT=$(realpath $(dirname $(dirname $0)))
cd $PROJECT_ROOT

# Create output directory
mkdir -p analysis-reports

# Check if cppcheck is installed
if ! command -v cppcheck &> /dev/null; then
    echo "❌ cppcheck is not installed. Please install it first."
    echo "   On Ubuntu/Debian: sudo apt-get install cppcheck"
    echo "   On macOS: brew install cppcheck"
    exit 1
fi

# Check if clang-format is installed
if ! command -v clang-format &> /dev/null; then
    echo "❌ clang-format is not installed. Please install it first."
    echo "   On Ubuntu/Debian: sudo apt-get install clang-format"
    echo "   On macOS: brew install clang-format"
    exit 1
fi

# Run cppcheck static analysis
echo "Running cppcheck static analysis..."
cppcheck --enable=all --std=c++11 --suppress=missingIncludeSystem \
    --error-exitcode=1 src/ test/unit/ test/integration/ \
    2>&1 | tee analysis-reports/cppcheck-results.txt

if [ $? -eq 0 ]; then
    echo "✅ cppcheck analysis passed."
    CPPCHECK_STATUS="passed"
else
    echo "❌ cppcheck analysis found issues."
    CPPCHECK_STATUS="failed"
fi

# Verify code formatting
echo "Verifying code formatting..."
FORMAT_ISSUES=0
FORMATTED_FILES=""

# Find all C++ files
CPP_FILES=$(find src test -name "*.cpp" -o -name "*.h" -type f)

for file in $CPP_FILES; do
    # Check if the file is correctly formatted
    clang-format --dry-run --Werror "$file" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "❌ $file is not properly formatted."
        FORMAT_ISSUES=$((FORMAT_ISSUES + 1))
        FORMATTED_FILES="$FORMATTED_FILES\n- $file"
    fi
done

if [ $FORMAT_ISSUES -eq 0 ]; then
    echo "✅ Code formatting check passed."
    FORMAT_STATUS="passed"
else
    echo "❌ $FORMAT_ISSUES files have formatting issues."
    FORMAT_STATUS="failed"
fi

# Check header files for include guards
echo "Checking header files for include guards..."
GUARD_ISSUES=0
GUARD_FILES=""

# Find all header files
HEADER_FILES=$(find src test -name "*.h" -type f)

for file in $HEADER_FILES; do
    # Extract filename for include guard
    filename=$(basename "$file" .h)
    dirname=$(dirname "$file" | tr / _ | tr '[:lower:]' '[:upper:]')
    expected_guard="${dirname}_${filename}_H"
    
    # Check for expected include guard
    if ! grep -q "#ifndef ${filename}_H" "$file" && ! grep -q "#ifndef ${expected_guard}" "$file"; then
        echo "❌ $file is missing proper include guards."
        GUARD_ISSUES=$((GUARD_ISSUES + 1))
        GUARD_FILES="$GUARD_FILES\n- $file"
    fi
done

if [ $GUARD_ISSUES -eq 0 ]; then
    echo "✅ Include guard check passed."
    GUARD_STATUS="passed"
else
    echo "❌ $GUARD_ISSUES files have include guard issues."
    GUARD_STATUS="failed"
fi

# Generate markdown report
echo "Generating analysis report..."
cat > analysis-reports/analysis-report.md << EOF
# Code Quality Analysis Report

## Overall Status

| Check | Status |
|-------|--------|
| Static Analysis | $CPPCHECK_STATUS |
| Code Formatting | $FORMAT_STATUS |
| Include Guards | $GUARD_STATUS |

## Static Analysis Details

\`\`\`
$(cat analysis-reports/cppcheck-results.txt)
\`\`\`

EOF

if [ $FORMAT_ISSUES -gt 0 ]; then
    cat >> analysis-reports/analysis-report.md << EOF
## Formatting Issues

The following files have formatting issues:
$FORMATTED_FILES

To fix formatting issues, run:
\`\`\`
clang-format -i <filename>
\`\`\`

EOF
fi

if [ $GUARD_ISSUES -gt 0 ]; then
    cat >> analysis-reports/analysis-report.md << EOF
## Include Guard Issues

The following files have include guard issues:
$GUARD_FILES

Each header file should have proper include guards:

\`\`\`cpp
#ifndef FILENAME_H
#define FILENAME_H

// File contents

#endif // FILENAME_H
\`\`\`

EOF
fi

echo "Analysis report generated at analysis-reports/analysis-report.md"

# Exit with error if any checks failed
if [ "$CPPCHECK_STATUS" = "failed" ] || [ "$FORMAT_STATUS" = "failed" ] || [ "$GUARD_STATUS" = "failed" ]; then
    echo "❌ Some code quality checks failed. Please review the analysis report."
    exit 1
else
    echo "✅ All code quality checks passed!"
    exit 0
fi 