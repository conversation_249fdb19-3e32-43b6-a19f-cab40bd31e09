"""
SimulAVR integration script for PlatformIO.
This script configures PlatformIO to use SimulAVR for emulation-based testing.
"""

import os
import re
import sys
import time
import subprocess
import tempfile
from platformio.managers.platform import PlatformBase

class SimulavrRunner:
    """Class to manage SimulAVR execution for testing"""
    
    def __init__(self, target_elf, device="attiny85", verbosity=1):
        self.target_elf = target_elf
        self.device = device
        self.verbosity = verbosity
        self.process = None
        self.stdout = None
        self.stderr = None
        self.vcd_file = None
        self.gdb_enabled = False
        self.exit_code = None
    
    def configure_vcd_trace(self, vcd_file=None):
        """Set up VCD tracing for waveform analysis"""
        if vcd_file is None:
            # Create a temporary VCD file
            fd, vcd_file = tempfile.mkstemp(suffix='.vcd')
            os.close(fd)
        
        self.vcd_file = vcd_file
        return self
    
    def enable_gdb(self, port=1212):
        """Enable GDB debugging interface"""
        self.gdb_enabled = True
        self.gdb_port = port
        return self
    
    def setup_command(self):
        """Build the SimulAVR command with all options"""
        cmd = ["simulavr", "-d", self.device, "-f", self.target_elf]
        
        # Add verbosity
        if self.verbosity > 1:
            cmd.append("-v")
        
        # Add VCD tracing if configured
        if self.vcd_file:
            cmd.extend(["-W", f"0x22,{self.vcd_file}"])
        
        # Add GDB support if enabled
        if self.gdb_enabled:
            cmd.extend(["-g", "-p", str(self.gdb_port)])
        
        # Set terminal output for Unity tests
        cmd.extend(["-T", "exit"])
        
        return cmd
    
    def run(self, timeout=60):
        """Run SimulAVR with the configured options"""
        cmd = self.setup_command()
        print(f"Running SimulAVR with command: {' '.join(cmd)}")
        
        try:
            # Start the process
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # Wait for completion with timeout
            self.stdout, self.stderr = self.process.communicate(timeout=timeout)
            self.exit_code = self.process.returncode
            
            # Process and display the output
            self._process_output()
            
            return self.exit_code == 0
            
        except subprocess.TimeoutExpired:
            # Kill the process if it times out
            self.process.kill()
            print(f"SimulAVR execution timed out after {timeout} seconds.")
            return False
            
        except Exception as e:
            print(f"Error running SimulAVR: {e}")
            return False
    
    def _process_output(self):
        """Process SimulAVR and test output"""
        if self.stdout:
            # Split lines for better readability
            lines = self.stdout.splitlines()
            
            # Extract Unity test results
            test_results = []
            in_test_section = False
            
            for line in lines:
                # Check for Unity test output markers
                if "TEST(Unity" in line:
                    in_test_section = True
                    test_results.append(line)
                elif in_test_section and line.strip():
                    test_results.append(line)
                elif in_test_section and not line.strip():
                    in_test_section = False
                
                # Always print SimulAVR system messages
                if "SimulAVR" in line or "Error" in line:
                    print(line)
            
            # Print summary of test results if any were found
            if test_results:
                print("\n===== TEST RESULTS =====")
                for result in test_results:
                    print(result)
                print("=======================\n")
        
        # Always print errors
        if self.stderr:
            print("STDERR:", self.stderr)
    
    def parse_test_results(self):
        """Extract and parse Unity test results"""
        if not self.stdout:
            return None
        
        # Regular expressions to extract test information
        test_pattern = r"TEST\((\w+),\s*(\w+)\)\s*(\w+)"
        
        # Find all test results
        results = re.findall(test_pattern, self.stdout)
        
        # Process results
        test_summary = {
            "total": len(results),
            "passed": 0,
            "failed": 0,
            "tests": []
        }
        
        for group, test, status in results:
            success = status.upper() == "PASS"
            test_summary["tests"].append({
                "group": group,
                "name": test,
                "success": success
            })
            
            if success:
                test_summary["passed"] += 1
            else:
                test_summary["failed"] += 1
        
        return test_summary

def create_junit_report(test_summary, output_file):
    """Create a JUnit XML report from test results"""
    if not test_summary:
        return
    
    # Basic XML structure
    xml_lines = [
        '<?xml version="1.0" encoding="UTF-8"?>',
        '<testsuites>',
        f'  <testsuite name="SimulAVR" tests="{test_summary["total"]}" failures="{test_summary["failed"]}" errors="0">'
    ]
    
    # Add individual test cases
    for test in test_summary["tests"]:
        if test["success"]:
            xml_lines.append(f'    <testcase name="{test["name"]}" classname="{test["group"]}"/>')
        else:
            xml_lines.append(f'    <testcase name="{test["name"]}" classname="{test["group"]}">')
            xml_lines.append(f'      <failure message="Test failed">Test {test["name"]} failed</failure>')
            xml_lines.append('    </testcase>')
    
    # Close XML structure
    xml_lines.append('  </testsuite>')
    xml_lines.append('</testsuites>')
    
    # Write to file
    with open(output_file, 'w') as f:
        f.write('\n'.join(xml_lines))
    
    print(f"JUnit report written to {output_file}")

def on_test(name, target, source, env):
    """Run integration tests with SimulAVR"""
    # Only run for integration tests with the emulator environment
    if not env.get("PIOENV") == "emulator":
        return None
    
    print("Setting up SimulAVR integration test...")
    
    # Get the path to the compiled binary
    target_elf = str(target[0])
    
    # Set up VCD trace file
    vcd_file = os.path.join(os.path.dirname(target_elf), "sim_trace.vcd")
    
    # Create report directory
    report_dir = os.path.join(env.get("PROJECT_DIR", "."), "test-reports")
    os.makedirs(report_dir, exist_ok=True)
    
    # JUnit report file
    junit_file = os.path.join(report_dir, "integration-tests.xml")
    
    # Run SimulAVR with the compiled tests
    runner = SimulavrRunner(target_elf, "attiny85", verbosity=2)
    runner.configure_vcd_trace(vcd_file)
    success = runner.run(timeout=120)
    
    # Create test report
    test_summary = runner.parse_test_results()
    if test_summary:
        create_junit_report(test_summary, junit_file)
    
    if not success:
        raise Exception("SimulAVR integration test failed")
    
    # Calculate return code based on test results
    if test_summary and test_summary["failed"] > 0:
        return test_summary["failed"]
    
    return 0

# Register the callback with PlatformIO
try:
    Import("env")
    
    # Add the SimulAVR test runner when in the emulator environment
    if env.get("PIOENV") == "emulator":
        print("Registering SimulAVR integration for emulator environment")
        
        # Register the callback to run after compilation
        env.AddPostAction("$BUILD_DIR/${PROGNAME}.elf", on_test)
        
        # Add necessary flags for emulation mode
        env.Append(CPPDEFINES=[
            "EMULATION_MODE=1",
            "TEST_MODE=1"
        ])
        
        # Add defines for test flags based on the test files being built
        for source in env.get("PIOSRCFILES", []):
            if "test_system_integration" in source:
                env.Append(CPPDEFINES=["TEST_SYSTEM_INTEGRATION=1"])
            elif "test_integration_basic" in source:
                env.Append(CPPDEFINES=["TEST_INTEGRATION_BASIC=1"])
            elif "test_" in source:
                test_name = os.path.basename(source).replace(".cpp", "").upper()
                env.Append(CPPDEFINES=[f"{test_name}=1"])
        
except ImportError:
    # Script is not running in the PlatformIO build environment
    pass

# Allow running this script directly for testing
if __name__ == "__main__":
    if len(sys.argv) > 1:
        target_elf = sys.argv[1]
        runner = SimulavrRunner(target_elf, verbosity=2)
        runner.configure_vcd_trace()
        success = runner.run()
        sys.exit(0 if success else 1)
    else:
        print("Usage: python simulavr_script.py [target_elf]")
        sys.exit(1) 