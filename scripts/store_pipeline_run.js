#!/usr/bin/env node

/**
 * Pipeline Run History Storage Script
 * 
 * This script fetches GitHub Actions workflow run data and stores it in Firestore
 * allowing for historical tracking, analysis, and visualization of CI/CD pipeline runs.
 * 
 * Usage:
 *   node store_pipeline_run.js [run_id]
 *   
 * If run_id is omitted, the latest run will be fetched and stored.
 */

// Firebase imports
const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  doc,
  setDoc, 
  updateDoc,
  serverTimestamp 
} = require('firebase/firestore');

// HTTP client for GitHub API calls
const axios = require('axios');

// Configuration
const REPO = 'Cannasol-Tech/attiny-control';
const GITHUB_API = 'https://api.github.com';
const WORKFLOW_NAME = 'CI/CD Pipeline';

// Firebase configuration - replace with your own Firebase project details
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID
};

// GitHub token
const GITHUB_TOKEN = process.env.GITHUB_TOKEN;

if (!GITHUB_TOKEN) {
  console.error('Error: GITHUB_TOKEN environment variable is required');
  process.exit(1);
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// API client setup
const githubApi = axios.create({
  baseURL: GITHUB_API,
  headers: {
    Authorization: `token ${GITHUB_TOKEN}`,
    Accept: 'application/vnd.github.v3+json'
  }
});

// Get command line arguments
const runId = process.argv[2];

/**
 * Fetch workflow run details
 */
async function getWorkflowRun(id) {
  if (id) {
    // Fetch specific run
    const response = await githubApi.get(`/repos/${REPO}/actions/runs/${id}`);
    return response.data;
  } else {
    // Fetch latest run of the CI/CD workflow
    const response = await githubApi.get(`/repos/${REPO}/actions/workflows`);
    const workflow = response.data.workflows.find(w => w.name === WORKFLOW_NAME);
    
    if (!workflow) {
      throw new Error(`Workflow "${WORKFLOW_NAME}" not found`);
    }
    
    const runsResponse = await githubApi.get(`/repos/${REPO}/actions/workflows/${workflow.id}/runs?per_page=1`);
    if (runsResponse.data.workflow_runs.length === 0) {
      throw new Error('No workflow runs found');
    }
    
    return runsResponse.data.workflow_runs[0];
  }
}

/**
 * Fetch jobs for a workflow run
 */
async function getWorkflowJobs(runId) {
  const response = await githubApi.get(`/repos/${REPO}/actions/runs/${runId}/jobs`);
  return response.data.jobs;
}

/**
 * Extract error information from job steps
 */
function extractErrorInfo(job) {
  const failedSteps = job.steps.filter(step => step.conclusion === 'failure');
  return failedSteps.map(step => ({
    name: step.name,
    number: step.number,
    status: step.status,
    conclusion: step.conclusion
  }));
}

/**
 * Store workflow run data in Firestore
 */
async function storeWorkflowRun(run, jobs) {
  const runRef = doc(collection(db, 'pipeline-runs'), run.id.toString());
  
  const runData = {
    id: run.id,
    repo: REPO,
    workflow: {
      id: run.workflow_id,
      name: WORKFLOW_NAME
    },
    status: run.status,
    conclusion: run.conclusion,
    branch: run.head_branch,
    commit: {
      id: run.head_sha,
      message: run.head_commit?.message || 'Unknown',
      author: run.head_commit?.author?.name || 'Unknown'
    },
    created_at: new Date(run.created_at),
    updated_at: new Date(run.updated_at),
    run_number: run.run_number,
    url: run.html_url,
    run_attempt: run.run_attempt,
    stored_at: serverTimestamp()
  };
  
  // Add job data
  const jobsData = jobs.map(job => ({
    id: job.id,
    name: job.name,
    status: job.status,
    conclusion: job.conclusion,
    started_at: job.started_at ? new Date(job.started_at) : null,
    completed_at: job.completed_at ? new Date(job.completed_at) : null,
    duration: job.started_at && job.completed_at ? 
      (new Date(job.completed_at) - new Date(job.started_at)) / 1000 : null,
    url: job.html_url,
    errors: job.conclusion === 'failure' ? extractErrorInfo(job) : []
  }));
  
  runData.jobs = jobsData;
  
  // Calculate overall stats
  const totalJobs = jobs.length;
  const successfulJobs = jobs.filter(job => job.conclusion === 'success').length;
  const failedJobs = jobs.filter(job => job.conclusion === 'failure').length;
  const skippedJobs = jobs.filter(job => job.conclusion === 'skipped').length;
  
  runData.stats = {
    total_jobs: totalJobs,
    successful_jobs: successfulJobs,
    failed_jobs: failedJobs,
    skipped_jobs: skippedJobs,
    success_rate: totalJobs > 0 ? (successfulJobs / totalJobs) * 100 : 0
  };
  
  // Store in Firestore
  await setDoc(runRef, runData);
  
  return runData;
}

/**
 * Main function
 */
async function main() {
  try {
    console.log(`Fetching workflow run${runId ? ` ${runId}` : ''} for ${REPO}...`);
    
    // Get workflow run
    const run = await getWorkflowRun(runId);
    console.log(`Retrieved workflow run #${run.run_number} (${run.id})`);
    
    // Get jobs for the run
    const jobs = await getWorkflowJobs(run.id);
    console.log(`Retrieved ${jobs.length} jobs for run #${run.run_number}`);
    
    // Store in Firestore
    const storedData = await storeWorkflowRun(run, jobs);
    console.log(`Successfully stored workflow run #${run.run_number} in Firestore`);
    console.log(`Status: ${storedData.status}, Conclusion: ${storedData.conclusion || 'pending'}`);
    console.log(`Success rate: ${storedData.stats.success_rate.toFixed(1)}%`);
    
    // Log summary of jobs
    console.log('\nJob summary:');
    storedData.jobs.forEach(job => {
      const status = job.conclusion === 'success' ? '✅' : 
                    job.conclusion === 'failure' ? '❌' : 
                    job.conclusion === 'skipped' ? '⏭️' : '⏳';
      
      console.log(`${status} ${job.name} - ${job.status} (${job.conclusion || 'pending'})`);
      
      if (job.errors && job.errors.length > 0) {
        console.log('  Errors:');
        job.errors.forEach(error => {
          console.log(`  - Step #${error.number}: ${error.name}`);
        });
      }
    });
    
    console.log(`\nView details at: ${storedData.url}`);
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('API response error:', error.response.data);
    }
    process.exit(1);
  }
}

// Execute the script
main(); 