#!/usr/bin/env node

/**
 * Firebase setup script for CI/CD pipeline monitoring
 * 
 * This script guides you through creating a new Firebase project
 * or configuring an existing one for CI/CD pipeline monitoring.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log(`
===============================================
Firebase Setup for CI/CD Pipeline Monitoring
===============================================

This script will help you configure Firebase for storing
and visualizing CI/CD pipeline runs.

Steps:
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore in your project
3. Create a web app in your Firebase project
4. Get your Firebase configuration
5. Configure this project to use your Firebase project
`);

// Questions to ask the user
const questions = [
  {
    name: 'api<PERSON><PERSON>',
    question: 'Enter your Firebase API Key:',
  },
  {
    name: 'authDomain',
    question: 'Enter your Firebase Auth Domain:',
  },
  {
    name: 'projectId',
    question: 'Enter your Firebase Project ID:',
  },
  {
    name: 'storageBucket',
    question: 'Enter your Firebase Storage Bucket:',
  },
  {
    name: 'messagingSenderId',
    question: 'Enter your Firebase Messaging Sender ID:',
  },
  {
    name: 'appId',
    question: 'Enter your Firebase App ID:',
  },
  {
    name: 'githubToken',
    question: 'Enter your GitHub Personal Access Token (with repo scope):',
  }
];

// Config object to store answers
const config = {};

// Function to ask questions sequentially
function askQuestions(index = 0) {
  if (index >= questions.length) {
    // All questions answered, process the config
    processConfig();
    return;
  }

  const question = questions[index];
  rl.question(`${question.question} `, (answer) => {
    config[question.name] = answer;
    askQuestions(index + 1);
  });
}

// Process the config and create .env file
function processConfig() {
  console.log('\nCreating .env file...');

  const envContent = `# GitHub Configuration
GITHUB_TOKEN=${config.githubToken}

# Firebase Configuration
FIREBASE_API_KEY=${config.apiKey}
FIREBASE_AUTH_DOMAIN=${config.authDomain}
FIREBASE_PROJECT_ID=${config.projectId}
FIREBASE_STORAGE_BUCKET=${config.storageBucket}
FIREBASE_MESSAGING_SENDER_ID=${config.messagingSenderId}
FIREBASE_APP_ID=${config.appId}

# Server Configuration
PORT=3000
`;

  // Write .env file
  fs.writeFileSync(path.join(__dirname, '..', '.env'), envContent);
  console.log('✅ .env file created successfully!');

  // Create firestore.rules file with basic security rules
  const rulesContent = `rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Pipeline runs collection
    match /pipeline-runs/{runId} {
      // Only authenticated users can read pipeline data
      allow read: if request.auth != null;
      
      // Only authorized service accounts can write pipeline data
      allow write: if request.auth != null && 
                     request.auth.token.email_verified && 
                     request.auth.token.email.matches(".*@${config.projectId}.iam.gserviceaccount.com");
    }
  }
}
`;

  fs.writeFileSync(path.join(__dirname, '..', 'firestore.rules'), rulesContent);
  console.log('✅ firestore.rules file created successfully!');

  // Instructions for next steps
  console.log(`
===============================================
Setup Complete! Next Steps:
===============================================

1. Install dependencies:
   $ npm install

2. Deploy Firestore rules (optional):
   $ firebase login
   $ firebase init firestore
   $ firebase deploy --only firestore:rules

3. Run the dashboard:
   $ npm start

4. Store pipeline runs:
   $ npm run store

For more information, see the documentation in docs/ci_monitoring.md.
`);

  rl.close();
}

// Start asking questions
askQuestions(); 