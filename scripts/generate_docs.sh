#!/bin/bash
# scripts/generate_docs.sh
# Script to generate documentation for ATTINY Control System

# Exit on error
set -e

echo "Generating documentation for ATTINY Control System..."

# Check if Doxygen is installed
if ! command -v doxygen &> /dev/null; then
    echo "Error: Doxygen is not installed. Please install it and try again."
    exit 1
fi

# Navigate to project root directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR/.."

# Generate Doxygen documentation
echo "Running Doxygen..."
doxygen Doxyfile

# Check if we have pandoc for markdown conversion
if command -v pandoc &> /dev/null; then
    echo "Converting markdown files to HTML..."
    
    # Create directory for HTML files if it doesn't exist
    mkdir -p docs/html
    
    # Convert markdown files to HTML
    for file in docs/*.md; do
        if [ -f "$file" ]; then
            filename=$(basename -- "$file")
            name="${filename%.*}"
            echo "Converting $filename to HTML..."
            pandoc "$file" -o "docs/html/${name}.html" --standalone --metadata title="${name}"
        fi
    done
else
    echo "Pandoc not found. Skipping markdown to HTML conversion."
    echo "Consider installing pandoc for full documentation generation."
fi

echo "Documentation generated successfully in docs/api/ and docs/html/"
echo "Open docs/api/index.html in your browser to view the API documentation."