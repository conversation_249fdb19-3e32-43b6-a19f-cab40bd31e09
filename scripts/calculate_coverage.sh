#!/bin/bash

# Code Coverage Analysis Script for ATtiny85 Control System
# This script calculates code coverage for unit tests

echo "========================================"
echo "Calculating Code Coverage"
echo "========================================"

# Navigate to the project root directory
PROJECT_ROOT=$(realpath $(dirname $(dirname $0)))
cd $PROJECT_ROOT

# Create output directory
mkdir -p coverage-reports

# Check if gcov and lcov are installed
if ! command -v gcov &> /dev/null; then
    echo "❌ gcov is not installed. Please install it first."
    echo "   On Ubuntu/Debian: sudo apt-get install gcc"
    echo "   On macOS: It should be included with Xcode"
    exit 1
fi

if ! command -v lcov &> /dev/null; then
    echo "❌ lcov is not installed. Please install it first."
    echo "   On Ubuntu/Debian: sudo apt-get install lcov"
    echo "   On macOS: brew install lcov"
    exit 1
fi

# Compile tests with coverage flags
echo "Compiling tests with coverage flags..."
mkdir -p test/build

# Clean previous coverage data
rm -f test/build/*.gcda test/build/*.gcno

# Compile each test with coverage flags
compile_test_with_coverage() {
    test_file=$1
    test_name=$(basename $test_file .cpp)
    echo "Compiling test: $test_name"
    
    g++ -std=c++11 -o test/build/$test_name \
        -I. \
        -Isrc \
        -Itest \
        -Itest/unity \
        -DNATIVE_TEST \
        --coverage -fprofile-arcs -ftest-coverage \
        $test_file \
        test/mocks/Arduino.cpp \
        test/unity/unity.c
        
    if [ $? -ne 0 ]; then
        echo "❌ Compilation failed: $test_name"
        return 1
    fi
    
    return 0
}

# Run all tests and collect coverage data
echo "Running tests and collecting coverage data..."
for test_file in test/unit/test_*.cpp; do
    test_name=$(basename $test_file .cpp)
    
    compile_test_with_coverage $test_file
    if [ $? -eq 0 ]; then
        echo "Running test: $test_name"
        test/build/$test_name > /dev/null
    fi
done

# Generate coverage report
echo "Generating coverage report..."
lcov --capture --directory . --output-file coverage-reports/coverage.info
lcov --remove coverage-reports/coverage.info '*/test/*' '/usr/*' --output-file coverage-reports/coverage.info

# Generate HTML report
genhtml coverage-reports/coverage.info --output-directory coverage-reports/html

# Calculate coverage percentages for each module
echo "Calculating module coverage..."
modules=$(find src/modules -name "*.cpp" | sed 's|.*/||' | sed 's|\.cpp$||')

echo "# Code Coverage Report" > coverage-reports/coverage-report.md
echo "" >> coverage-reports/coverage-report.md
echo "## Module Coverage" >> coverage-reports/coverage-report.md
echo "" >> coverage-reports/coverage-report.md
echo "| Module | Line Coverage | Function Coverage |" >> coverage-reports/coverage-report.md
echo "|--------|---------------|-------------------|" >> coverage-reports/coverage-report.md

total_lines=0
total_covered_lines=0
total_functions=0
total_covered_functions=0

for module in $modules; do
    module_coverage=$(lcov --extract coverage-reports/coverage.info "*$module.cpp" --output-file /dev/stdout | grep -E "lines|functions" | grep -E ":[[:space:]]*[0-9]+")
    
    lines=$(echo "$module_coverage" | grep "lines" | grep -oE "[0-9]+\.[0-9]+%" | head -1)
    functions=$(echo "$module_coverage" | grep "functions" | grep -oE "[0-9]+\.[0-9]+%" | head -1)
    
    # Extract numeric values for totals
    lines_num=$(echo "$module_coverage" | grep "lines" | grep -oE "[0-9]+ of [0-9]+" | head -1)
    functions_num=$(echo "$module_coverage" | grep "functions" | grep -oE "[0-9]+ of [0-9]+" | head -1)
    
    covered_lines=$(echo "$lines_num" | cut -d' ' -f1)
    total_module_lines=$(echo "$lines_num" | cut -d' ' -f3)
    covered_functions=$(echo "$functions_num" | cut -d' ' -f1)
    total_module_functions=$(echo "$functions_num" | cut -d' ' -f3)
    
    # Accumulate totals
    total_lines=$((total_lines + total_module_lines))
    total_covered_lines=$((total_covered_lines + covered_lines))
    total_functions=$((total_functions + total_module_functions))
    total_covered_functions=$((total_covered_functions + covered_functions))
    
    # Add to report
    echo "| $module | $lines | $functions |" >> coverage-reports/coverage-report.md
done

# Calculate overall coverage
overall_line_coverage=0
overall_function_coverage=0
if [ $total_lines -gt 0 ]; then
    overall_line_coverage=$(echo "scale=2; 100 * $total_covered_lines / $total_lines" | bc)
fi
if [ $total_functions -gt 0 ]; then
    overall_function_coverage=$(echo "scale=2; 100 * $total_covered_functions / $total_functions" | bc)
fi

echo "" >> coverage-reports/coverage-report.md
echo "## Overall Coverage" >> coverage-reports/coverage-report.md
echo "" >> coverage-reports/coverage-report.md
echo "| Metric | Coverage | Target |" >> coverage-reports/coverage-report.md
echo "|--------|----------|--------|" >> coverage-reports/coverage-report.md
echo "| Lines | ${overall_line_coverage}% | 90% |" >> coverage-reports/coverage-report.md
echo "| Functions | ${overall_function_coverage}% | 95% |" >> coverage-reports/coverage-report.md

echo "" >> coverage-reports/coverage-report.md
echo "## Coverage Details" >> coverage-reports/coverage-report.md
echo "" >> coverage-reports/coverage-report.md
echo "Detailed coverage report is available in the HTML report." >> coverage-reports/coverage-report.md

# Print summary
echo "========================================"
echo "Coverage Summary"
echo "========================================"
echo "Line coverage: ${overall_line_coverage}%"
echo "Function coverage: ${overall_function_coverage}%"
echo "Detailed HTML report is available at: coverage-reports/html/index.html"
echo "Markdown report is available at: coverage-reports/coverage-report.md"

# Check if target coverage is met
if (( $(echo "$overall_line_coverage < 90" | bc -l) )); then
    echo "❌ Line coverage is below the target of 90%"
    exit_code=1
else
    echo "✅ Line coverage meets or exceeds the target of 90%"
    exit_code=0
fi

if (( $(echo "$overall_function_coverage < 95" | bc -l) )); then
    echo "❌ Function coverage is below the target of 95%"
    exit_code=1
else
    echo "✅ Function coverage meets or exceeds the target of 95%"
fi

exit $exit_code 