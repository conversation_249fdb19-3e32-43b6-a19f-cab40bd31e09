#!/bin/bash
# run_emulation_tests.sh - <PERSON>ript to run emulation tests with helpful options
# Usage: ./scripts/run_emulation_tests.sh [options]

set -e  # Exit on error

# Default values
TEST_PATTERN=""
VCD_OUTPUT_DIR="test-traces"
OPEN_WAVEFORM=false
CLEAN_BUILD=false
VERBOSE=false
GTKWAVE_PATH=$(which gtkwave 2>/dev/null || echo "")

# Print help
function show_help {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -t, --test PATTERN   Run tests matching pattern (e.g. 'test_integration_basic')"
    echo "  -o, --output DIR     Directory to save VCD trace files (default: 'test-traces')"
    echo "  -w, --wave           Open waveform in GTKWave after test (if available)"
    echo "  -c, --clean          Clean build before running tests"
    echo "  -v, --verbose        Enable verbose output"
    echo "  -h, --help           Show this help message"
    echo ""
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -t|--test)
            TEST_PATTERN="$2"
            shift 2
            ;;
        -o|--output)
            VCD_OUTPUT_DIR="$2"
            shift 2
            ;;
        -w|--wave)
            OPEN_WAVEFORM=true
            shift
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Create output directory
mkdir -p "$VCD_OUTPUT_DIR"

# Set PlatformIO verbosity
VERBOSE_FLAG=""
if [ "$VERBOSE" = true ]; then
    VERBOSE_FLAG="-v"
fi

# Clean if requested
if [ "$CLEAN_BUILD" = true ]; then
    echo "Cleaning build..."
    platformio run --target clean $VERBOSE_FLAG
fi

# Build command
TEST_CMD="platformio test -e emulator $VERBOSE_FLAG"
if [ -n "$TEST_PATTERN" ]; then
    TEST_CMD="$TEST_CMD -f $TEST_PATTERN"
fi

# Run tests
echo "Running emulation tests..."
eval "$TEST_CMD"

# Copy VCD files to output directory
echo "Copying trace files to $VCD_OUTPUT_DIR..."
find .pio/build/emulator -name "*.vcd" -exec cp {} "$VCD_OUTPUT_DIR/" \;

# List trace files
TRACES=$(find "$VCD_OUTPUT_DIR" -name "*.vcd" -type f -newer .pio/build/emulator/firmware.elf)
if [ -z "$TRACES" ]; then
    echo "No trace files generated in this run."
    exit 0
fi

echo "Generated trace files:"
for trace in $TRACES; do
    echo "  $trace"
done

# Open waveform if requested and GTKWave is available
if [ "$OPEN_WAVEFORM" = true ]; then
    if [ -n "$GTKWAVE_PATH" ]; then
        echo "Opening latest trace in GTKWave..."
        newest_trace=$(ls -t $TRACES | head -1)
        $GTKWAVE_PATH "$newest_trace" &
    else
        echo "GTKWave not found. Install GTKWave to view waveforms."
        echo "You can open VCD files with any waveform viewer manually."
    fi
fi

echo "Emulation tests completed successfully!" 