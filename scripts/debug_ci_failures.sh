#!/bin/bash
# <PERSON>ript to download and analyze CI pipeline failure logs using GitHub API

set -e

# Configuration
REPO="Cannasol-Tech/attiny-control"
OUTPUT_DIR="pipeline-logs"
GITHUB_API="https://api.github.com"

# Create output directory
mkdir -p "${OUTPUT_DIR}"
echo "📁 Created output directory: ${OUTPUT_DIR}"

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Check for required tools
if ! command_exists curl; then
    echo "❌ curl is not installed but required for API calls"
    exit 1
fi

if ! command_exists jq; then
    echo "⚠️ jq is not installed. For better output formatting, install jq."
    USE_JQ=false
else
    USE_JQ=true
fi

# Prompt for GitHub token if not provided via environment
if [ -z "$GITHUB_TOKEN" ]; then
    echo "Enter your GitHub personal access token (will be hidden):"
    read -s GITHUB_TOKEN
    echo
fi

if [ -z "$GITHUB_TOKEN" ]; then
    echo "❌ GitHub token is required"
    exit 1
fi

# Get latest workflow run ID
echo "🔍 Finding latest CI/CD workflow runs..."
RESPONSE=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
    "${GITHUB_API}/repos/${REPO}/actions/workflows/ci-cd.yml/runs?per_page=5")

if [ "$USE_JQ" = true ]; then
    RUNS=$(echo "$RESPONSE" | jq -r '.workflow_runs[] | "\(.id) \(.status) \(.conclusion) \(.created_at) \(.head_commit.message)"')
    LATEST_RUN_ID=$(echo "$RESPONSE" | jq -r '.workflow_runs[0].id')
else
    # Simple parsing without jq (less robust)
    LATEST_RUN_ID=$(echo "$RESPONSE" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    RUNS="Cannot display full run details without jq. Latest run ID: $LATEST_RUN_ID"
fi

echo "Recent workflow runs:"
echo "$RUNS"
echo

# Allow user to select a specific run
echo "Enter run ID to analyze (press Enter for latest run $LATEST_RUN_ID):"
read -r RUN_ID
if [ -z "$RUN_ID" ]; then
    RUN_ID=$LATEST_RUN_ID
fi

# Create directory for this run
RUN_DIR="${OUTPUT_DIR}/run-${RUN_ID}"
mkdir -p "$RUN_DIR"

# Get run details
echo "📊 Getting details for workflow run: $RUN_ID"
RUN_DETAILS=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
    "${GITHUB_API}/repos/${REPO}/actions/runs/${RUN_ID}")

if [ "$USE_JQ" = true ]; then
    RUN_STATUS=$(echo "$RUN_DETAILS" | jq -r '.status')
    RUN_CONCLUSION=$(echo "$RUN_DETAILS" | jq -r '.conclusion')
    COMMIT_MESSAGE=$(echo "$RUN_DETAILS" | jq -r '.head_commit.message')
    
    echo "Status: $RUN_STATUS"
    echo "Conclusion: $RUN_CONCLUSION"
    echo "Commit: $COMMIT_MESSAGE"
else
    echo "Run details retrieved (install jq for better formatting)"
fi

# Get jobs for this run
echo "👷 Getting jobs for run $RUN_ID..."
JOBS_RESPONSE=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
    "${GITHUB_API}/repos/${REPO}/actions/runs/${RUN_ID}/jobs")

# Download logs for each job
if [ "$USE_JQ" = true ]; then
    JOB_COUNT=$(echo "$JOBS_RESPONSE" | jq '.jobs | length')
    echo "Found $JOB_COUNT jobs"
    
    echo "$JOBS_RESPONSE" | jq -c '.jobs[]' | while read -r job; do
        JOB_ID=$(echo "$job" | jq -r '.id')
        JOB_NAME=$(echo "$job" | jq -r '.name')
        JOB_STATUS=$(echo "$job" | jq -r '.conclusion')
        
        echo "⬇️ Downloading logs for job: $JOB_NAME (Status: $JOB_STATUS)"
        
        # Only download logs if the job has completed
        if [ "$JOB_STATUS" != "null" ]; then
            LOG_URL=$(echo "$job" | jq -r '.logs_url')
            LOG_FILE="${RUN_DIR}/${JOB_NAME// /_}.log.zip"
            
            curl -s -L -H "Authorization: token $GITHUB_TOKEN" \
                -o "$LOG_FILE" "$LOG_URL"
            
            # Extract the logs
            JOB_DIR="${RUN_DIR}/${JOB_NAME// /_}"
            mkdir -p "$JOB_DIR"
            unzip -q -o "$LOG_FILE" -d "$JOB_DIR" || echo "⚠️ Failed to extract logs for $JOB_NAME"
            
            # If job failed, highlight it
            if [ "$JOB_STATUS" = "failure" ]; then
                echo "❌ FAILURE detected in job: $JOB_NAME"
                echo "   Log file: $JOB_DIR"
                
                # Search for common error patterns
                if [ -d "$JOB_DIR" ]; then
                    echo "   Common errors found:"
                    grep -n "error:" "$JOB_DIR"/* 2>/dev/null | head -10 | sed 's/^/   - /'
                    grep -n "failure" "$JOB_DIR"/* 2>/dev/null | head -10 | sed 's/^/   - /'
                    grep -n "failed" "$JOB_DIR"/* 2>/dev/null | head -10 | sed 's/^/   - /'
                fi
            fi
        else
            echo "   ⏳ Job is still running or logs not available"
        fi
    done
else
    echo "Cannot process jobs without jq. Please install jq for better functionality."
fi

# Download artifacts
echo "📦 Checking for artifacts..."
ARTIFACTS_RESPONSE=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
    "${GITHUB_API}/repos/${REPO}/actions/runs/${RUN_ID}/artifacts")

if [ "$USE_JQ" = true ]; then
    ARTIFACT_COUNT=$(echo "$ARTIFACTS_RESPONSE" | jq '.artifacts | length')
    echo "Found $ARTIFACT_COUNT artifacts"
    
    ARTIFACTS_DIR="${RUN_DIR}/artifacts"
    mkdir -p "$ARTIFACTS_DIR"
    
    echo "$ARTIFACTS_RESPONSE" | jq -c '.artifacts[]' | while read -r artifact; do
        ARTIFACT_ID=$(echo "$artifact" | jq -r '.id')
        ARTIFACT_NAME=$(echo "$artifact" | jq -r '.name')
        
        echo "⬇️ Downloading artifact: $ARTIFACT_NAME"
        ARTIFACT_FILE="${ARTIFACTS_DIR}/${ARTIFACT_NAME}.zip"
        
        curl -s -L -H "Authorization: token $GITHUB_TOKEN" \
            -o "$ARTIFACT_FILE" \
            "${GITHUB_API}/repos/${REPO}/actions/artifacts/${ARTIFACT_ID}/zip"
        
        # Extract the artifact
        ARTIFACT_DIR="${ARTIFACTS_DIR}/${ARTIFACT_NAME}"
        mkdir -p "$ARTIFACT_DIR"
        unzip -q -o "$ARTIFACT_FILE" -d "$ARTIFACT_DIR" || echo "⚠️ Failed to extract artifact: $ARTIFACT_NAME"
    done
else
    echo "Cannot process artifacts without jq"
fi

echo "✨ Done! All available logs downloaded to: ${RUN_DIR}"
echo "Review the logs to identify the pipeline failures."

# Create summary report
SUMMARY_FILE="${RUN_DIR}/failure_summary.md"
echo "# CI Pipeline Failure Summary" > "$SUMMARY_FILE"
echo "" >> "$SUMMARY_FILE"
echo "## Run Information" >> "$SUMMARY_FILE"
echo "- **Run ID**: $RUN_ID" >> "$SUMMARY_FILE"
echo "- **Status**: $RUN_STATUS" >> "$SUMMARY_FILE"
echo "- **Conclusion**: $RUN_CONCLUSION" >> "$SUMMARY_FILE"
echo "- **Commit**: $COMMIT_MESSAGE" >> "$SUMMARY_FILE"
echo "" >> "$SUMMARY_FILE"

if [ "$USE_JQ" = true ]; then
    echo "## Job Status" >> "$SUMMARY_FILE"
    echo "$JOBS_RESPONSE" | jq -r '.jobs[] | "- **" + .name + "**: " + (.conclusion // "running")' >> "$SUMMARY_FILE"
    echo "" >> "$SUMMARY_FILE"
    
    echo "## Failure Analysis" >> "$SUMMARY_FILE"
    echo "$JOBS_RESPONSE" | jq -c '.jobs[]' | while read -r job; do
        JOB_NAME=$(echo "$job" | jq -r '.name')
        JOB_STATUS=$(echo "$job" | jq -r '.conclusion')
        
        if [ "$JOB_STATUS" = "failure" ]; then
            echo "### $JOB_NAME" >> "$SUMMARY_FILE"
            echo "" >> "$SUMMARY_FILE"
            
            JOB_DIR="${RUN_DIR}/${JOB_NAME// /_}"
            if [ -d "$JOB_DIR" ]; then
                # Extract some context from the logs
                ERROR_CONTEXT=$(grep -n -A 5 -B 5 "error:" "$JOB_DIR"/* 2>/dev/null | head -20)
                if [ -n "$ERROR_CONTEXT" ]; then
                    echo "#### Error Context" >> "$SUMMARY_FILE"
                    echo '```' >> "$SUMMARY_FILE"
                    echo "$ERROR_CONTEXT" >> "$SUMMARY_FILE"
                    echo '```' >> "$SUMMARY_FILE"
                    echo "" >> "$SUMMARY_FILE"
                fi
                
                # Extract common failure patterns
                FAILURES=$(grep -n "failure" "$JOB_DIR"/* 2>/dev/null | head -10)
                if [ -n "$FAILURES" ]; then
                    echo "#### Failure Messages" >> "$SUMMARY_FILE"
                    echo '```' >> "$SUMMARY_FILE"
                    echo "$FAILURES" >> "$SUMMARY_FILE"
                    echo '```' >> "$SUMMARY_FILE"
                    echo "" >> "$SUMMARY_FILE"
                fi
            else
                echo "No log data available" >> "$SUMMARY_FILE"
                echo "" >> "$SUMMARY_FILE"
            fi
        fi
    done
fi

echo "📄 Summary report created: $SUMMARY_FILE"
if command_exists open; then
    open "$SUMMARY_FILE"
elif command_exists xdg-open; then
    xdg-open "$SUMMARY_FILE"
fi 