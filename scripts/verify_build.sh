#!/bin/bash
# Build verification script for ATTINY85 Control System

echo "==== ATTINY85 Control System Build Verification ===="

# Check PlatformIO installation
if ! command -v platformio &> /dev/null
then
    echo "ERROR: PlatformIO not found! Please install it."
    exit 1
fi

# Check AVR toolchain
if ! command -v avr-gcc &> /dev/null
then
    echo "ERROR: AVR GCC not found! Please install the AVR toolchain."
    exit 1
fi

echo "✅ Found required tools"

# Clean previous builds
echo "Cleaning previous builds..."
platformio run --target clean

# Build for each environment
for env in attiny85 native emulator
do
    echo "Building for environment: $env"
    platformio run -e $env || { echo "❌ Build failed for $env!"; exit 1; }
    echo "✅ Build for $env completed successfully"
done

# Check firmware size for ATtiny85
echo "Checking memory usage..."
SIZE_OUTPUT=$(platformio run -e attiny85 --verbose 2>&1 | grep -E "RAM:|Flash:")
echo "$SIZE_OUTPUT"

# Check if firmware fits in memory
if echo "$SIZE_OUTPUT" | grep -q "1[0-9][0-9]\.0%"; then
    echo "❌ WARNING: Program may be too large for the ATtiny85!"
fi

# Check for library compatibility
echo "Checking library dependencies..."
platformio pkg list

# Run unit tests if requested
if [ "$1" == "--with-tests" ]; then
    echo "Running unit tests..."
    platformio test -e native || echo "⚠️ Some unit tests may have failed"
    
    echo "Running integration tests..."
    platformio test -e emulator || echo "⚠️ Some integration tests may have failed"
fi

echo "Checking build output files..."
if [ -f ".pio/build/attiny85/firmware.hex" ]; then
    echo "✅ Found firmware.hex"
else
    echo "❌ Missing firmware.hex"
    exit 1
fi

if [ -f ".pio/build/attiny85/firmware.elf" ]; then
    echo "✅ Found firmware.elf"
else
    echo "❌ Missing firmware.elf"
    exit 1
fi

echo "==== Build verification complete! ===="
echo "Run with --with-tests to also run unit and integration tests"
exit 0 