/**
 * @file pin_config.h
 * @brief Pin configuration for ATTINY85 Control System
 */

#ifndef PIN_CONFIG_H
#define PIN_CONFIG_H

#include <Arduino.h>

/**
 * @brief Class to manage pin configuration and access
 */
class PinConfig {
public:
    /**
     * @brief Initialize all pins
     */
    static void init();

    /**
     * @brief Pin definitions
     */
    static const int AIR_OUT = 0;     // PIN PB0
    static const int PWM_OUT = 1;     // PIN PB1
    static const int PUMP_OUT = 2;    // PIN PB2
    static const int SONIC_IN = 3;    // PIN PB3
    static const int PWM_IN = 4;      // PIN PB4
    static const int STATUS_OUT = 5;  // PIN PB5

private:
    /**
     * @brief Private constructor to prevent instantiation
     */
    PinConfig() {}
};

#endif // PIN_CONFIG_H 