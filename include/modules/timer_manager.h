/**
 * @file timer_manager.h
 * @brief Timer management for ATTINY85 Control System
 */

#ifndef TIMER_MANAGER_H
#define TIMER_MANAGER_H

#include <Arduino.h>

/**
 * @brief Class to manage timer configurations
 */
class TimerManager {
public:
    /**
     * @brief Initialize Timer1 for timing the air pressure control
     */
    static void initTimer1();
    
    /**
     * @brief Get the current interrupt count
     * @return The current interrupt count
     */
    static uint16_t getInterruptCount();
    
    /**
     * @brief Set the interrupt count
     * @param count The value to set the interrupt count to
     */
    static void setInterruptCount(uint16_t count);
    
    /**
     * @brief Reset the timer counter
     */
    static void resetCounter();
    
    /**
     * @brief Get the maximum interrupt count (for timeout)
     * @return The maximum interrupt count
     */
    static uint16_t getMaxInterruptCount();

private:
    // Interrupt counter for timing air control
    static volatile uint16_t interruptCount;
    
    // Maximum interrupt count for timing
    static const uint16_t INTERRUPT_COUNT_MAX = 579;
    
    /**
     * @brief Private constructor to prevent instantiation
     */
    TimerManager() {}
};

#endif // TIMER_MANAGER_H 