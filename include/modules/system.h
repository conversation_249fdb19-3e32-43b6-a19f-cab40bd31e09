/**
 * @file system.h
 * @brief System integration for ATTINY85 Control System
 */

#ifndef SYSTEM_H
#define SYSTEM_H

/**
 * @brief Class to manage system integration
 */
class System {
public:
    /**
     * @brief Initialize the entire system
     */
    static void init();
    
    /**
     * @brief Process system control loop
     * This is called repeatedly in the main loop
     */
    static void process();

private:
    /**
     * @brief Private constructor to prevent instantiation
     */
    System() {}
};

#endif // SYSTEM_H 