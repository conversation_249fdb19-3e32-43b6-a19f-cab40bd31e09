/**
 * @file air_control.h
 * @brief Air pressure control for ATTINY85 Control System
 */

#ifndef AIR_CONTROL_H
#define AIR_CONTROL_H

#include <Arduino.h>
#include "modules/pin_config.h"

/**
 * @brief Class to manage air pressure control
 */
class AirControl {
public:
    /**
     * @brief Initialize air control system
     */
    static void init();
    
    /**
     * @brief Turn air pressure on
     */
    static void turnOn();
    
    /**
     * @brief Turn air pressure off
     */
    static void turnOff();
    
    /**
     * @brief Check if air pressure is on
     * @return True if air is on, false otherwise
     */
    static bool isOn();
    
    /**
     * @brief Process air pressure control logic
     */
    static void process();
    
    /**
     * @brief Set the air status
     * @param status The status to set (true = on, false = off)
     */
    static void setStatus(bool status);

private:
    // Air status (on/off)
    static bool airStatus;
    
    // Previous air status
    static bool prevAirStatus;
    
    /**
     * @brief Private constructor to prevent instantiation
     */
    AirControl() {}
    
    /**
     * @brief Update air status in EEPROM if changed
     */
    static void updateEeprom();
};

/**
 * @brief Turn on the air
 */
void air_turnOn();

/**
 * @brief Turn off the air
 */
void air_turnOff();

/**
 * @brief Check if air is running
 * @return True if air is on, false otherwise
 */
bool isAirRunning();

/**
 * @brief Turn off the air from an interrupt service routine
 * This is a critical function that is called from timer ISR
 */
void air_turnOff_from_ISR();

/**
 * @brief Reset the air control state for testing
 * This is only used in testing environments
 */
#if defined(NATIVE_TEST) || defined(EMULATION_MODE)
void air_resetState();
#endif

#endif // AIR_CONTROL_H 