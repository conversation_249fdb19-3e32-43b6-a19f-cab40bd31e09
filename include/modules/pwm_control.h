/**
 * @file pwm_control.h
 * @brief PWM signal processing for ATTINY85 Control System
 */

#ifndef PWM_CONTROL_H
#define PWM_CONTROL_H

#include <Arduino.h>
#include "modules/pin_config.h"

/**
 * @brief Class to manage PWM signal processing
 */
class PwmControl {
public:
    /**
     * @brief Initialize PWM control
     */
    static void init();

    /**
     * @brief Get PWM duty cycle from a pin
     * @param pin The pin to read PWM from
     * @return The duty cycle as a percentage (0-100)
     */
    static byte getDutyCycle(byte pin);

    /**
     * @brief Set PWM output duty cycle
     * @param percentage The duty cycle percentage (0-100)
     * @param cappedAt Maximum allowed duty cycle percentage (0-100)
     */
    static void setOutput(byte percentage, byte cappedAt = 77);

    /**
     * @brief Process input PWM and set output accordingly
     * @return The current duty cycle percentage
     */
    static byte processInputOutput();

private:
    // Current cycle percentage
    static byte cyclePercent;
    
    // PWM output value (0-255)
    static byte pwmOutVal;

    /**
     * @brief Private constructor to prevent instantiation
     */
    PwmControl() {}
};

#endif // PWM_CONTROL_H 