/**
 * @file pump_control.h
 * @brief Pump control for ATTINY85 Control System
 */

#ifndef PUMP_CONTROL_H
#define PUMP_CONTROL_H

#include <Arduino.h>
#include "modules/pin_config.h"

/**
 * @brief Class to manage pump control
 */
class PumpControl {
public:
    /**
     * @brief Initialize pump control
     */
    static void init();
    
    /**
     * @brief Turn pump on
     */
    static void turnOn();
    
    /**
     * @brief Turn pump off
     */
    static void turnOff();
    
    /**
     * @brief Check if pump is on
     * @return True if pump is on, false otherwise
     */
    static bool isOn();
    
    /**
     * @brief Process pump control based on duty cycle
     * @param dutyCycle The current PWM duty cycle
     */
    static void process(byte dutyCycle);

private:
    // Pump status
    static bool pumpStatus;
    
    /**
     * @brief Private constructor to prevent instantiation
     */
    PumpControl() {}
};

#endif // PUMP_CONTROL_H 