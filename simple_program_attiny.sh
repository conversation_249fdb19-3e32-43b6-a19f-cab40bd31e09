#!/bin/bash

# Simple ATtiny85 Programming Script
# This script focuses on reliable programming using minimal steps

echo "============================================="
echo "  Simple ATtiny85 Programming Script"
echo "============================================="
echo

# Set path to PlatformIO
PLATFORMIO="$HOME/Library/Python/3.9/bin/platformio"

# Check if PlatformIO exists
if [ ! -f "$PLATFORMIO" ]; then
    echo "❌ PlatformIO not found at $PLATFORMIO"
    echo "   Programming is not possible."
    exit 1
fi

# Find Arduino port
ARDUINO_PORT=$(ls /dev/tty.usb* 2>/dev/null | head -n 1)
if [ -z "$ARDUINO_PORT" ]; then
    echo "❌ No Arduino detected on any USB port"
    echo "   Check if <PERSON>rd<PERSON><PERSON> is connected and powered"
    exit 1
fi

echo "✅ Using PlatformIO at: $PLATFORMIO"
echo "✅ Arduino detected at: $ARDUINO_PORT"
echo

echo "Please confirm your setup:"
echo "1. Arduino R4 WiFi has the ArduinoISP sketch loaded"
echo "2. All connections are correct between Arduino and ATtiny85"
echo "3. Using a breadboard with stable connections"
echo "4. Optionally, a 10μF capacitor between RESET and GND on Arduino"
echo
read -p "Ready to continue? (y/n): " READY
if [ "$READY" != "y" ]; then
    echo "Exiting at user request."
    exit 0
fi

echo
echo "============================================="
echo "  Step 1: Building firmware"
echo "============================================="

# Just build first to make sure code compiles
"$PLATFORMIO" run
if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please fix code errors first."
    exit 1
fi

echo
echo "============================================="
echo "  Step 2: Programming ATtiny85"
echo "============================================="
echo "Press the reset button on Arduino now..."
echo "Starting upload in 3 seconds..."
sleep 3

# Try uploading with lower baud rate and extra parameters for reliability
"$PLATFORMIO" run --target upload --upload-port "$ARDUINO_PORT" \
    --extra-flags="--programmer arduino --reset --no-verify --force -v -e -U flash:w:.pio/build/attiny85/firmware.hex"

UPLOAD_RESULT=$?
if [ $UPLOAD_RESULT -eq 0 ]; then
    echo
    echo "✅ SUCCESS! ATtiny85 has been programmed successfully."
else
    echo
    echo "❌ Upload failed with exit code $UPLOAD_RESULT"
    echo
    echo "Trying alternative method in 3 seconds..."
    sleep 3
    
    # Try alternative approach with avrdude directly
    AVRDUDE_PATH="$HOME/.platformio/packages/tool-avrdude/avrdude"
    AVRDUDE_CONF="$HOME/.platformio/packages/tool-avrdude/avrdude.conf"
    
    if [ -f "$AVRDUDE_PATH" ]; then
        echo "Using avrdude directly for more control..."
        
        "$AVRDUDE_PATH" -C "$AVRDUDE_CONF" -v -pattiny85 -cstk500v1 -P"$ARDUINO_PORT" \
            -b9600 -e -Uflash:w:.pio/build/attiny85/firmware.hex:i -F
    else
        echo "❌ Could not find avrdude at $AVRDUDE_PATH"
        echo "Please try the following manual command in terminal:"
        echo "avrdude -c arduino -p attiny85 -P $ARDUINO_PORT -b9600 -e -U flash:w:.pio/build/attiny85/firmware.hex:i -F"
    fi
fi

echo
echo "============================================="
echo "  Troubleshooting"
echo "============================================="
echo "If programming failed, try the following:"
echo "1. Re-upload the ArduinoISP sketch to the Arduino"
echo "2. Check all connections and try again"
echo "3. Add or change the value of the capacitor between RESET and GND"
echo "4. Try pressing the reset button on Arduino just before uploading"
echo "5. Try using Arduino IDE directly with the ATtiny board package"
echo "=============================================" 