# ATTINY85 Control System

A modular control system for ATtiny85 microcontrollers with PWM input handling, pump control, and air pressure sensing. Built with modern development practices including unit testing, CI/CD integration, cloud-based firmware management, and production monitoring.

![CI/CD Status](https://github.com/user/attiny-control/actions/workflows/ci.yml/badge.svg)

## Features

- **Modular Architecture**: Clean separation of concerns with specialized modules
- **PWM Signal Processing**: Advanced input signal handling
- **Pump Control System**: Reliable pump activation based on input signals
- **Air Pressure Monitoring**: Safety timeout for air pressure buildup
- **Comprehensive Testing**: Unit and integration tests with hardware emulation
- **CI/CD Pipeline**: Automated build, test, and deployment processes
- **Cloud Integration**: Google Cloud Platform for firmware storage and management
- **Monitoring & Alerting**: Real-time device monitoring with automated alerts
- **Multi-Environment Deployment**: Structured deployment process across development, staging, and production
- **Version Management**: Sophisticated version tracking and rollout control
- **Rollback Capability**: Safe rollback procedures for production deployments

## Directory Structure

```
attiny-control/
├── .github/workflows/       # CI/CD configuration
├── src/                     # Source code
│   ├── main.cpp             # Main control logic
│   └── modules/             # Specialized modules
│       ├── air_control.cpp  # Air pressure control
│       ├── pin_config.cpp   # Pin configuration
│       ├── pump_control.cpp # Pump control module
│       ├── pwm_control.cpp  # PWM signal processing
│       ├── system.cpp       # System management
│       └── timer_manager.cpp # Timer utilities
├── include/                 # Header files
│   └── modules/             # Module headers
├── test/                    # Test code
│   ├── unit/                # Unit tests
│   ├── integration/         # Integration tests
│   └── emulation/           # Hardware emulation tests
├── docs/                    # Documentation
│   ├── api/                 # API documentation (generated)
│   ├── architecture.md      # System architecture
│   ├── user_manual.md       # User manual
│   ├── developer_guide.md   # Developer guide
│   ├── technical_reference.md # Technical reference
│   ├── hardware-setup.md    # Hardware setup guide
│   ├── emulation-testing.md # Emulation testing guide
│   ├── ci-cd-pipeline.md    # CI/CD pipeline info
│   └── production-deployment.md # Production deployment guide
├── lib/                     # External libraries
├── scripts/                 # Utility scripts
│   └── generate_docs.sh     # Documentation generation script
├── deploy.sh                # Deployment script
├── rollback.sh              # Rollback script
└── monitoring/              # Monitoring configuration
    ├── dashboard.json       # Monitoring dashboard
    ├── setup.sh             # Monitoring setup script
    └── tools/               # Monitoring tools
        ├── deployment-tracker.js # Deployment tracking tool
        └── version-manager.js    # Version management tool
```

## Documentation

The project includes comprehensive documentation:

- **[User Manual](docs/user_manual.md)** - For end users
- **[Technical Reference](docs/technical_reference.md)** - Hardware details
- **[Developer Guide](docs/developer_guide.md)** - For contributors
- **[Architecture](docs/architecture.md)** - System design
- **API Reference** - Generated Doxygen documentation (in docs/api after generation)
- **[Hardware Setup](docs/hardware-setup.md)** - Hardware configuration guide
- **[Emulation Testing](docs/emulation-testing.md)** - Emulation test procedures
- **[CI/CD Pipeline](docs/ci-cd-pipeline.md)** - Build pipeline details
- **[Production Deployment](docs/production-deployment.md)** - Production deployment procedures

### Generating Documentation

To generate the API documentation:

```bash
# Run the documentation generation script
./scripts/generate_docs.sh

# View the documentation
# Open docs/api/index.html in your browser
```

## Getting Started

### Prerequisites

- **Development Environment**:
  - [PlatformIO](https://platformio.org/) (recommended)
  - AVR toolchain for ATtiny85
  - [SimulAVR](https://www.nongnu.org/simulavr/) for hardware emulation
  - [Google Cloud SDK](https://cloud.google.com/sdk/docs/install) for cloud integration

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/user/attiny-control.git
   cd attiny-control
   ```

2. Install dependencies:
   ```bash
   platformio pkg install
   ```

3. Set up monitoring and cloud integration:
   ```bash
   # Install required Node.js packages
   npm install

   # Set up monitoring
   ./monitoring/setup.sh
   ```

### Building

```bash
# Build for ATtiny85
platformio run -e attiny85

# Build for emulation and testing
platformio run -e emulator
```

### Testing

The project includes a comprehensive testing framework to ensure reliability and correctness:

### Unit Tests

Unit tests verify the behavior of individual modules in isolation. These tests use:

- [Unity Test Framework](https://github.com/ThrowTheSwitch/Unity) for C/C++ testing
- Mock implementations of Arduino and EEPROM libraries
- A custom test runner script

To run unit tests:

```bash
# Run all unit tests
./test/run_unit_tests.sh

# Run a specific test (e.g., pin_config)
./test/run_unit_tests.sh test_pin_config
```

### Integration Tests

Integration tests verify the entire system's behavior using the SimulAVR emulator:

```bash
# Requires PlatformIO and SimulAVR
platformio test -e emulator
```

### Continuous Integration

The project uses GitHub Actions for continuous integration:

1. **Build Verification**: Ensures code compiles for the ATtiny85
2. **Unit Testing**: Runs all unit tests with detailed reporting
3. **Integration Testing**: Uses SimulAVR to test complete system behavior
4. **Code Analysis**: Static code analysis with cppcheck 
5. **Code Coverage**: Tracks test coverage metrics
6. **Documentation**: Generates and publishes test reports

For details about the CI/CD pipeline, see [docs/CI_CD.md](docs/CI_CD.md).

## Emulation Testing

This project includes comprehensive emulation testing using SimulAVR to simulate the ATtiny85 hardware:

- **No Hardware Required**: Test complete functionality without physical devices
- **Waveform Analysis**: Generated VCD files can be analyzed with tools like GTKWave
- **Time Manipulation**: Accelerate tests that would normally require waiting
- **Fault Injection**: Simulate various error conditions safely

For detailed information on emulation testing:
- See [Emulation Testing Guide](docs/emulation-testing.md)

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration:

1. **Build Verification**: Ensures code compiles for the ATtiny85
2. **Unit Testing**: Runs all unit tests with detailed reporting
3. **Integration Testing**: Uses SimulAVR to test complete system behavior
4. **Code Analysis**: Static code analysis with cppcheck 
5. **Code Coverage**: Tracks test coverage metrics
6. **Documentation**: Generates and publishes test reports

For details about the CI/CD pipeline, see [docs/CI_CD.md](docs/CI_CD.md).

## Code Quality

The project maintains high code quality standards through:

### Static Analysis

```bash
# Run static analysis checks
./scripts/run_code_analysis.sh
```

This performs:
- Static code analysis with cppcheck
- Code formatting verification with clang-format
- Include guard checks
- Generates a comprehensive quality report

### Code Coverage

```bash
# Generate code coverage report
./scripts/calculate_coverage.sh
```

This:
- Compiles tests with coverage instrumentation
- Runs tests and collects coverage data
- Generates HTML and Markdown reports
- Verifies coverage meets targets (90% line, 95% function)

### Style Guide

Code follows a consistent style defined in `.clang-format`. To format code:

```bash
# Format a specific file
clang-format -i path/to/file.cpp

# Format all source files
find src -name "*.cpp" -o -name "*.h" | xargs clang-format -i
```

### Continuous Integration

The CI pipeline enforces quality standards by:
- Running all unit and integration tests
- Verifying code quality with static analysis
- Measuring code coverage
- Checking firmware size constraints
- Generating comprehensive reports

## Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Run the tests (`./test/run_unit_tests.sh`)
4. Commit your changes (`git commit -m 'Add some amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- SimulAVR Project for the emulator
- PlatformIO for the development environment
- Unity for the test framework

## Documentation

The project includes comprehensive documentation:

- **[User Manual](docs/user_manual.md)** - For end users
- **[Technical Reference](docs/technical_reference.md)** - Hardware details
- **[Developer Guide](docs/developer_guide.md)** - For contributors
- **[Architecture](docs/architecture.md)** - System design
- **API Reference** - Generated Doxygen documentation (in docs/api after generation)
- **[Hardware Setup](docs/hardware-setup.md)** - Hardware configuration guide
- **[Emulation Testing](docs/emulation-testing.md)** - Emulation test procedures
- **[CI/CD Pipeline](docs/ci-cd-pipeline.md)** - Build pipeline details
- **[Production Deployment](docs/production-deployment.md)** - Production deployment procedures

### Generating Documentation

To generate the API documentation:

```bash
# Run the documentation generation script
./scripts/generate_docs.sh

# View the documentation
# Open docs/api/index.html in your browser
```

## Getting Started

### Prerequisites

- **Development Environment**:
  - [PlatformIO](https://platformio.org/) (recommended)
  - AVR toolchain for ATtiny85
  - [SimulAVR](https://www.nongnu.org/simulavr/) for hardware emulation
  - [Google Cloud SDK](https://cloud.google.com/sdk/docs/install) for cloud integration

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/user/attiny-control.git
   cd attiny-control
   ```

2. Install dependencies:
   ```bash
   platformio pkg install
   ```

3. Set up monitoring and cloud integration:
   ```bash
   # Install required Node.js packages
   npm install

   # Set up monitoring
   ./monitoring/setup.sh
   ```

### Building

```bash
# Build for ATtiny85
platformio run -e attiny85

# Build for emulation and testing
platformio run -e emulator
```

### Testing

The project includes a comprehensive testing framework to ensure reliability and correctness:

### Unit Tests

Unit tests verify the behavior of individual modules in isolation. These tests use:

- [Unity Test Framework](https://github.com/ThrowTheSwitch/Unity) for C/C++ testing
- Mock implementations of Arduino and EEPROM libraries
- A custom test runner script

To run unit tests:

```bash
# Run all unit tests
./test/run_unit_tests.sh

# Run a specific test (e.g., pin_config)
./test/run_unit_tests.sh test_pin_config
```

### Integration Tests

Integration tests verify the entire system's behavior using the SimulAVR emulator:

```bash
# Requires PlatformIO and SimulAVR
platformio test -e emulator
```

### Continuous Integration

The project uses GitHub Actions for continuous integration. The pipeline:

1. Runs unit tests
2. Runs integration tests
3. Builds firmware
4. Publishes test reports

For more details about the CI/CD pipeline, see [docs/CI_CD.md](docs/CI_CD.md).

## Emulation Testing

This project includes comprehensive emulation testing using SimulAVR to simulate the ATtiny85 hardware:

- **No Hardware Required**: Test complete functionality without physical devices
- **Waveform Analysis**: Generated VCD files can be analyzed with tools like GTKWave
- **Time Manipulation**: Accelerate tests that would normally require waiting
- **Fault Injection**: Simulate various error conditions safely

For detailed information on emulation testing:
- See [Emulation Testing Guide](docs/emulation-testing.md)

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration:

1. **Build Verification**: Ensures code compiles for the ATtiny85
2. **Unit Testing**: Runs all unit tests with detailed reporting
3. **Integration Testing**: Uses SimulAVR to test complete system behavior
4. **Code Analysis**: Static code analysis with cppcheck 
5. **Code Coverage**: Tracks test coverage metrics
6. **Documentation**: Generates and publishes test reports

For details about the CI/CD pipeline, see [docs/CI_CD.md](docs/CI_CD.md).

## Code Quality

The project maintains high code quality standards through:

### Static Analysis

```bash
# Run static analysis checks
./scripts/run_code_analysis.sh
```

This performs:
- Static code analysis with cppcheck
- Code formatting verification with clang-format
- Include guard checks
- Generates a comprehensive quality report

### Code Coverage

```bash
# Generate code coverage report
./scripts/calculate_coverage.sh
```

This:
- Compiles tests with coverage instrumentation
- Runs tests and collects coverage data
- Generates HTML and Markdown reports
- Verifies coverage meets targets (90% line, 95% function)

### Style Guide

Code follows a consistent style defined in `.clang-format`. To format code:

```bash
# Format a specific file
clang-format -i path/to/file.cpp

# Format all source files
find src -name "*.cpp" -o -name "*.h" | xargs clang-format -i
```

### Continuous Integration

The CI pipeline enforces quality standards by:
- Running all unit and integration tests
- Verifying code quality with static analysis
- Measuring code coverage
- Checking firmware size constraints
- Generating comprehensive reports

## Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Run the tests (`./test/run_unit_tests.sh`)
4. Commit your changes (`git commit -m 'Add some amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- SimulAVR Project for the emulator
- PlatformIO for the development environment
- Unity for the test framework