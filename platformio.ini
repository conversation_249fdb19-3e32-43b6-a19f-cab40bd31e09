; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
default_envs = attiny85
src_dir = src
include_dir = include
test_dir = test
extra_scripts = scripts/simulavr_script.py

[env]
test_framework = unity
test_build_src = yes
lib_extra_dirs = 
  lib
  src

[env:attiny85]
platform = atmelavr
board = attiny85
framework = arduino
board_build.f_cpu = 8000000L
upload_protocol = arduino
upload_flags = 
    -P$UPLOAD_PORT
    -b9600   ; Lower baud rate for more reliable programming
    -F  ; Force programming without checking device signature
    -v  ; Verbose output for better troubleshooting
upload_port = /dev/tty.usbmodemF412FA9B73C82  # Arduino R4 WiFi port
build_flags =
    -DATTINY85=1
    -DARDUINO_ARCH_AVR=1
    -std=gnu++11
    -fno-exceptions
    -D F_CPU=8000000L  ; 8MHz internal oscillator
    -DDEBUG_MODE=0     ; Set to 1 to enable debug output

; Configure the ATtiny85 fuses
board_fuses.lfuse = 0xE2
board_fuses.hfuse = 0xD7
board_fuses.efuse = 0xFF

[env:attiny85_usbasp]
platform = atmelavr
board = attiny85
framework = arduino
board_build.f_cpu = 8000000L
upload_protocol = usbasp
upload_flags =
    -F  ; Force programming without checking device signature
    -v  ; Verbose output for better troubleshooting
    -B 4  ; Slower programming speed for better reliability
build_flags =
    -DATTINY85=1
    -DARDUINO_ARCH_AVR=1
    -std=gnu++11
    -fno-exceptions
    -D F_CPU=8000000L  ; 8MHz internal oscillator
    -DDEBUG_MODE=0     ; Set to 1 to enable debug output

; Serial Monitor
monitor_speed = 9600

[env:native]
platform = native
build_flags =
    -std=gnu++11
    -fno-exceptions
    -DUNITY_INCLUDE_CONFIG_H
    -DNATIVE_TEST=1
    -I include
    -I test/mocks
build_src_filter =
    -<*>
    +<modules/timer_manager.cpp>
test_build_src = yes
lib_deps =
    throwtheswitch/Unity @ ^2.5.2
lib_compat_mode = off
test_ignore = integration
test_filter = test_timer_manager_enhanced
extra_scripts =
    pre:scripts/native_test_setup.py

[env:emulator]
platform = atmelavr
board = attiny85
framework = arduino
build_flags =
    -DATTINY85=1
    -DARDUINO_ARCH_AVR=1
    -DEMULATION_MODE=1
    -std=gnu++11
    -fno-exceptions
    -DUNITY_INCLUDE_CONFIG_H
    -I include
lib_deps =
    throwtheswitch/Unity @ ^2.5.2
lib_compat_mode = off
test_build_src = yes
test_filter = integration
extra_scripts = 
    pre:scripts/simulavr_script.py
test_speed = 0 
; Speed 0 allows us to use the SimulAVR timing instead of Unity's

; Testing-specific build options
[test]
build_flags =
    -DUNIT_TEST=1
    -DUNITY_INCLUDE_CONFIG_H
    -I include
lib_deps =
    throwtheswitch/Unity @ ^2.5.2
test_transport = custom
test_speed = 0
