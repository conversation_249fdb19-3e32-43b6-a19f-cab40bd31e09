# System Diagrams

This directory contains visual documentation of the ATtiny85 Control System architecture, data flows, state machines, and other important system aspects.

## Purpose

Visual representations of system architecture and behavior provide several benefits:

1. Enhanced understanding of complex systems and interactions
2. Faster onboarding of new team members
3. Improved communication between hardware and software teams
4. Clearer documentation of system behavior for maintenance and troubleshooting

## Diagram Types

### 1. Architecture Diagrams

Architecture diagrams show the high-level structure of the system, including major components, their relationships, and interfaces.

**Examples:**
- `system-architecture.svg` - Overall system architecture
- `hardware-connections.svg` - Physical connections between components
- `software-architecture.svg` - Software module organization

### 2. Data Flow Diagrams

Data flow diagrams illustrate how data moves through the system, from input sources to output destinations, including transformations along the way.

**Examples:**
- `pwm-input-processing.svg` - How PWM input signals are processed
- `control-signals.svg` - Flow of control signals to output devices

### 3. State Machines

State machine diagrams document the different states of the system or its components, transitions between states, and the events that trigger those transitions.

**Examples:**
- `system-state-machine.svg` - Main system state machine
- `pump-control-states.svg` - States and transitions for pump control
- `solenoid-timing-states.svg` - States and transitions for solenoid valve timing

### 4. Sequence Diagrams

Sequence diagrams show the time-ordered interactions between components or modules, illustrating the flow of operations and message passing.

**Examples:**
- `startup-sequence.svg` - System initialization sequence
- `error-handling-sequence.svg` - Error detection and recovery sequence

### 5. Timing Diagrams

Timing diagrams display the precise timing of signals and operations, especially important for interrupt-driven or real-time operations.

**Examples:**
- `interrupt-timing.svg` - Timing of interrupt handlers
- `pwm-response-timing.svg` - System response timing to PWM input changes

## File Organization

Diagrams are organized by type and function:

```
system-diagrams/
├── architecture/
│   ├── system-architecture.svg
│   ├── hardware-connections.svg
│   └── software-architecture.svg
├── data-flow/
│   ├── pwm-input-processing.svg
│   └── control-signals.svg
├── state-machines/
│   ├── system-state-machine.svg
│   ├── pump-control-states.svg
│   └── solenoid-timing-states.svg
├── sequences/
│   ├── startup-sequence.svg
│   └── error-handling-sequence.svg
└── timing/
    ├── interrupt-timing.svg
    └── pwm-response-timing.svg
```

## Diagram Standards

All diagrams should follow these standards:

1. **Format**: SVG preferred for version control and scalability
2. **Naming**: Clear, descriptive names using kebab-case
3. **Version**: Include version number and date in the diagram
4. **Legend**: Include a legend explaining symbols and notations
5. **References**: Include references to related code or documentation

## Recommended Tools

- [Draw.io](https://app.diagrams.net/) - Free online and desktop diagramming tool
- [PlantUML](https://plantuml.com/) - Text-based diagram generation
- [Lucidchart](https://www.lucidchart.com/) - Professional diagramming tool
- [Mermaid](https://mermaid-js.github.io/mermaid/) - JavaScript based diagramming and charting tool

## Example Diagram: System Architecture

```
┌─────────────────────────┐      ┌─────────────────────┐
│                         │      │                     │
│    External Control     │      │   ATtiny85 MCU      │
│    System               │      │                     │
│                         │      │  ┌───────────────┐  │
│  ┌─────────────────┐    │      │  │               │  │
│  │                 │    │      │  │  Timer        │  │
│  │  PWM Generator  ├────┼──────┼─►│  Manager      │  │
│  │                 │    │      │  │               │  │
│  └─────────────────┘    │      │  └───────┬───────┘  │
│                         │      │          │          │
└─────────────────────────┘      │          ▼          │
                                 │  ┌───────────────┐  │
                                 │  │               │  │     ┌───────────────┐
                                 │  │  Control      │  │     │               │
                                 │  │  Logic        ├──┼─────► Pump Relay    │
                                 │  │               │  │     │               │
                                 │  └───────┬───────┘  │     └───────────────┘
                                 │          │          │
                                 │          ▼          │     ┌───────────────┐
                                 │  ┌───────────────┐  │     │               │
                                 │  │               │  │     │ Air Solenoid  │
                                 │  │  Output       ├──┼─────► Valve         │
                                 │  │  Manager      │  │     │               │
                                 │  │               │  │     └───────────────┘
                                 │  └───────────────┘  │
                                 │                     │
                                 └─────────────────────┘
```

## Maintenance Guidelines

1. Update diagrams whenever significant architectural changes are made
2. Keep diagrams in sync with code implementation
3. Review diagrams as part of code review process
4. Include diagram references in relevant code comments
5. Use version control to track diagram changes over time 