# Knowledge Graph

This document provides a structured visualization of relationships between components, concepts, and processes in the ATtiny85 Control System project.

## Purpose

A knowledge graph serves to:

1. Visualize complex relationships between different elements of the system
2. Help identify dependencies and potential impacts of changes
3. Provide a holistic view of the project for new team members
4. Facilitate knowledge discovery and navigation
5. Identify potential knowledge gaps or overlapping areas

## Core Concepts

### System Components

- **Hardware Components**: Physical parts of the system
- **Software Modules**: Code components that implement functionality
- **External Systems**: Other systems that interact with our system
- **Documentation**: Information about the system
- **Tools**: Software used to develop, test, and deploy the system

### Relationship Types

- **Depends On**: Component A requires Component B to function
- **Implements**: Component A provides an implementation of Concept B
- **Controls**: Component A directly affects the state of Component B
- **Communicates With**: Component A exchanges data with Component B
- **Documents**: Documentation A describes Component/Concept B
- **Tests**: Test A verifies the functionality of Component B

## Knowledge Graph Structure

```
[Component/Concept] ---(Relationship)---> [Component/Concept]
```

## Core System Components

### Hardware Components

```
                          +------------+
                          |            |
                   +------|  ATtiny85  |------+
                   |      |            |      |
                   |      +------------+      |
                   |                          |
                   |                          |
                   v                          v
           +---------------+          +---------------+
           |               |          |               |
           |  Pump Relay   |          |  Air Solenoid |
           |               |          |               |
           +---------------+          +---------------+
                   ^                          ^
                   |                          |
                   |      +------------+      |
                   |      |            |      |
                   +------|  Control   |------+
                          |  System    |
                          |            |
                          +------------+
                                ^
                                |
                                |
                          +------------+
                          |            |
                          |    PWM     |
                          |   Input    |
                          |            |
                          +------------+
```

### Software Architecture

```
+-------------------+       +-------------------+       +-------------------+
|                   |       |                   |       |                   |
|  Input Processing |------>|  Control Logic    |------>|  Output Manager   |
|                   |       |                   |       |                   |
+-------------------+       +-------------------+       +-------------------+
         ^                          ^                          ^
         |                          |                          |
         |                  +---------------+                  |
         |                  |               |                  |
         +------------------|  Timer Manager|------------------+
                            |               |
                            +---------------+
                                    ^
                                    |
                            +---------------+
                            |               |
                            | Error Handler |
                            |               |
                            +---------------+
```

## Detailed Relationships

### Hardware Dependencies

- **ATtiny85** ---(Controls)---> **Pump Relay**
- **ATtiny85** ---(Controls)---> **Air Solenoid**
- **PWM Input** ---(Communicates With)---> **ATtiny85**
- **Arduino (as ISP)** ---(Programs)---> **ATtiny85**

### Software Dependencies

- **Timer Manager** ---(Depends On)---> **Arduino Core**
- **Control Logic** ---(Depends On)---> **Timer Manager**
- **Input Processing** ---(Depends On)---> **Timer Manager**
- **Output Manager** ---(Depends On)---> **Control Logic**
- **Error Handler** ---(Monitors)---> **Control Logic**
- **Error Handler** ---(Monitors)---> **Timer Manager**
- **Error Handler** ---(Monitors)---> **Input Processing**

### Development Toolchain

- **PlatformIO** ---(Builds)---> **Firmware**
- **AVR-GCC** ---(Compiles)---> **C/C++ Code**
- **Arduino IDE** ---(Programs)---> **Arduino (as ISP)**
- **avrdude** ---(Programs)---> **ATtiny85**
- **Simulavr** ---(Emulates)---> **ATtiny85**
- **Unity** ---(Tests)---> **Software Modules**

### Documentation Structure

- **Programming Guide** ---(Documents)---> **ATtiny85**
- **Programming Guide** ---(Documents)---> **Arduino (as ISP)**
- **Hardware Setup** ---(Documents)---> **Physical Components**
- **Deployment Strategy** ---(Documents)---> **Production Process**
- **System Diagrams** ---(Visualizes)---> **System Architecture**
- **Code Review Checklists** ---(Verifies)---> **Code Quality**

## Operational States and Transitions

```
                  +---------------+
                  |               |
            +---->|  Initializing |
            |     |               |
            |     +-------+-------+
            |             |
            |             v
            |     +---------------+
            |     |               |
            |     |     Idle      |<---------+
            |     |               |          |
            |     +-------+-------+          |
            |             |                  |
   Reset    |             v                  |
            |     +---------------+          |
            |     |               |          |
            |     |   Processing  |          |
            |     |     Input     |          |
            |     |               |          |
            |     +-------+-------+          |
            |             |                  |
            |             v                  |
            |     +---------------+          |
            |     |               |          |
            |     |   Activating  |          |
            |     |    Outputs    |          |
            |     |               |          |
            |     +-------+-------+          |
            |             |                  |
            |             v                  |
            |     +---------------+          |
            |     |               |          |
            |     |   Monitoring  |----------+
            |     |    Timing     |    Timeout
            |     |               |
            |     +-------+-------+
            |             |
            |             | Error
            |             v
            |     +---------------+
            |     |               |
            +-----|  Error State  |
                  |               |
                  +---------------+
```

## Knowledge Areas and Expertise Requirements

- **AVR Microcontroller Programming**
  - ATtiny85 architecture
  - Register manipulation
  - Interrupt handling
  - Memory constraints

- **Embedded System Design**
  - Real-time operations
  - Hardware interfacing
  - Power management
  - Timing considerations

- **Control System Theory**
  - State machines
  - Input signal processing
  - Output control algorithms
  - Fault detection

- **Development Tools**
  - PlatformIO ecosystem
  - Arduino framework
  - Version control
  - CI/CD pipelines

- **Hardware Integration**
  - Circuit design
  - Component selection
  - Signal integrity
  - Power requirements

## Resource Allocation and Constraints

- **Flash Memory (8KB)**
  - Firmware code
  - Constant data
  - Interrupt vectors

- **SRAM (512 bytes)**
  - Variables
  - Stack
  - I/O buffers

- **Processing Time**
  - Interrupt handling
  - Main loop execution
  - Response latency

- **Power Consumption**
  - Active mode
  - Sleep mode
  - I/O operations

## Maintaining the Knowledge Graph

1. Update the knowledge graph when:
   - New components are added to the system
   - Relationships between components change
   - New concepts or processes are introduced
   - System architecture is modified

2. Review the knowledge graph periodically to:
   - Verify accuracy of relationships
   - Identify potential knowledge gaps
   - Discover optimization opportunities
   - Improve documentation coverage

3. Use the knowledge graph during:
   - Onboarding of new team members
   - Planning of system modifications
   - Impact analysis of proposed changes
   - Root cause analysis of issues 