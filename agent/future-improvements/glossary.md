# Project Glossary

This document provides definitions for technical terms, acronyms, and project-specific vocabulary used throughout the ATtiny85 Control System documentation.

## Purpose

A centralized glossary serves to:

1. Ensure consistent terminology across all project documentation
2. Provide quick reference for team members and stakeholders
3. Facilitate onboarding of new team members
4. Reduce miscommunication and misinterpretation of technical concepts

## Technical Terms

### A

**ADC (Analog-to-Digital Converter)**  
A hardware component that converts analog signals (like voltage levels) to digital values. The ATtiny85 has a 10-bit ADC with 4 channels.

**Arduino as ISP**  
A method of using an Arduino board as an In-System Programmer for programming microcontrollers like the ATtiny85.

**AVR**  
A family of microcontrollers developed by Atmel (now Microchip), which includes the ATtiny85. AVR microcontrollers use a modified Harvard architecture with separate memories and buses for program and data.

**ATtiny85**  
An 8-bit AVR microcontroller with 8KB of flash memory, 512 bytes of SRAM, and 512 bytes of EEPROM, packaged in an 8-pin DIP or SOIC package.

**avrdude**  
A utility for downloading/uploading/manipulating the ROM and EEPROM contents of AVR microcontrollers.

### B

**Bootloader**  
A small program that runs when the microcontroller is powered on or reset, typically used to load and run the main application code.

**Brown-out Detection (BOD)**  
A circuit that monitors the supply voltage and generates a reset when the voltage drops below a set threshold, preventing erratic operation.

### C

**CI/CD (Continuous Integration/Continuous Deployment)**  
Automated processes for integrating code changes, running tests, and deploying firmware to target devices.

**Clock**  
A signal used to coordinate actions of digital circuits. The ATtiny85 can use an internal oscillator at 8MHz or an external crystal.

### D

**Duty Cycle**  
In PWM signals, the percentage of time the signal is in the active (high) state compared to the total period.

### E

**EEPROM (Electrically Erasable Programmable Read-Only Memory)**  
Non-volatile memory used to store small amounts of data that persists when power is removed. The ATtiny85 has 512 bytes of EEPROM.

**Emulation**  
Using software to simulate hardware behavior. In this project, Simulavr is used to emulate the ATtiny85.

### F

**Flash Memory**  
Non-volatile memory used to store the program code. The ATtiny85 has 8KB of flash memory.

**Fuse Bits**  
Configuration settings in AVR microcontrollers that determine fundamental operations like clock source, brown-out detection, and reset behavior.

### I

**I/O (Input/Output)**  
Pins on the microcontroller that can be programmed to either read external signals (input) or control external components (output).

**ISP (In-System Programming)**  
A method of programming microcontrollers while they are installed in a system, typically using SPI communication.

**ISR (Interrupt Service Routine)**  
A function that is executed when an interrupt occurs, temporarily pausing the normal program flow.

**Interrupt**  
A signal that causes the microcontroller to pause its current execution and handle a high-priority condition.

### M

**MCU (Microcontroller Unit)**  
An integrated circuit containing a processor core, memory, and programmable I/O peripherals.

### P

**PB0-PB5**  
Pin designations for the ATtiny85 I/O pins (Port B, pins 0-5).

**PCB (Printed Circuit Board)**  
A board that mechanically supports and electrically connects electronic components using conductive tracks.

**PlatformIO**  
An open-source ecosystem for IoT development that provides a unified workflow for building embedded systems.

**PWM (Pulse Width Modulation)**  
A technique for controlling the amount of power delivered to a device by rapidly switching between on and off states.

### R

**RESET**  
A pin or signal that brings the microcontroller back to its initial state.

### S

**SCK (Serial Clock)**  
The clock signal in SPI communication, generated by the master device.

**SPI (Serial Peripheral Interface)**  
A synchronous serial communication protocol used for short-distance communication, primarily in embedded systems.

**SRAM (Static Random Access Memory)**  
Volatile memory used for variable storage during program execution. The ATtiny85 has 512 bytes of SRAM.

### T

**Timer**  
A peripheral that counts clock pulses to measure time intervals and generate events at specified times.

## Project-Specific Terms

### A

**Air Solenoid**  
A valve controlled by an electromagnetic coil used in the system to control air flow.

### C

**Control System**  
The complete hardware and software system designed to control the pump and air solenoid based on input signals.

### D

**Deployment Procedure**  
The documented process for uploading verified firmware to production devices.

### E

**Error State**  
A system condition indicating that a fault has been detected, typically resulting in safe shutdown of outputs.

### F

**Firmware Version**  
The specific release of the software running on the ATtiny85, following semantic versioning (MAJOR.MINOR.PATCH).

### P

**Pump Relay**  
An electromagnetic switch used to control the high-power pump motor from the low-power microcontroller signal.

**PWM Input**  
The control signal received by the ATtiny85 to determine the desired operation mode and timing.

### S

**Solenoid Timing**  
The precise control of when the air solenoid is activated and for how long.

**System State**  
The current operating mode of the control system (e.g., idle, running, error).

### T

**Timeout**  
A safety feature that deactivates outputs after a predetermined period without valid input.

## Abbreviations

| Abbreviation | Full Form | Description |
|--------------|-----------|-------------|
| ADC | Analog-to-Digital Converter | Hardware for converting analog signals to digital values |
| BOD | Brown-out Detection | Circuit that monitors supply voltage |
| EEPROM | Electrically Erasable Programmable Read-Only Memory | Non-volatile memory for data storage |
| GPIO | General Purpose Input/Output | Pins that can be configured as inputs or outputs |
| I2C | Inter-Integrated Circuit | Serial communication protocol |
| IDE | Integrated Development Environment | Software application for code development |
| ISP | In-System Programming | Method for programming installed microcontrollers |
| ISR | Interrupt Service Routine | Function executed when an interrupt occurs |
| LED | Light Emitting Diode | Indicator light used for status display |
| MCU | Microcontroller Unit | Integrated circuit with processor, memory, and I/O |
| PCB | Printed Circuit Board | Board connecting electronic components |
| PWM | Pulse Width Modulation | Technique for power control using duty cycle |
| RISC | Reduced Instruction Set Computer | Processor architecture used in AVR |
| SCK | Serial Clock | Clock signal in SPI communication |
| SPI | Serial Peripheral Interface | Synchronous serial communication protocol |
| SRAM | Static Random Access Memory | Volatile memory for variable storage |
| TWI | Two-Wire Interface | Atmel's implementation of I2C protocol |
| UART | Universal Asynchronous Receiver/Transmitter | Serial communication hardware |

## Maintenance Guidelines

1. Add new terms as they are introduced in the project
2. Keep definitions concise and technically accurate
3. Review and update the glossary with each major release
4. Ensure consistency between terminology used in code comments, documentation, and UI
5. Link to relevant sections of the documentation or external resources where appropriate 