# Dependency Map

This document tracks all external dependencies for the ATtiny85 Control System, their versions, compatibility constraints, and upgrade paths.

## Purpose

The Dependency Map provides a centralized view of all the libraries, tools, and external components that the project depends on. This helps in:

1. Maintaining version consistency across environments
2. Planning for dependency upgrades
3. Identifying potential conflicts or compatibility issues
4. Documenting specific requirements for embedded hardware constraints

## Library Dependencies

| Library | Version | Purpose | Source | License | Hardware Constraints | Upgrade Notes |
|---------|---------|---------|--------|---------|---------------------|---------------|
| ArduinoCore-avr | 1.8.6 | Arduino core for AVR microcontrollers | [GitHub](https://github.com/arduino/ArduinoCore-avr) | LGPL 2.1 | Flash: 3.8KB, RAM: 260B | Test thoroughly with interrupt timing when upgrading |
| TimerLib | 1.3.0 | AVR timer management | [GitHub URL] | MIT | Flash: 512B, RAM: 24B | Pin compatibility issues with versions <1.2.0 |
| Unity | 2.5.2 | Unit testing framework | [GitHub](https://github.com/ThrowTheSwitch/Unity) | MIT | N/A (test only) | Major API changes in 3.x branch |

## Tool Dependencies

| Tool | Version | Purpose | Installation | Notes |
|------|---------|---------|-------------|-------|
| PlatformIO | 6.1.7 | Build system and IDE | `pip install platformio` | Config file format changed in 6.0+ |
| avrdude | 7.0 | AVR programmer | Included with PlatformIO | Command syntax changes in 7.x |
| AVR-GCC | 7.3.0 | C/C++ compiler for AVR | Included with PlatformIO | Memory optimization flags in newer versions |
| Simulavr | 1.1.0 | AVR simulator for testing | Custom build script | Requires Python 3.8+ |

## Hardware Dependencies

| Component | Version/Model | Interface | Alternative Options | Notes |
|-----------|---------------|-----------|---------------------|-------|
| ATtiny85 | PDIP/SOIC-8 | SPI for programming | ATtiny45 (less memory) | Must operate at 8MHz internal clock |
| Arduino (as ISP) | Any Arduino | SPI | USBasp, AVR Dragon | Upload ArduinoISP sketch first |
| External Crystal | 8MHz | Connect to pins 2-3 | Internal oscillator | Optional, improves timing precision |

## Dependency Graph

```
ATtiny85 Control System
├── Development Dependencies
│   ├── PlatformIO
│   │   ├── AVR-GCC
│   │   ├── avrdude
│   │   └── Arduino Framework
│   └── Unity Test Framework
│       └── Simulavr (integration tests)
│
├── Runtime Dependencies
│   ├── Arduino Core AVR
│   │   └── AVR Standard Library
│   ├── TimerLib
│   └── [Other Libraries]
│
└── Hardware Dependencies
    ├── ATtiny85 Microcontroller
    └── Programming Hardware
        └── Arduino (as ISP)
```

## Dependency Update Process

1. **Research**: Investigate new versions of dependencies for relevant changes
2. **Testing**: Test the updated dependency in a controlled environment
   - Unit tests pass
   - Integration tests pass
   - Memory usage within acceptable limits
   - Timing characteristics maintained
3. **Documentation**: Update this dependency map with new version and notes
4. **Implementation**: Update the dependency in the project
5. **Verification**: Verify that the system continues to function as expected

## Version Compatibility Matrix

| ATtiny85 Firmware Version | ArduinoCore-avr | TimerLib | Unity | PlatformIO | AVR-GCC |
|---------------------------|-----------------|----------|-------|------------|---------|
| 1.0.x | 1.8.3-1.8.6 | 1.2.x-1.3.x | 2.5.x | 5.x-6.x | 7.3.0 |
| 1.1.x | 1.8.5-1.8.6 | 1.3.x | 2.5.x | 6.x | 7.3.0 |
| 2.0.x | 1.8.6+ | 1.3.x+ | 2.5.x+ | 6.1.x+ | 7.3.0+ |

## Memory Usage Analysis

Memory constraints are critical for ATtiny85 development. This section tracks the flash and RAM usage of key dependencies.

### Flash Memory (8KB total on ATtiny85)

| Component | Size (bytes) | % of Available | Notes |
|-----------|--------------|----------------|-------|
| Arduino Core (minimal) | 1,824 | 22.3% | Required base functionality |
| TimerLib | 512 | 6.3% | Timer management |
| Application Code | 3,584 | 43.8% | Core functionality |
| Available | 2,272 | 27.7% | For future expansion |

### RAM Memory (512 bytes total on ATtiny85)

| Component | Size (bytes) | % of Available | Notes |
|-----------|--------------|----------------|-------|
| Arduino Core (minimal) | 96 | 18.8% | Static variables and runtime |
| TimerLib | 24 | 4.7% | Timer management data |
| Application Data | 298 | 58.2% | Runtime variables |
| Available | 94 | 18.4% | For future expansion |

## Critical Upgrade Notes

- **Arduino Core AVR**: Versions after 1.9.0 introduce changes in timer handling that may affect timing precision
- **TimerLib**: Version 2.0.0 anticipated in Q4 2023 with breaking API changes
- **AVR-GCC**: Version 8.x introduces new optimization options that could reduce code size by ~5%

## Update History

| Date | Dependency | Old Version | New Version | Notes |
|------|------------|-------------|-------------|-------|
| 2023-10-15 | Unity | 2.5.0 | 2.5.2 | Fixed memory leaks in test framework |
| 2023-11-01 | PlatformIO | 6.1.5 | 6.1.7 | Improved upload reliability |
| 2023-11-30 | TimerLib | 1.2.8 | 1.3.0 | Added power-saving features |

## Responsible Team Members

- **Dependency Manager**: [Name/Role]
- **Library Evaluation**: [Name/Role]
- **Compatibility Testing**: [Name/Role] 