# Code Review Checklists

This directory contains standardized checklists for conducting thorough code reviews of the ATtiny85 Control System codebase. These checklists are designed to ensure consistency, quality, and identify potential issues early in the development process.

## Purpose

Code review checklists help to:

1. Ensure consistent application of best practices
2. Reduce the cognitive load on reviewers by providing a structured approach
3. Cover all critical aspects of embedded software development
4. Improve code quality and maintainability
5. Prevent common issues specific to constrained embedded systems like the ATtiny85

## How to Use These Checklists

1. **Select the appropriate checklist** based on the type of code being reviewed (e.g., general C/C++ code, interrupt handlers, Arduino-specific code)
2. **Go through each item** in the checklist systematically
3. **Mark items as** ✅ (Pass), ❌ (Fail), or ➖ (Not Applicable)
4. **Add comments** for any failures, explaining the issue and suggesting improvements
5. **Create issues** for any significant problems found during review
6. **Attach the completed checklist** to the pull request or code review thread

## Available Checklists

### General Code Review Checklist
`general-code-review.md` - For reviewing any C/C++ code in the project.

### Specialized Checklists

- `interrupt-handler-review.md` - For reviewing interrupt service routines (ISRs)
- `memory-optimization-review.md` - For reviewing code with a focus on memory usage
- `power-efficiency-review.md` - For reviewing code with a focus on power consumption
- `timing-critical-review.md` - For reviewing code where timing is critical

## Sample Checklist: General Code Review

```markdown
# General Code Review Checklist

## Reviewer: [Name]
## PR/File(s): [Reference]
## Date: [YYYY-MM-DD]

### Functionality
- [ ] Code correctly implements the intended functionality
- [ ] Edge cases are handled appropriately
- [ ] Error conditions are detected and handled
- [ ] Timeout conditions are detected and handled
- [ ] Functions have clear, single responsibilities

### Memory Management
- [ ] Variables use appropriate data types (considering size constraints)
- [ ] No unnecessary global variables
- [ ] Stack usage is minimized for critical functions
- [ ] No potential stack overflows
- [ ] All pointers are checked before use

### Timing & Performance
- [ ] Critical paths avoid blocking operations
- [ ] Long-running operations are broken into smaller steps
- [ ] Interrupt handlers are kept as short as possible
- [ ] Appropriate use of volatile for shared variables
- [ ] No unnecessary operations in frequently executed code

### Code Structure & Style
- [ ] Follows project coding standards
- [ ] Functions are short and focused
- [ ] Variable and function names are clear and consistent
- [ ] Comments explain "why" not "what"
- [ ] No duplicated code

### Hardware Interaction
- [ ] Pin configurations are correct
- [ ] Hardware registers are properly initialized
- [ ] Critical sections are properly protected
- [ ] Peripheral usage is consistent with documentation
- [ ] No race conditions with hardware access

### Testing
- [ ] Unit tests cover normal operation
- [ ] Unit tests cover error conditions
- [ ] Tests for timing-critical code
- [ ] Memory usage is verified
- [ ] Documentation is updated to reflect changes

### Additional Comments
[Add any additional comments, concerns, or recommendations here]
```

## Creating New Checklists

To create a new specialized checklist:

1. Create a new markdown file in this directory named `[topic]-review.md`
2. Follow the existing template structure
3. Focus on items specific to the topic area
4. Include clear pass/fail criteria for each item
5. Avoid excessive length - aim for 20-30 items maximum
6. Add the new checklist to the "Available Checklists" section of this README

## Maintenance Guidelines

1. Review and update these checklists after each major release
2. Add new items based on issues discovered during development
3. Remove or modify items that are no longer relevant
4. Gather feedback from team members on checklist effectiveness
5. Track metrics on common issues found during code reviews to refine checklists 