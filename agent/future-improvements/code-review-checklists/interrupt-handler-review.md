# Interrupt Handler Code Review Checklist

## Reviewer: [Name]
## PR/File(s): [Reference]
## Date: [YYYY-MM-DD]

This checklist is specifically designed for reviewing Interrupt Service Routines (ISRs) and interrupt-related code in the ATtiny85 Control System. Due to the critical nature of interrupt handling in embedded systems, special attention must be paid to timing, side effects, and potential race conditions.

## Basic ISR Structure

- [ ] ISR is declared with the correct vector for the intended interrupt
- [ ] ISR is kept as short as possible (ideally < 20 instructions)
- [ ] ISR focuses only on essential processing that must be done immediately
- [ ] Non-critical processing is deferred to the main loop when possible
- [ ] ISR is annotated with `__attribute__((interrupt))` where appropriate

## Timing and Performance

- [ ] No blocking operations or delays within the ISR
- [ ] No floating-point arithmetic in the ISR
- [ ] No unnecessary loops or complex calculations
- [ ] Memory operations are minimized (especially writes)
- [ ] Function calls within ISR are avoided when possible
- [ ] If function calls are used, they are inline or very simple
- [ ] Total execution time of ISR is documented or measured
- [ ] Worst-case execution time is calculated and acceptable for the application

## Shared Data Access

- [ ] All variables shared between ISR and main code are declared `volatile`
- [ ] Atomic operations are used for multi-byte variables shared with main code
- [ ] Complex data structures are not modified directly by ISRs
- [ ] No dynamic memory allocation occurs in ISRs
- [ ] Critical sections in main code disable interrupts appropriately when accessing shared data
- [ ] Critical sections are kept as short as possible
- [ ] Re-entrancy issues are considered and addressed

## Interrupt Management

- [ ] Interrupts are only enabled after all peripherals are properly initialized
- [ ] Interrupt flags are properly cleared before re-enabling interrupts
- [ ] Nested interrupts are handled appropriately if used
- [ ] Interrupt priority is considered and correctly configured
- [ ] ISR correctly manages interrupt flags before returning

## Hardware Interaction

- [ ] Hardware registers are accessed correctly (atomic operations where needed)
- [ ] Register bit manipulation is done safely (read-modify-write concerns)
- [ ] Hardware limitations of peripherals during interrupts are considered
- [ ] Interrupts associated with unused peripherals are disabled
- [ ] Pin/port configurations match the hardware design

## Fault Tolerance

- [ ] ISR handles spurious interrupts gracefully
- [ ] Overflow/underflow conditions are prevented or detected
- [ ] Error conditions are detected and handled appropriately
- [ ] Watchdog timer is considered in the context of interrupt frequency
- [ ] System can recover from missing or extra interrupts

## Documentation and Comments

- [ ] ISR purpose is clearly documented
- [ ] Expected timing characteristics are documented
- [ ] Preconditions and postconditions are specified
- [ ] Shared variables and their usage patterns are documented
- [ ] Complex sequences or bit manipulations are explained

## Testing Considerations

- [ ] ISR functionality is verified via targeted unit tests
- [ ] Interrupt timing has been measured or simulated
- [ ] Edge cases have been tested (e.g., rapid trigger of interrupts)
- [ ] Interactions between multiple interrupts have been tested
- [ ] System behaves correctly during periods of high interrupt activity

## Special Concerns for ATtiny85

- [ ] Limited stack usage within ISR (ATtiny85 has only 512 bytes of SRAM)
- [ ] Appropriate use of sleep modes with interrupt wake-up
- [ ] Consideration of hardware limitations (e.g., single timer, limited pins)
- [ ] Pin change interrupt masks are correctly configured if used
- [ ] Register configurations account for the specific ATtiny85 hardware behavior

## Additional Comments

[Add any additional comments, observations, or concerns here]

## Reviewer Recommendation

- [ ] Approved - ISR implementation meets all safety and performance criteria
- [ ] Approved with minor concerns - Issues noted but not critical to functionality
- [ ] Needs revision - Significant issues need to be addressed before approval

[Explanation of decision] 