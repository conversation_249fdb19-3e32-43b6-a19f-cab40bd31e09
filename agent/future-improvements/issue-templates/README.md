# Issue Templates

This directory contains standardized templates for creating different types of issues for the ATtiny85 Control System project. Using consistent issue templates helps ensure that all necessary information is provided when reporting bugs, requesting features, or documenting technical debt.

## Purpose

Standardized issue templates serve several important functions:

1. Ensure all necessary information is provided upfront
2. Facilitate triage and prioritization
3. Create a consistent format for tracking issues
4. Improve communication between team members
5. Provide context for decision-making

## Available Templates

### Bug Report Template

`bug-report.md` - For reporting defects, unexpected behavior, or other issues with the existing system.

### Feature Request Template

`feature-request.md` - For requesting new functionality or significant enhancements to the system.

### Technical Debt Template

`technical-debt.md` - For documenting technical debt or areas needing refactoring.

### Hardware Issue Template

`hardware-issue.md` - For reporting issues specific to hardware interactions or compatibility.

### Performance Issue Template

`performance-issue.md` - For reporting issues related to timing, memory usage, or power consumption.

## Sample Template: Bug Report

```markdown
# Bug Report

## Description
[A clear and concise description of the bug]

## Environment
- Firmware Version: [e.g., 1.0.1]
- Hardware: [e.g., ATtiny85 PDIP]
- Programmer: [e.g., Arduino as ISP]
- Operating Conditions: [e.g., 5V power supply, room temperature]

## Steps to Reproduce
1. [First Step]
2. [Second Step]
3. [Additional Steps...]

## Expected Behavior
[What you expected to happen]

## Actual Behavior
[What actually happened]

## Diagnostic Information
- LED Status: [Pattern observed, if applicable]
- Measured Values: [Any measurements taken, if applicable]
- Debug Output: [Any debug output, if available]

## Possible Causes
[Any insights about what might be causing the issue]

## Possible Solutions
[Any suggestions for how to fix the issue]

## Additional Context
[Any other information that might be relevant]

## Screenshots/Waveforms
[If applicable, add screenshots or signal waveforms]
```

## Sample Template: Technical Debt

```markdown
# Technical Debt Issue

## Component/Module
[The specific component or module affected]

## Description
[A clear description of the technical debt]

## Impact
- **Reliability**: [High/Medium/Low]
- **Maintainability**: [High/Medium/Low]
- **Performance**: [High/Medium/Low]
- **Memory Usage**: [High/Medium/Low]
- **Power Efficiency**: [High/Medium/Low]

## Current Implementation
[Description of how it's currently implemented]

## Ideal Implementation
[Description of how it should be implemented]

## Refactoring Effort Estimate
[Small (1-2 days) / Medium (3-5 days) / Large (1-2 weeks+)]

## Risks
[Potential risks associated with refactoring]

## Dependencies
[Any other components or modules that would be affected]

## Proposed Timeline
[When this should be addressed]
```

## How to Use These Templates

When creating a new issue in the project repository:

1. Choose the appropriate template based on the type of issue
2. Fill out all relevant sections of the template
3. Be as specific and detailed as possible
4. Add labels that correspond to the issue type
5. Assign to appropriate team members if known

## Template Maintenance

These templates should be reviewed and updated periodically:

1. After major releases to incorporate any new requirements
2. When patterns of incomplete information are observed in submitted issues
3. When new types of issues or categories emerge

## GitHub Issue Template Integration

To configure these templates as GitHub issue templates:

1. Copy the templates to the `.github/ISSUE_TEMPLATE/` directory in the repository
2. Create a `config.yml` file in the same directory to configure the template chooser
3. Update template frontmatter to include appropriate metadata
4. Test the templates by creating new issues

Example `config.yml`:

```yaml
blank_issues_enabled: false
contact_links:
  - name: ATtiny85 Control System Documentation
    url: https://github.com/org/repo/docs
    about: Please check the documentation before opening an issue
```

Example template frontmatter:

```markdown
---
name: Bug Report
about: Report a bug in the ATtiny85 Control System
title: "[BUG] "
labels: bug, needs-triage
assignees: ""
---
``` 