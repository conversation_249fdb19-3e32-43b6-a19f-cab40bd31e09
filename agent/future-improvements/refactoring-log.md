# Refactoring Log

This document tracks technical debt, refactoring efforts, and code improvements for the ATtiny85 Control System over time. It serves as a living record of system evolution and architectural decisions.

## Purpose

The Refactoring Log helps to:

1. Track technical debt in a systematic way
2. Document the rationale for refactoring decisions
3. Prioritize refactoring efforts based on impact
4. Maintain a history of architectural improvements
5. Provide context for future developers about past decisions

## Active Technical Debt Items

| ID | Description | Impact | Effort | Priority | Created | Owner |
|----|------------|--------|--------|----------|---------|-------|
| TD001 | Timer interrupt handler has grown too complex | High (affects timing reliability) | Medium (2-3 days) | High | 2023-10-15 | [Name] |
| TD002 | PWM input detection uses polling instead of interrupts | Medium (wastes CPU cycles) | Low (1 day) | Medium | 2023-10-20 | [Name] |
| TD003 | Global variables used for system state | Medium (makes testing difficult) | High (4-5 days) | Medium | 2023-11-01 | [Name] |
| TD004 | Inconsistent error handling across modules | Low (potential for missed errors) | Medium (2-3 days) | Low | 2023-11-10 | [Name] |
| TD005 | Hard-coded timing values | Medium (makes adjustments difficult) | Low (1 day) | Medium | 2023-11-15 | [Name] |

## Completed Refactorings

| ID | Description | Impact | Effort | Completion Date | Release Version | Notes |
|----|------------|--------|--------|-----------------|-----------------|-------|
| RF001 | Extracted timer management into dedicated module | High (improved maintainability) | Medium | 2023-09-05 | 1.0.0 | Reduced main loop complexity by 35% |
| RF002 | Replaced busy-wait delays with interrupt-driven timing | High (improved responsiveness) | High | 2023-09-15 | 1.0.0 | System now responds to inputs during timing operations |
| RF003 | Implemented state machine for control logic | High (improved reliability) | High | 2023-09-25 | 1.0.0 | Clearer control flow, easier to extend |
| RF004 | Optimized memory usage in status reporting | Medium (saved 32 bytes RAM) | Low | 2023-10-10 | 1.0.1 | Freed up memory for error handling |
| RF005 | Consolidated duplicate pin control functions | Low (reduced code size) | Low | 2023-10-25 | 1.0.2 | Simplified output control logic |

## Planned Refactorings

| ID | Description | Associated Technical Debt | Target Release | Prerequisites | Notes |
|----|------------|--------------------------|----------------|---------------|-------|
| PL001 | Refactor timer interrupt handler | TD001 | 1.1.0 | None | Split into smaller, focused handlers |
| PL002 | Implement interrupt-based PWM detection | TD002 | 1.1.0 | None | Use pin change interrupts |
| PL003 | Replace global state with state manager | TD003 | 1.2.0 | PL001, PL002 | Design encapsulated state management |
| PL004 | Standardize error handling | TD004 | 1.2.0 | PL003 | Create unified error handling system |
| PL005 | Create configuration module for timing values | TD005 | 1.1.0 | None | Move hardcoded values to config.h |

## Refactoring Prioritization Guidelines

Refactoring efforts are prioritized based on the following criteria:

1. **Impact on Reliability** - Issues affecting system reliability are highest priority
2. **Memory Efficiency** - Improvements that free up memory are high priority given ATtiny85 constraints
3. **Maintainability** - Changes that significantly improve code maintainability
4. **Performance** - Optimizations that reduce execution time in critical paths
5. **Power Efficiency** - Improvements that reduce power consumption
6. **Code Size** - Reductions in flash memory usage
7. **Developer Experience** - Improvements that make the codebase easier to work with

## Refactoring Process

When implementing refactorings, follow this process:

1. **Documentation** - Update this log with planned refactoring details
2. **Branch Creation** - Create a dedicated branch for the refactoring effort
3. **Tests First** - Ensure test coverage exists before refactoring
4. **Incremental Changes** - Make small, focused changes with frequent testing
5. **Review** - Conduct thorough code review with specific attention to the refactoring goals
6. **Integration** - Merge the refactoring branch and verify system behavior
7. **Update** - Mark the refactoring as complete in this log

## Code Smell Inventory

This section tracks identified "code smells" that may indicate need for future refactoring:

| ID | Code Smell | Location | Impact | Notes |
|----|------------|----------|--------|-------|
| CS001 | Long function | `main.cpp:handle_input()` | Medium | Function has grown to 75 lines |
| CS002 | Shotgun surgery | Pin configuration | High | Changes require updates in multiple files |
| CS003 | Duplicate code | Error handling | Medium | Similar patterns repeated across modules |
| CS004 | Feature envy | `timer.cpp:check_system_state()` | Low | Function uses more external data than internal |
| CS005 | Primitive obsession | Timing parameters | Medium | Using raw integers instead of meaningful structures |

## Architecture Evolution

This section documents significant architectural changes over time:

### Version 1.0.0 (2023-09-30) - Initial Architecture

```
ATtiny85 Control System
├── main.cpp (Central control logic)
├── timer.cpp (Basic timing operations)
├── input.cpp (Input signal processing)
└── output.cpp (Output control)
```

### Version 1.1.0 (Planned) - Modular Architecture

```
ATtiny85 Control System
├── main.cpp (System initialization and main loop)
├── modules/
│   ├── timer_manager.cpp (Enhanced timing with interrupts)
│   ├── input_processor.cpp (Interrupt-based input handling)
│   ├── state_machine.cpp (Formalized system states)
│   ├── output_controller.cpp (Output management)
│   └── error_handler.cpp (Centralized error handling)
└── config.h (System configuration parameters)
```

## Maintenance Guidelines

1. Update this log when identifying new technical debt
2. Review open technical debt items during sprint planning
3. Mark completed refactorings with actual effort and impact assessments
4. Periodically review and consolidate similar technical debt items
5. Use this log during code reviews to identify patterns in technical debt
6. Maintain a healthy ratio of new features to refactoring efforts 