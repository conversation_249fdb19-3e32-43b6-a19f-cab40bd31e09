# Codebase Structure

This document provides a hierarchical representation of the project's file and directory structure.

## Project Root

```
attiny-control/
├── .github/
│   └── workflows/         # CI/CD workflow definitions (to be implemented)
├── .cursor/               # Cursor-specific files
│   └── rules/             # Cursor rules
├── agent/                 # Documentation and agent files
│   ├── credentials/       # Credential storage (gitignored)
    ├── prompts/           # Feature prompt files ( these guide feature implementation )
│   ├── component-registry.md
│   ├── codebase-structure.md
│   ├── context-memory.md
│   ├── development-notes.md
│   ├── documentation-reviews.md
│   ├── implementation-plan.md
│   ├── requests_to_human.md
│   ├── test-coverage.md
│   └── version-history.md
├── include/               # Header files
├── lib/                   # Project-specific libraries
├── src/                   # Source code
│   ├── main.cpp           # Main application entry point (to be created from main.ino)
│   └── modules/           # Modular components (to be created)
├── test/                  # Test files
│   ├── unit/              # Unit tests (to be implemented)
│   └── integration/       # Integration tests (to be implemented)
├── .gitignore             # Git ignore file
├── platformio.ini         # PlatformIO configuration
└── README.md              # Project documentation
```

## Module Dependencies

No module dependencies defined yet.

## Component Relationships

No component relationships defined yet. 