# Test Coverage Documentation

This document tracks test coverage for all implemented features and components.

## Unit Tests

The following unit tests are implemented and verified:

- **Pin Configuration** (`test_pin_config.cpp`):
  - Pin constant values
  - Pin initialization
  - Initial pin states
  - Pin range validity

- **PWM Control** (`test_pwm_control.cpp`):
  - PWM initialization
  - Duty cycle reading
  - Output setting with and without capping
  - Processing input to output with different duty cycles
  - Custom maximum output values

- **Timer Manager** (`test_timer_manager.cpp` and `test_timer_manager_enhanced.cpp`):
  - Timer initialization
  - Interrupt counting
  - Counter reset
  - Maximum interrupt count validation
  - ISR behavior
  - Timeout behavior

- **Air Control** (`test_air_control.cpp`):
  - Initialization from EEPROM
  - On/off functionality
  - Status tracking
  - Process logic based on sonicator input
  - ISR callback behavior

- **Pump Control** (`test_pump_control.cpp`):
  - Pump initialization
  - On/off functionality
  - Status tracking
  - Process logic based on duty cycle thresholds
  - Threshold boundary testing

## Integration Tests

Integration tests are implemented in the following files:

- `test_integration_basic.cpp`: 
  - System initialization
  - PWM input response
  - Air pressure timeout
  - Error recovery

- `test_system_integration.cpp`:
  - Normal operation tests
  - High duty cycle behavior
  - Low duty cycle behavior
  - Air timeout functionality
  - State transition testing

## Emulation Tests

Emulation tests using SimulAVR are configured and implemented:

- System behavior in emulated environment
- Waveform capture and analysis
- Mock hardware interfaces for testing
- Simulated timer interrupts

## Mock Implementations

The following mock implementations support the test environment:

- **Arduino.h/Arduino.cpp**: Mock Arduino functions and hardware
- **EEPROM.h/EEPROM.cpp**: Mock EEPROM for persistent storage
- **ATTINY85_emulator.h**: Hardware emulation utilities

## Test Metrics

Current test coverage is estimated at:
- Unit Tests: ~85% of functions
- Integration Tests: ~70% of system functionality
- Emulation Tests: ~60% of hardware interactions

## Test Environment

- Test framework: Unity
- Emulation framework: SimulAVR
- Mock implementations: Arduino, EEPROM, hardware interfaces

## Test Execution

```bash
# Run unit tests
platformio test -e native

# Run integration tests with hardware emulation
platformio test -e emulator

# Run a specific test
platformio test -e native -f test_pump_control

# Run all tests with detailed output
platformio test -v
``` 