# Unit Testing Plan

This document outlines the test strategy for the ATTINY85 Control System, detailing the approach for unit testing each component.

## Testing Architecture

### Testing Framework
- **Unity**: Used for writing and running C/C++ unit tests
- **PlatformIO Test Runner**: Manages test execution and reporting

### Test Environment
- **Native Tests**: Runs on host machine for rapid feedback
- **Hardware-specific Tests**: Runs on real ATTINY85 or emulator

### Mocking Strategy
- **Arduino Mocks**: Simulates Arduino functions and hardware behavior
- **Test Helper Classes**: Provides controlled access to private module state
- **External Dependency Mocks**: Simulates behavior of external libraries like EEPROM

## Module Test Coverage

### PinConfig Module
- **Test Files**: `test/unit/test_pin_config.cpp`
- **Coverage**:
  - Pin constant values
  - Pin initialization behavior

### PwmControl Module
- **Test Files**: `test/unit/test_pwm_control.cpp`
- **Coverage**:
  - PWM initialization
  - Duty cycle reading
  - Output setting with and without capping
  - Input-to-output processing

### TimerManager Module
- **Test Files**: `test/unit/test_timer_manager.cpp`
- **Coverage**:
  - Timer initialization
  - Interrupt count management
  - Counter reset functionality
  - Maximum interrupt count validation
  - Interrupt Service Routine behavior

### AirControl Module
- **Test Files**: `test/unit/test_air_control.cpp`
- **Coverage**:
  - Air control initialization
  - On/off functionality
  - Status tracking
  - Process logic based on sonicator input
  - EEPROM state persistence
  - ISR callback behavior

### PumpControl Module
- **Test Files**: `test/unit/test_pump_control.cpp`
- **Coverage**:
  - Pump initialization
  - On/off functionality
  - Status tracking
  - Process logic based on duty cycle thresholds

### System Module
- **Test Files**: `test/unit/test_system.cpp`
- **Coverage**:
  - System initialization
  - Main processing loop
  - Integration with other modules

## Integration Testing

### System Integration Tests
- **Test Files**: `test/integration/test_system_integration.cpp`
- **Coverage**:
  - End-to-end system behavior
  - Module interaction validation
  - State transition testing

## Test Execution

### Running Tests
- Run all tests: `pio test`
- Run specific test: `pio test -f <test_name>`
- Run with specific environment: `pio test -e <environment>`

### Test Output
- Test results are reported in the terminal
- Pass/fail status for each test case
- Execution time metrics

## Test Maintenance

### Adding New Tests
1. Create test file in appropriate directory
2. Include Unity framework and module header files
3. Define test cases and assertions
4. Add entry point with RUN_TEST calls

### Mocking New Dependencies
1. Add mock declarations to Arduino.h or create new mock header
2. Implement mock behavior in corresponding .cpp file
3. Add reset functionality in ArduinoMock::reset()

## Future Improvements

- Add code coverage reporting
- Implement property-based testing for complex behaviors
- Add continuous integration testing with GitHub Actions
- Implement simulavr-based hardware emulation testing 