## Purpose
Implement comprehensive monitoring and alerting systems for the ATTINY control firmware development process, CI/CD pipeline, and deployed devices.

## Instructions

1. **GitHub Repository Monitoring**
   - Set up branch protection rules to enforce quality controls
   - Configure status checks to prevent merging failing code

   ```bash
   # Using GitHub CLI to set up branch protection (can also be done via web UI)
   gh api repos/sboyett31/attiny-control/branches/main/protection \
     -X PUT \
     -F required_status_checks.strict=true \
     -F required_status_checks.contexts[]="build-and-test" \
     -F required_status_checks.contexts[]="emulation-test" \
     -F required_pull_request_reviews.required=true \
     -F required_pull_request_reviews.required_approving_review_count=1 \
     -F enforce_admins=true
   ```

2. **Build and Test Monitoring**
   - Implement status badges in README.md
   - Set up monitoring for CI/CD pipeline performance

   ```markdown
   # Add to README.md
   ## Build Status
   
   [![Build Status](https://github.com/sboyett31/attiny-control/workflows/ATTINY%20Control%20CI/CD%20Pipeline/badge.svg)](https://github.com/sboyett31/attiny-control/actions)
   [![Code Coverage](https://codecov.io/gh/sboyett31/attiny-control/branch/main/graph/badge.svg)](https://codecov.io/gh/sboyett31/attiny-control)
   ```

   ```yaml
   # Add to .github/workflows/ci-cd.yml
   - name: Upload coverage to Codecov
     uses: codecov/codecov-action@v2
     with:
       files: ./build/coverage.xml
       flags: unittests
       fail_ci_if_error: true
   ```

3. **Code Quality Monitoring**
   - Integrate static code analysis tools
   - Set up monitoring for code quality metrics

   ```yaml
   # Add to .github/workflows/ci-cd.yml
   - name: Run static analysis
     run: |
       # Install cppcheck
       apt-get update && apt-get install -y cppcheck
       
       # Run cppcheck on source files
       cppcheck --enable=all --xml --xml-version=2 src/ 2> cppcheck-result.xml
       
       # Convert to format for visualization
       mkdir -p build/reports
       python3 tools/cppcheck_report.py cppcheck-result.xml build/reports/cppcheck.html
   
   - name: Upload code analysis results
     uses: actions/upload-artifact@v3
     with:
       name: code-analysis
       path: build/reports/
   ```

   ```python
   # tools/cppcheck_report.py
   import sys
   import xml.etree.ElementTree as ET
   
   def convert_to_html(xml_file, html_file):
       tree = ET.parse(xml_file)
       root = tree.getroot()
       
       with open(html_file, 'w') as f:
           f.write('<!DOCTYPE html>\n')
           f.write('<html lang="en">\n')
           f.write('<head>\n')
           f.write('  <meta charset="UTF-8">\n')
           f.write('  <title>Cppcheck Analysis Results</title>\n')
           f.write('  <style>\n')
           f.write('    body { font-family: Arial, sans-serif; }\n')
           f.write('    .error { color: red; }\n')
           f.write('    .warning { color: orange; }\n')
           f.write('    .style { color: blue; }\n')
           f.write('    .performance { color: purple; }\n')
           f.write('    .information { color: green; }\n')
           f.write('  </style>\n')
           f.write('</head>\n')
           f.write('<body>\n')
           f.write('  <h1>Cppcheck Analysis Results</h1>\n')
           f.write('  <table border="1" cellspacing="0" cellpadding="5">\n')
           f.write('    <tr><th>File</th><th>Line</th><th>Type</th><th>Message</th></tr>\n')
           
           for error in root.findall('.//error'):
               severity = error.get('severity')
               msg = error.get('msg')
               
               for location in error.findall('location'):
                   file = location.get('file')
                   line = location.get('line')
                   
                   f.write(f'    <tr class="{severity}">\n')
                   f.write(f'      <td>{file}</td>\n')
                   f.write(f'      <td>{line}</td>\n')
                   f.write(f'      <td>{severity}</td>\n')
                   f.write(f'      <td>{msg}</td>\n')
                   f.write('    </tr>\n')
           
           f.write('  </table>\n')
           f.write('</body>\n')
           f.write('</html>\n')
   
   if __name__ == '__main__':
       if len(sys.argv) != 3:
           print('Usage: python3 cppcheck_report.py <xml_file> <html_file>')
           sys.exit(1)
       
       convert_to_html(sys.argv[1], sys.argv[2])
   ```

4. **Test Coverage Monitoring**
   - Implement test coverage tracking
   - Set up monitoring for test coverage metrics

   ```cmake
   # Add to CMakeLists.txt
   if(CMAKE_COMPILER_IS_GNUCXX)
     include(CodeCoverage)
     append_coverage_compiler_flags()
     setup_target_for_coverage_lcov(
       NAME coverage
       EXECUTABLE ctest -j ${PROCESSOR_COUNT}
       EXCLUDE "tests/*" "/usr/*"
     )
   endif()
   ```

   ```yaml
   # Add to .github/workflows/ci-cd.yml
   - name: Generate test coverage
     run: |
       cd build
       make coverage
       
   - name: Upload coverage report
     uses: actions/upload-artifact@v3
     with:
       name: coverage-report
       path: build/coverage/
   ```

5. **Pipeline Performance Monitoring**
   - Create metrics for CI/CD pipeline performance
   - Set up alerts for pipeline failures or degradations

   ```javascript
   // tools/ci-metrics.js
   const fs = require('fs');
   const { Storage } = require('@google-cloud/storage');
   const { BigQuery } = require('@google-cloud/bigquery');
   
   // Initialize clients
   const storage = new Storage();
   const bigquery = new BigQuery();
   
   async function recordBuildMetrics() {
     const buildId = process.env.GITHUB_RUN_ID;
     const buildNumber = process.env.GITHUB_RUN_NUMBER;
     const commit = process.env.GITHUB_SHA;
     const branch = process.env.GITHUB_REF.replace('refs/heads/', '');
     const startTime = new Date(process.env.BUILD_START_TIME);
     const endTime = new Date();
     const duration = (endTime - startTime) / 1000; // in seconds
     
     // Read test results
     const testResults = JSON.parse(fs.readFileSync('build/test-results.json'));
     
     // Record metrics to BigQuery
     await bigquery.dataset('ci_metrics').table('builds').insert([
       {
         build_id: buildId,
         build_number: parseInt(buildNumber),
         commit,
         branch,
         start_time: startTime.toISOString(),
         end_time: endTime.toISOString(),
         duration,
         status: process.env.BUILD_STATUS,
         test_count: testResults.total,
         test_passed: testResults.passed,
         test_failed: testResults.failed,
         test_coverage: testResults.coverage
       }
     ]);
     
     console.log('CI metrics recorded successfully');
   }
   
   recordBuildMetrics().catch(err => {
     console.error('Error recording CI metrics:', err);
     process.exit(1);
   });
   ```

   ```yaml
   # Add to .github/workflows/ci-cd.yml
   - name: Record build metrics
     run: |
       npm install @google-cloud/storage @google-cloud/bigquery
       export BUILD_START_TIME="${{ steps.build-start.outputs.time }}"
       export BUILD_STATUS="${{ job.status }}"
       node tools/ci-metrics.js
     env:
       GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_SA_KEY }}
   ```

6. **Deployment Monitoring**
   - Track firmware versions in production
   - Monitor deployment success rates

   ```javascript
   // tools/deployment-tracker.js
   const { Firestore } = require('@google-cloud/firestore');
   const { BigQuery } = require('@google-cloud/bigquery');
   
   const firestore = new Firestore();
   const bigquery = new BigQuery();
   
   async function trackDeployment() {
     const version = process.env.VERSION;
     const environment = process.env.ENVIRONMENT || 'production';
     const timestamp = new Date();
     const buildId = process.env.GITHUB_RUN_ID;
     const status = process.env.DEPLOY_STATUS;
     
     // Record deployment in Firestore
     await firestore.collection('deployments').add({
       version,
       environment,
       timestamp,
       buildId,
       status,
       commit: process.env.GITHUB_SHA,
       deployer: process.env.GITHUB_ACTOR
     });
     
     // Record metrics in BigQuery
     await bigquery.dataset('ci_metrics').table('deployments').insert([
       {
         version,
         environment,
         timestamp: timestamp.toISOString(),
         build_id: buildId,
         status,
         commit: process.env.GITHUB_SHA
       }
     ]);
     
     console.log(`Deployment of ${version} to ${environment} tracked successfully`);
   }
   
   trackDeployment().catch(err => {
     console.error('Error tracking deployment:', err);
     process.exit(1);
   });
   ```

7. **Alert Configuration**
   - Set up email alerts for critical failures
   - Configure Slack notifications for build events

   ```yaml
   # Add to .github/workflows/ci-cd.yml
   - name: Send Slack notification
     uses: 8398a7/action-slack@v3
     with:
       status: ${{ job.status }}
       fields: repo,message,commit,author,action,eventName,ref,workflow
     env:
       SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
     if: always()
   
   - name: Send email on failure
     uses: dawidd6/action-send-mail@v3
     with:
       server_address: smtp.gmail.com
       server_port: 465
       username: ${{ secrets.EMAIL_USERNAME }}
       password: ${{ secrets.EMAIL_PASSWORD }}
       subject: "❌ ATTINY Control Build Failed"
       body: |
         The build for ATTINY Control has failed.
         
         Repository: ${{ github.repository }}
         Workflow: ${{ github.workflow }}
         Run: ${{ github.run_id }}
         Commit: ${{ github.sha }}
         
         See details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
       to: <EMAIL>
       from: ATTINY Control CI
     if: failure()
   ```

8. **Real-time Monitoring Dashboard**
   - Implement a monitoring dashboard in Google Cloud
   - Configure metrics visualization and alerts

   ```bash
   # Create a Cloud Monitoring dashboard using gcloud
   cat > dashboard.json << EOF
   {
     "displayName": "ATTINY Control CI/CD Dashboard",
     "gridLayout": {
       "columns": "2",
       "widgets": [
         {
           "title": "Build Success Rate",
           "xyChart": {
             "dataSets": [
               {
                 "timeSeriesQuery": {
                   "timeSeriesFilter": {
                     "filter": "metric.type=\"custom.googleapis.com/ci/build/success_rate\"",
                     "aggregation": {
                       "perSeriesAligner": "ALIGN_MEAN",
                       "crossSeriesReducer": "REDUCE_MEAN",
                       "groupByFields": []
                     }
                   }
                 }
               }
             ]
           }
         },
         {
           "title": "Test Coverage",
           "xyChart": {
             "dataSets": [
               {
                 "timeSeriesQuery": {
                   "timeSeriesFilter": {
                     "filter": "metric.type=\"custom.googleapis.com/ci/test/coverage\"",
                     "aggregation": {
                       "perSeriesAligner": "ALIGN_MEAN",
                       "crossSeriesReducer": "REDUCE_MEAN",
                       "groupByFields": []
                     }
                   }
                 }
               }
             ]
           }
         }
         // More widgets...
       ]
     }
   }
   EOF
   
   gcloud monitoring dashboards create --config-from-file=dashboard.json
   ```

9. **Device Status Monitoring**
   - Implement a device status dashboard
   - Set up alerting for offline or malfunctioning devices

   ```javascript
   // functions/device-monitor/index.js
   const { BigQuery } = require('@google-cloud/bigquery');
   const { Firestore } = require('@google-cloud/firestore');
   const { v4: uuidv4 } = require('uuid');
   
   const bigquery = new BigQuery();
   const firestore = new Firestore();
   
   exports.recordDeviceHeartbeat = async (req, res) => {
     // Enable CORS
     res.set('Access-Control-Allow-Origin', '*');
     
     if (req.method === 'OPTIONS') {
       res.set('Access-Control-Allow-Methods', 'POST');
       res.set('Access-Control-Allow-Headers', 'Content-Type');
       res.status(204).send('');
       return;
     }
     
     // Validate request
     const {deviceId, firmwareVersion, status, metrics} = req.body;
     if (!deviceId || !firmwareVersion) {
       res.status(400).send('Missing required parameters');
       return;
     }
     
     const timestamp = new Date();
     const heartbeatId = uuidv4();
     
     try {
       // Record heartbeat in Firestore
       await firestore.collection('devices').doc(deviceId).set({
         lastHeartbeat: timestamp,
         firmwareVersion,
         status: status || 'online',
         metrics: metrics || {}
       }, {merge: true});
       
       // Record heartbeat in BigQuery for analytics
       await bigquery.dataset('device_metrics').table('heartbeats').insert([{
         heartbeat_id: heartbeatId,
         device_id: deviceId,
         firmware_version: firmwareVersion,
         timestamp: timestamp.toISOString(),
         status: status || 'online',
         ...metrics
       }]);
       
       res.status(200).json({
         success: true,
         timestamp: timestamp.toISOString(),
         heartbeatId
       });
     } catch (error) {
       console.error('Error recording device heartbeat:', error);
       res.status(500).send('Failed to record heartbeat');
     }
   };
   ```

10. **Monitoring Service Deployment**
    - Deploy the monitoring stack to Google Cloud
    - Configure access controls and security

    ```yaml
    # functions/device-monitor/deployment.yaml
    name: device-monitor
    runtime: nodejs14
    entryPoint: recordDeviceHeartbeat
    trigger:
      httpsTrigger:
        securityLevel: SECURE_ALWAYS
    ```

## Verification
- Verify GitHub branch protection is correctly configured
- Confirm CI/CD metrics are properly recorded
- Test email and Slack notifications
- Validate monitoring dashboard functionality
- Verify alert system triggers on failures
- Test real-time device status monitoring

## Next Steps
- After setting up monitoring and alerts, proceed to `9-final-integration-testing.md`