## Purpose
<PERSON><PERSON><PERSON> and organize the ATTINY85 control code into a modular, testable structure following object-oriented principles.

## Instructions

1. **Code Modularization**
   - Separate the monolithic `pwm_production.c` into logical modules
   - Create header files for each module with proper include guards
   - Implement proper function declarations and definitions

2. **Core Modules to Create**

   a. **PWM Control Module** (`pwm_control.h/c`)
   ```c
   // pwm_control.h
   #ifndef PWM_CONTROL_H
   #define PWM_CONTROL_H

   // PWM signal management functions
   byte GetPWM(byte pin);
   void initTimerCounter0(void);
   void flow_pump_control(void);

   #endif
   ```

   b. **Air Pressure Module** (`air_pressure.h/c`)
   ```c
   // air_pressure.h
   #ifndef AIR_PRESSURE_H
   #define AIR_PRESSURE_H

   // Air pressure control functions
   void turn_air_on(void);
   void turn_air_off(void);
   bool is_air_on(void);
   void air_pressure_control(void);
   void initialize_air(void);
   
   #endif
   ```

   c. **Pump Control Module** (`pump_control.h/c`)
   ```c
   // pump_control.h
   #ifndef PUMP_CONTROL_H
   #define PUMP_CONTROL_H

   // Pump control functions
   void turn_pump_on(void);
   void turn_pump_off(void);
   bool is_pump_on(void);
   void initialize_pump(void);
   
   #endif
   ```

   d. **Sonicator Interface Module** (`sonicator.h/c`)
   ```c
   // sonicator.h
   #ifndef SONICATOR_H
   #define SONICATOR_H

   // Sonicator detection functions
   bool is_sonicator_on(void);
   
   #endif
   ```

   e. **Timer Module** (`timer.h/c`)
   ```c
   // timer.h
   #ifndef TIMER_H
   #define TIMER_H

   // Timer initialization and interrupt handler
   void initTimerCounter1(void);
   // Declare ISR in implementation file
   
   #endif
   ```

   f. **EEPROM Management Module** (`eeprom_manager.h/c`)
   ```c
   // eeprom_manager.h
   #ifndef EEPROM_MANAGER_H
   #define EEPROM_MANAGER_H

   // EEPROM functions
   void update_air_status_eeprom(void);
   void read_eeprom(void);
   
   #endif
   ```

   g. **System Initialization Module** (`system_init.h/c`)
   ```c
   // system_init.h
   #ifndef SYSTEM_INIT_H
   #define SYSTEM_INIT_H

   // System initialization functions
   void initialize_variables(void);
   void initialize_chip(void);
   
   #endif
   ```

   h. **Configuration Module** (`config.h`)
   ```c
   // config.h
   #ifndef CONFIG_H
   #define CONFIG_H

   // Pin definitions
   #define AIR_OUT_PIN 0      // PIN PB0
   #define PWM_OUT_PIN 1      // PIN PB1
   #define PUMP_OUT_PIN 2     // PIN PB2
   #define SONIC_IN_PIN 3     // PIN PB3
   #define PWM_IN_PIN 4       // PIN PB4
   #define STATUS_OUT_PIN 5   // PIN PB5

   // Configuration constants
   #define INTERRUPT_COUNT_MAX 579
   #define PWM_DUTY_CYCLE_CAP 77
   #define PWM_PUMP_THRESHOLD 94

   #endif
   ```

   i. **Main Application** (`main.c`)
   ```c
   // main.c
   #include "config.h"
   #include "system_init.h"
   #include "pwm_control.h"
   #include "air_pressure.h"
   #include "eeprom_manager.h"

   int main(void) {
     // Chip starts
     read_eeprom(); // read eeprom status on restart
     initialize_chip();

     while (1) {
       /* System Control Loop */
       air_pressure_control();
       flow_pump_control();
     }
   }
   ```

3. **Global Variables Management**
   - Move global variables to appropriate modules
   - Create accessor functions where needed
   - Use extern declarations in headers when necessary

4. **Documentation**
   - Add descriptive comments for each function
   - Document the purpose of each module
   - Create proper function documentation with parameters and return values

## Verification
- Ensure all functions from original code are properly categorized
- Confirm header files have appropriate include guards
- Verify that functions are declared in headers and defined in implementation files
- Check that global variables are properly encapsulated

## Next Steps
- After refactoring the source code, proceed to `3-unit-test-framework.md`