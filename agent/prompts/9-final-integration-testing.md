## Purpose
Implement comprehensive integration testing to validate the complete ATTINY control system from code through CI/CD pipeline to deployment.

## Instructions

1. **Full System Integration Tests**
   - Create tests that verify the entire system functions correctly
   - Test interaction between all components

   ```c
   // tests/integration/full_system_test.c
   #include "unity.h"
   #include "config.h"
   #include "pwm_control.h"
   #include "air_pressure.h"
   #include "pump_control.h"
   #include "sonicator.h"
   #include "system_init.h"
   #include "mocks/mock_avr_io.h"
   #include "mocks/mock_eeprom.h"
   #include "mocks/mock_pin_control.h"

   void setUp(void) {
     resetMockPins();
     resetMockRegisters();
     resetMockEEPROM();
     
     // Initialize the system
     initialize_variables();
     initialize_chip();
   }

   void tearDown(void) {
     // Clean up
   }

   void test_full_flow_control_cycle(void) {
     // Initial state - Pump should be off after initialization
     TEST_ASSERT_FALSE(is_pump_on());
     
     // Test PWM inputs from 0% to 100% in steps
     for (int pwm = 0; pwm <= 100; pwm += 10) {
       // Set mock PWM input
       setMockPulseTime(PWM_IN_PIN, HIGH, pwm * 10);
       setMockPulseTime(PWM_IN_PIN, LOW, (100 - pwm) * 10);
       
       // Run flow pump control
       flow_pump_control();
       
       // Verify expected behavior
       if (pwm >= PWM_PUMP_THRESHOLD) {
         // Pump should be off when PWM is above threshold
         TEST_ASSERT_FALSE(is_pump_on());
       } else if (pwm < 2) {
         // Pump should be on when PWM is below 2%
         TEST_ASSERT_TRUE(is_pump_on());
       } else {
         // PWM output should be limited to 77% max
         int expected_pwm_out = 255 * (pwm / 100.0);
         if (pwm > PWM_DUTY_CYCLE_CAP) {
           expected_pwm_out = 255 * (PWM_DUTY_CYCLE_CAP / 100.0);
         }
         TEST_ASSERT_EQUAL(expected_pwm_out, mock_OCR0B);
       }
     }
   }

   void test_full_air_pressure_cycle(void) {
     // Initial state
     TEST_ASSERT_FALSE(is_air_on());
     
     // Simulate sonicator turning on (LOW signal)
     setPinState(SONIC_IN_PIN, LOW);
     
     // Run air pressure control
     air_pressure_control();
     
     // Verify air is on
     TEST_ASSERT_TRUE(is_air_on());
     TEST_ASSERT_EQUAL(0, mock_interrupt_count);
     
     // Simulate sonicator turning off (HIGH signal)
     setPinState(SONIC_IN_PIN, HIGH);
     
     // Air should stay on initially
     air_pressure_control();
     TEST_ASSERT_TRUE(is_air_on());
     
     // Simulate timer interrupts incrementing counter
     for (int i = 0; i < INTERRUPT_COUNT_MAX; i++) {
       mock_interrupt_count = i;
       // Call the ISR directly
       TIM1_COMPA_vect_call();
       
       // Air should remain on until count reaches max
       TEST_ASSERT_TRUE(is_air_on());
     }
     
     // Simulate final interrupt that exceeds the threshold
     mock_interrupt_count = INTERRUPT_COUNT_MAX;
     TIM1_COMPA_vect_call();
     
     // Air should turn off
     TEST_ASSERT_FALSE(is_air_on());
   }

   void test_eeprom_persistence(void) {
     // Start with air off
     turn_air_off();
     TEST_ASSERT_FALSE(is_air_on());
     
     // Turn air on and verify EEPROM update
     turn_air_on();
     TEST_ASSERT_TRUE(is_air_on());
     TEST_ASSERT_EQUAL(0, mock_eeprom_memory[0]);
     
     // Simulate power cycle by resetting state and reading from EEPROM
     bool saved_air_status = is_air_on();
     
     // Reset state directly
     extern bool air_status;
     air_status = false;
     
     // Read from EEPROM
     read_eeprom();
     
     // Verify state is restored
     TEST_ASSERT_EQUAL(saved_air_status, is_air_on());
   }

   void test_complete_system_operation(void) {
     // Test full system operation with interacting components
     
     // 1. Initialize with both sonicator off and low PWM
     setPinState(SONIC_IN_PIN, HIGH);
     setMockPulseTime(PWM_IN_PIN, HIGH, 500);
     setMockPulseTime(PWM_IN_PIN, LOW, 500);
     
     // 2. Initial state check
     TEST_ASSERT_FALSE(is_air_on());
     
     // 3. Run system loop
     air_pressure_control();
     flow_pump_control();
     
     // 4. Check PWM pass-through
     TEST_ASSERT_EQUAL(127, mock_OCR0B); // 50% of 255 is ~127
     
     // 5. Turn sonicator on
     setPinState(SONIC_IN_PIN, LOW);
     air_pressure_control();
     
     // 6. Verify air turns on
     TEST_ASSERT_TRUE(is_air_on());
     
     // 7. Change PWM to high value
     setMockPulseTime(PWM_IN_PIN, HIGH, 950);
     setMockPulseTime(PWM_IN_PIN, LOW, 50);
     flow_pump_control();
     
     // 8. Verify pump turns off due to high PWM
     TEST_ASSERT_FALSE(is_pump_on());
     
     // 9. Turn sonicator off
     setPinState(SONIC_IN_PIN, HIGH);
     air_pressure_control();
     
     // 10. Air should still be on until timer expires
     TEST_ASSERT_TRUE(is_air_on());
     
     // 11. Simulate timer expiration
     mock_interrupt_count = INTERRUPT_COUNT_MAX + 1;
     TIM1_COMPA_vect_call();
     
     // 12. Verify air turns off
     TEST_ASSERT_FALSE(is_air_on());
     
     // 13. Verify EEPROM persistence
     TEST_ASSERT_EQUAL(1, mock_eeprom_memory[0]);
   }

   int main(void) {
     UNITY_BEGIN();
     
     RUN_TEST(test_full_flow_control_cycle);
     RUN_TEST(test_full_air_pressure_cycle);
     RUN_TEST(test_eeprom_persistence);
     RUN_TEST(test_complete_system_operation);
     
     return UNITY_END();
   }
   ```

2. **End-to-End Testing Script**
   - Create a script to test the full development-to-deployment workflow
   - Validate CI/CD pipeline and deployment

   ```python
   # tests/e2e/pipeline_test.py
   import os
   import sys
   import time
   import subprocess
   import requests
   import unittest
   from google.cloud import storage

   class EndToEndTest(unittest.TestCase):
       @classmethod
       def setUpClass(cls):
           # Configuration
           cls.project_id = 'cannasol-automation-suite'
           cls.firmware_bucket = 'cannasol-firmware'
           cls.firmware_path = 'attiny-control/firmware-test.hex'
           
           # Initialize GCP clients
           cls.storage_client = storage.Client(project=cls.project_id)
           cls.bucket = cls.storage_client.bucket(cls.firmware_bucket)
           
           # Build firmware
           cls.build_firmware()
       
       @classmethod
       def build_firmware(cls):
           """Build firmware for testing"""
           subprocess.run([
               'mkdir', '-p', 'build'
           ], check=True)
           
           subprocess.run([
               'avr-gcc', '-mmcu=attiny85', '-Os',
               '-o', 'build/firmware.elf',
               '-I', 'src'
           ] + [f'src/{file}' for file in os.listdir('src') if file.endswith('.c')],
              check=True)
           
           subprocess.run([
               'avr-objcopy',
               '-j', '.text', '-j', '.data',
               '-O', 'ihex',
               'build/firmware.elf',
               'build/firmware.hex'
           ], check=True)
           
           # For testing, copy to a specific test file
           with open('build/firmware.hex', 'rb') as f:
               cls.firmware_content = f.read()
       
       def test_01_upload_firmware(self):
           """Test uploading firmware to GCS"""
           blob = self.bucket.blob(self.firmware_path)
           blob.upload_from_string(self.firmware_content)
           
           # Verify upload
           self.assertTrue(blob.exists())
           
           # Verify content
           downloaded_content = blob.download_as_bytes()
           self.assertEqual(self.firmware_content, downloaded_content)
       
       def test_02_trigger_build(self):
           """Test triggering a build in Cloud Build"""
           # This would normally use the Cloud Build API
           # For this example, we'll simulate a successful build
           print("Simulating Cloud Build trigger...")
           time.sleep(2)  # Simulate build time
           
           # In a real scenario, you would check build status
           build_success = True
           self.assertTrue(build_success)
       
       def test_03_verify_deployment(self):
           """Test firmware deployment verification"""
           # Check if firmware is properly deployed
           production_blob = self.bucket.blob('attiny-control/firmware-production.hex')
           self.assertTrue(production_blob.exists())
           
           # Check firmware metadata
           metadata_url = f"https://us-central1-{self.project_id}.cloudfunctions.net/firmware-metadata?path={self.firmware_path}"
           response = requests.get(metadata_url)
           self.assertEqual(response.status_code, 200)
           
           # Verify firmware content is valid
           firmware_data = response.json()
           self.assertIn('version', firmware_data)
           self.assertIn('size', firmware_data)
       
       @classmethod
       def tearDownClass(cls):
           """Clean up test resources"""
           # Delete test firmware
           blob = cls.bucket.blob(cls.firmware_path)
           if blob.exists():
               blob.delete()

   if __name__ == '__main__':
       unittest.main()
   ```

3. **Pipeline Verification Tests**
   - Verify that the CI/CD pipeline correctly responds to code changes
   - Test build, test, and deployment steps

   ```bash
   # tests/e2e/verify_pipeline.sh
   #!/bin/bash
   set -e

   echo "Running CI/CD pipeline verification test"

   # Step 1: Clone the repository
   echo "Cloning repository..."
   TMP_DIR=$(mktemp -d)
   git clone https://github.com/sboyett31/attiny-control.git "$TMP_DIR"
   cd "$TMP_DIR"

   # Step 2: Make a test change
   echo "Making test changes..."
   BRANCH_NAME="test-pipeline-$(date +%s)"
   git checkout -b "$BRANCH_NAME"
   
   # Modify a comment in a source file
   echo "// Test pipeline verification $(date)" >> src/main.c
   git add src/main.c
   git commit -m "test: Verify pipeline functionality"

   # Step 3: Push the branch
   echo "Pushing test branch..."
   git push origin "$BRANCH_NAME"

   # Step 4: Create a PR (using GitHub CLI if available)
   if command -v gh &> /dev/null; then
     echo "Creating pull request..."
     gh pr create --title "Test: CI/CD Pipeline Verification" \
                  --body "Automated test to verify CI/CD pipeline functionality." \
                  --repo sboyett31/attiny-control \
                  --base main
   else
     echo "GitHub CLI not found. Please create a PR manually at:"
     echo "https://github.com/sboyett31/attiny-control/pull/new/$BRANCH_NAME"
   fi

   # Step 5: Wait for CI to complete
   echo "Waiting for CI pipeline to run..."
   sleep 30

   # Step 6: Check CI status
   if command -v gh &> /dev/null; then
     PR_NUM=$(gh pr list --state open --repo sboyett31/attiny-control --json number --jq ".[0].number")
     echo "Checking status of PR #$PR_NUM..."
     
     # Poll for CI completion
     for i in {1..10}; do
       CI_STATUS=$(gh pr checks "$PR_NUM" --repo sboyett31/attiny-control --json checkSuites --jq '.checkSuites[0].conclusion')
       if [ "$CI_STATUS" == "success" ] || [ "$CI_STATUS" == "failure" ]; then
         break
       fi
       echo "CI still running... (attempt $i/10)"
       sleep 30
     done
     
     # Verify CI completed
     if [ "$CI_STATUS" == "success" ]; then
       echo "✅ CI pipeline completed successfully!"
     else
       echo "❌ CI pipeline failed or timed out: $CI_STATUS"
       exit 1
     fi
     
     # Clean up
     echo "Cleaning up test PR..."
     gh pr close "$PR_NUM" --repo sboyett31/attiny-control --delete-branch
   else
     echo "Please check CI status manually on GitHub."
   fi

   # Clean up temporary directory
   cd -
   rm -rf "$TMP_DIR"

   echo "Pipeline verification test completed."
   ```

4. **Emulator Integration Tests**
   - Create tests that validate firmware behavior on the emulated hardware
   - Test integration with the CI/CD pipeline

   ```python
   # tests/e2e/emulator_integration_test.py
   import os
   import sys
   import unittest
   import subprocess
   from test_harness import ATTINY85Emulator

   class EmulatorIntegrationTest(unittest.TestCase):
       @classmethod
       def setUpClass(cls):
           # Build firmware if not already built
           if not os.path.exists('build/firmware.hex'):
               subprocess.run([
                   'python3', 'tools/build_for_emulation.py'
               ], check=True)
           
           # Initialize emulator
           cls.emulator = ATTINY85Emulator('build/firmware.hex')
       
       @classmethod
       def tearDownClass(cls):
           cls.emulator.close()
       
       def test_system_initialization(self):
           """Test system initialization"""
           # Run for a short time to complete initialization
           self.emulator.run_for_ms(100)
           
           # Check initial pin states
           self.assertEqual(0, self.emulator.get_pin(0))  # AIR_OUT should be LOW
           self.assertEqual(0, self.emulator.get_pin(2))  # PUMP_OUT should be LOW (pump off)
       
       def test_pwm_behavior(self):
           """Test PWM behavior with different input signals"""
           # Test with 50% duty cycle
           self.emulator.set_pwm_input(50)
           self.emulator.run_for_ms(100)
           
           # Measure output duty cycle
           output_duty = self.emulator.measure_pwm_duty_cycle(1)
           self.assertTrue(45 <= output_duty <= 55)  # Allow for some measurement error
           
           # Test with 90% duty cycle (should be capped)
           self.emulator.set_pwm_input(90)
           self.emulator.run_for_ms(100)
           
           # Check output is capped at 77%
           output_duty = self.emulator.measure_pwm_duty_cycle(1)
           self.assertTrue(70 <= output_duty <= 80)  # Should be around 77%
           
           # Test with very high duty cycle (pump should turn off)
           self.emulator.set_pwm_input(95)
           self.emulator.run_for_ms(100)
           
           # Check pump is off (HIGH)
           self.assertEqual(1, self.emulator.get_pin(2))
       
       def test_air_pressure_control(self):
           """Test complete air pressure control cycle"""
           # Start with sonicator off
           self.emulator.set_pin(3, 1)  # HIGH = OFF
           self.emulator.run_for_ms(100)
           
           # Air should be off
           self.assertEqual(0, self.emulator.get_pin(0))
           
           # Turn sonicator on
           self.emulator.set_pin(3, 0)  # LOW = ON
           self.emulator.run_for_ms(100)
           
           # Air should be on
           self.assertEqual(1, self.emulator.get_pin(0))
           
           # Turn sonicator off
           self.emulator.set_pin(3, 1)  # HIGH = OFF
           self.emulator.run_for_ms(100)
           
           # Air should still be on initially
           self.assertEqual(1, self.emulator.get_pin(0))
           
           # Run for simulated 5 minutes (shortened for test)
           # In full emulation, this would be the actual timing
           # For test purposes, we'd accelerate time or directly trigger the ISR
           self.emulator.simulate_timer_interrupts(579)  # Maximum count
           
           # Air should be off
           self.assertEqual(0, self.emulator.get_pin(0))
       
       def test_full_system_operation(self):
           """Test full system operation in various scenarios"""
           # Reset emulator
           self.emulator.reset()
           self.emulator.run_for_ms(100)
           
           # Scenario 1: Normal operation
           self.emulator.set_pin(3, 0)  # Sonicator ON
           self.emulator.set_pwm_input(50)  # 50% PWM
           self.emulator.run_for_ms(100)
           
           # Air should be on, PWM around 50%, pump on
           self.assertEqual(1, self.emulator.get_pin(0))  # Air ON
           self.assertEqual(0, self.emulator.get_pin(2))  # Pump ON
           
           # Scenario 2: Sonicator off, high PWM
           self.emulator.set_pin(3, 1)  # Sonicator OFF
           self.emulator.set_pwm_input(95)  # 95% PWM
           self.emulator.run_for_ms(100)
           
           # Air should stay on initially, pump off
           self.assertEqual(1, self.emulator.get_pin(0))  # Air still ON
           self.assertEqual(1, self.emulator.get_pin(2))  # Pump OFF
           
           # Scenario 3: Trigger air off after timer expires
           self.emulator.simulate_timer_interrupts(579)
           
           # Air should be off
           self.assertEqual(0, self.emulator.get_pin(0))  # Air now OFF
           
           # Scenario 4: Restart sonicator
           self.emulator.set_pin(3, 0)  # Sonicator ON
           self.emulator.run_for_ms(100)
           
           # Air should be on again
           self.assertEqual(1, self.emulator.get_pin(0))  # Air ON again

   if __name__ == '__main__':
       unittest.main()
   ```

5. **Hardware-in-the-loop Testing**
   - Define methodology for testing with actual hardware
   - Create test scripts for physical device verification

   ```python
   # tests/hardware/hardware_verification.py
   import os
   import sys
   import time
   import argparse
   import serial

   class HardwareVerifier:
       def __init__(self, port, baud_rate=9600):
           """Initialize hardware verifier"""
           self.port = port
           self.baud_rate = baud_rate
           self.serial = None
       
       def connect(self):
           """Connect to the test harness Arduino"""
           try:
               self.serial = serial.Serial(self.port, self.baud_rate, timeout=1)
               time.sleep(2)  # Wait for Arduino to reset
               return True
           except Exception as e:
               print(f"Failed to connect: {e}")
               return False
       
       def disconnect(self):
           """Close the serial connection"""
           if self.serial:
               self.serial.close()
       
       def send_command(self, command):
           """Send command to test harness"""
           if not self.serial:
               raise Exception("Not connected")
           
           self.serial.write(f"{command}\n".encode())
           time.sleep(0.1)
           
           # Read response
           response = self.serial.readline().decode().strip()
           return response
       
       def test_pwm_signal(self, duty_cycle):
           """Test PWM signal response"""
           # Set PWM input
           response = self.send_command(f"PWM:{duty_cycle}")
           if not response.startswith("OK"):
               return False, f"Failed to set PWM: {response}"
           
           # Read back pin states
           time.sleep(0.5)  # Give time for system to respond
           response = self.send_command("READ:ALL")
           
           # Parse response
           try:
               pins = {}
               for pin_info in response.split(';'):
                   if ':' in pin_info:
                       pin, value = pin_info.split(':')
                       pins[pin] = int(value)
               
               # Check pump state based on PWM
               if duty_cycle >= 94:
                   # Pump should be off (HIGH)
                   if pins.get('PUMP') != 1:
                       return False, f"Pump should be OFF with PWM {duty_cycle}%, got {pins.get('PUMP')}"
               else:
                   # PWM output should be capped
                   pwm_out = pins.get('PWM_OUT', 0)
                   if duty_cycle > 77:
                       # Should be capped
                       if pwm_out > 77 + 5:  # Allow 5% error
                           return False, f"PWM should be capped at 77%, got {pwm_out}%"
               
               return True, pins
           except Exception as e:
               return False, f"Error parsing response: {e}"
       
       def test_air_pressure(self):
           """Test air pressure control"""
           # Start with sonicator off
           response = self.send_command("SONIC:OFF")
           if not response.startswith("OK"):
               return False, f"Failed to set sonic: {response}"
           
           time.sleep(0.5)
           
           # Read pin states
           response = self.send_command("READ:ALL")
           pins = {}
           for pin_info in response.split(';'):
               if ':' in pin_info:
                   pin, value = pin_info.split(':')
                   pins[pin] = int(value)
           
           # Air should be off
           if pins.get('AIR') != 0:
               return False, "Air should be OFF with sonicator OFF"
           
           # Turn sonicator on
           response = self.send_command("SONIC:ON")
           if not response.startswith("OK"):
               return False, f"Failed to set sonic: {response}"
           
           time.sleep(0.5)
           
           # Read pin states
           response = self.send_command("READ:ALL")
           pins = {}
           for pin_info in response.split(';'):
               if ':' in pin_info:
                   pin, value = pin_info.split(':')
                   pins[pin] = int(value)
           
           # Air should be on
           if pins.get('AIR') != 1:
               return False, "Air should be ON with sonicator ON"
           
           # Turn sonicator off
           response = self.send_command("SONIC:OFF")
           if not response.startswith("OK"):
               return False, f"Failed to set sonic: {response}"
           
           # Air should still be on initially
           time.sleep(0.5)
           response = self.send_command("READ:AIR")
           air_value = int(response.split(':')[1])
           if air_value != 1:
               return False, "Air should stay ON after sonicator turns OFF"
           
           # We can't wait 5 minutes in a test
           # In a real test, you might have a special firmware that accelerates time
           # or a command to simulate time passing
           response = self.send_command("SIMULATE:AIR_TIMEOUT")
           if not response.startswith("OK"):
               return False, f"Failed to simulate timeout: {response}"
           
           time.sleep(0.5)
           
           # Air should now be off
           response = self.send_command("READ:AIR")
           air_value = int(response.split(':')[1])
           if air_value != 0:
               return False, "Air should be OFF after timeout"
           
           return True, "Air pressure control working correctly"
       
       def run_all_tests(self):
           """Run all hardware verification tests"""
           results = []
           
           # Connect to hardware
           if not self.connect():
               return False, "Failed to connect to hardware test fixture"
           
           try:
               # Reset device
               self.send_command("RESET")
               time.sleep(1)
               
               # Test PWM with various duty cycles
               for duty_cycle in [10, 50, 75, 80, 95]:
                   success, result = self.test_pwm_signal(duty_cycle)
                   results.append(("PWM Test {}%".format(duty_cycle), success, result))
               
               # Test air pressure control
               success, result = self.test_air_pressure()
               results.append(("Air Pressure Test", success, result))
               
               # Report results
               all_passed = all(result[1] for result in results)
               return all_passed, results
           finally:
               # Always disconnect
               self.disconnect()

   if __name__ == "__main__":
       parser = argparse.ArgumentParser(description="Hardware verification tests")
       parser.add_argument("--port", required=True, help="Serial port for test harness")
       args = parser.parse_args()
       
       verifier = HardwareVerifier(args.port)
       success, results = verifier.run_all_tests()
       
       if isinstance(results, list):
           # Detailed results
           for name, passed, details in results:
               status = "PASS" if passed else "FAIL"
               print(f"{status}: {name}")
               if not passed or isinstance(details, dict):
                   print(f"  Details: {details}")
       else:
           # Simple error message
           print(results)
       
       sys.exit(0 if success else 1)
   ```

6. **Pipeline Deployment Verification**
   - Create tests to verify deployment from CI/CD pipeline
   - Validate firmware storage and version tracking

   ```python
   # tests/e2e/verify_deployment.py
   import os
   import sys
   import argparse
   import json
   import time
   from google.cloud import storage
   from google.cloud import firestore

   class DeploymentVerifier:
       def __init__(self, project_id):
           """Initialize deployment verifier"""
           self.project_id = project_id
           self.storage_client = storage.Client(project=project_id)
           self.firestore_client = firestore.Client(project=project_id)
       
       def verify_firmware_files(self, bucket_name, min_versions=3):
           """Verify firmware files exist and are properly stored"""
           try:
               bucket = self.storage_client.bucket(bucket_name)
               if not bucket.exists():
                   return False, f"Bucket '{bucket_name}' does not exist"
               
               # Check for production firmware
               production_blob = bucket.blob('attiny-control/firmware-production.hex')
               if not production_blob.exists():
                   return False, "Production firmware not found"
               
               # Check for latest firmware
               latest_blob = bucket.blob('attiny-control/firmware-latest.hex')
               if not latest_blob.exists():
                   return False, "Latest firmware not found"
               
               # Check for versioned firmwares
               blobs = list(bucket.list_blobs(prefix='attiny-control/firmware-v'))
               if len(blobs) < min_versions:
                   return False, f"Expected at least {min_versions} version(s), found {len(blobs)}"
               
               return True, {
                   'production': production_blob.name,
                   'latest': latest_blob.name,
                   'versions': [blob.name for blob in blobs]
               }
           except Exception as e:
               return False, f"Error verifying firmware files: {e}"
       
       def verify_firestore_records(self, min_versions=3):
           """Verify Firestore contains proper firmware records"""
           try:
               # Check firmware collection
               firmware_docs = list(self.firestore_client.collection('firmware').stream())
               if len(firmware_docs) < min_versions:
                   return False, f"Expected at least {min_versions} firmware record(s), found {len(firmware_docs)}"
               
               # Check deployment record
               deployment_doc = self.firestore_client.collection('firmware-deployment').document('production').get()
               if not deployment_doc.exists:
                   return False, "Production deployment record not found"
               
               # Check deployment data
               deployment_data = deployment_doc.to_dict()
               if 'version' not in deployment_data or 'deployedAt' not in deployment_data:
                   return False, "Deployment record missing required fields"
               
               return True, {
                   'firmware_count': len(firmware_docs),
                   'deployment': deployment_data
               }
           except Exception as e:
               return False, f"Error verifying Firestore records: {e}"
       
       def run_all_checks(self, bucket_name):
           """Run all deployment verification checks"""
           results = []
           
           # Check firmware files
           success, details = self.verify_firmware_files(bucket_name)
           results.append(("Firmware Files", success, details))
           
           # Check Firestore records
           success, details = self.verify_firestore_records()
           results.append(("Firestore Records", success, details))
           
           # Check overall success
           all_passed = all(result[1] for result in results)
           
           return all_passed, results

   if __name__ == "__main__":
       parser = argparse.ArgumentParser(description="Verify CI/CD deployment")
       parser.add_argument("--project", default="cannasol-automation-suite", help="GCP Project ID")
       parser.add_argument("--bucket", default="cannasol-firmware", help="Firmware storage bucket")
       args = parser.parse_args()
       
       verifier = DeploymentVerifier(args.project)
       success, results = verifier.run_all_checks(args.bucket)
       
       for name, passed, details in results:
           status = "PASS" if passed else "FAIL"
           print(f"{status}: {name}")
           print(f"  Details: {json.dumps(details, indent=2, default=str)}")
       
       sys.exit(0 if success else 1)
   ```

## Verification
- Ensure all integration tests pass on local development environment
- Verify that end-to-end tests correctly validate the CI/CD pipeline
- Confirm hardware verification tests function with actual test hardware
- Validate that deployed firmware passes all verification checks

## Next Steps
- After implementing comprehensive integration testing, proceed to `10-production-deployment.md`