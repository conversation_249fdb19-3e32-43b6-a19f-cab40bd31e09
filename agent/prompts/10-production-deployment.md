## Purpose
Establish a robust and secure process for deploying the ATTINY control firmware to production devices, including versioning, hardware programming, and verification procedures.

## Instructions

1. **Production Firmware Release Process**
   - Define the procedure for releasing firmware to production
   - Implement version control and approval workflows

   ```bash
   # tools/release.sh
   #!/bin/bash
   set -e

   # Configuration
   PROJECT_ID="cannasol-automation-suite"
   FIRMWARE_BUCKET="cannasol-firmware"
   FIRMWARE_DIR="attiny-control"

   # Parse arguments
   VERSION=""
   RELEASE_TYPE=""

   function usage {
     echo "Usage: $0 [--major|--minor|--patch] [--version X.Y.Z]"
     echo ""
     echo "Options:"
     echo "  --major         Increment major version"
     echo "  --minor         Increment minor version"
     echo "  --patch         Increment patch version"
     echo "  --version X.Y.Z Use specific version"
     exit 1
   }

   # Parse command line arguments
   while [[ $# -gt 0 ]]; do
     case "$1" in
       --major)
         RELEASE_TYPE="major"
         shift
         ;;
       --minor)
         RELEASE_TYPE="minor"
         shift
         ;;
       --patch)
         RELEASE_TYPE="patch"
         shift
         ;;
       --version)
         VERSION="$2"
         shift 2
         ;;
       *)
         usage
         ;;
     esac
   done

   # Validate arguments
   if [[ -z "$VERSION" && -z "$RELEASE_TYPE" ]]; then
     usage
   fi

   # Generate version if not specified
   if [[ -z "$VERSION" ]]; then
     VERSION=$(tools/generate_version.sh $RELEASE_TYPE)
   fi

   echo "Preparing release for version $VERSION"

   # 1. Ensure we have a clean working directory
   if [[ -n $(git status --porcelain) ]]; then
     echo "Error: Working directory is not clean. Please commit all changes."
     exit 1
   fi

   # 2. Update version information
   echo "Updating version information..."
   echo "#define FIRMWARE_VERSION \"$VERSION\"" > src/version.h
   echo "#define FIRMWARE_BUILD_DATE \"$(date '+%Y-%m-%d %H:%M:%S')\"" >> src/version.h
   echo "#define FIRMWARE_COMMIT \"$(git rev-parse HEAD)\"" >> src/version.h

   # 3. Commit version change
   git add src/version.h
   git commit -m "chore: Bump version to $VERSION"

   # 4. Create version tag
   git tag -a "v$VERSION" -m "Release version $VERSION"

   # 5. Build firmware
   echo "Building firmware..."
   mkdir -p build
   cd build
   cmake ..
   make
   cd ..

   # 6. Upload to GCS
   echo "Uploading firmware to Google Cloud Storage..."
   FIRMWARE_PATH="$FIRMWARE_DIR/firmware-v$VERSION.hex"
   gsutil cp build/firmware.hex "gs://$FIRMWARE_BUCKET/$FIRMWARE_PATH"
   gsutil cp build/firmware.hex "gs://$FIRMWARE_BUCKET/$FIRMWARE_DIR/firmware-latest.hex"

   # 7. Update Firestore
   echo "Updating version metadata..."
   VERSION_METADATA="{\"version\":\"$VERSION\",\"releaseDate\":\"$(date -u +%FT%TZ)\",\"commit\":\"$(git rev-parse HEAD)\",\"path\":\"$FIRMWARE_PATH\"}"
   curl -X POST "https://us-central1-$PROJECT_ID.cloudfunctions.net/record-firmware-release" \
     -H "Content-Type: application/json" \
     -d "$VERSION_METADATA"

   # 8. Push to GitHub
   git push origin master
   git push origin "v$VERSION"

   echo "Release $VERSION completed successfully!"
   echo "Firmware uploaded to gs://$FIRMWARE_BUCKET/$FIRMWARE_PATH"
   ```

2. **Device Programming Procedure**
   - Establish procedure for programming physical devices
   - Create tools for firmware installation and verification

   ```python
   # tools/program_device.py
   import os
   import sys
   import time
   import argparse
   import subprocess
   from google.cloud import storage

   def download_firmware(project_id, bucket_name, version=None):
       """Download firmware from GCS bucket"""
       storage_client = storage.Client(project=project_id)
       bucket = storage_client.bucket(bucket_name)
       
       if version:
           # Download specific version
           blob_name = f"attiny-control/firmware-v{version}.hex"
       else:
           # Download production version
           blob_name = "attiny-control/firmware-production.hex"
       
       blob = bucket.blob(blob_name)
       if not blob.exists():
           raise Exception(f"Firmware {blob_name} not found in bucket {bucket_name}")
       
       # Create local directory if it doesn't exist
       os.makedirs("firmware", exist_ok=True)
       
       # Download the firmware
       local_path = f"firmware/firmware.hex"
       blob.download_to_filename(local_path)
       
       return local_path, blob_name

   def program_device(firmware_path, programmer="usbtiny", device="attiny85"):
       """Program the device with specified firmware"""
       # Verify avrdude is installed
       try:
           subprocess.run(["avrdude", "-v"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
       except FileNotFoundError:
           raise Exception("avrdude not found. Please install it first.")
       
       # Program the device
       command = [
           "avrdude",
           "-c", programmer,
           "-p", device,
           "-U", f"flash:w:{firmware_path}:i"
       ]
       
       try:
           result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
           if result.returncode != 0:
               raise Exception(f"Programming failed: {result.stderr}")
           return True
       except subprocess.CalledProcessError as e:
           raise Exception(f"Error executing avrdude: {str(e)}")

   def verify_device(programmer="usbtiny", device="attiny85"):
       """Verify device is properly programmed"""
       # Read back device configuration and verify
       command = [
           "avrdude",
           "-c", programmer,
           "-p", device,
           "-U", "flash:v:firmware/firmware.hex:i"
       ]
       
       try:
           result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
           return result.returncode == 0
       except subprocess.CalledProcessError:
           return False

   def run_functional_test(port):
       """Run a basic functional test after programming"""
       # This would connect to a test fixture to validate the programmed device
       # For this example, we'll use a simplified approach
       try:
           # Import the hardware verification module
           sys.path.append("tests/hardware")
           from hardware_verification import HardwareVerifier
           
           # Run verification tests
           verifier = HardwareVerifier(port)
           success, results = verifier.run_all_tests()
           
           if not success:
               raise Exception(f"Functional test failed: {results}")
           
           return True
       except Exception as e:
           print(f"Failed to run functional test: {e}")
           return False

   def record_programming(project_id, device_id, version, success):
       """Record programming event in cloud database"""
       from google.cloud import firestore
       from datetime import datetime
       
       # Get Firestore client
       db = firestore.Client(project=project_id)
       
       # Record programming event
       programming_ref = db.collection("device_programming").document()
       programming_ref.set({
           "deviceId": device_id,
           "firmwareVersion": version,
           "timestamp": datetime.utcnow(),
           "success": success,
           "programmer": os.environ.get("USER", "unknown")
       })
       
       # Update device record
       if success:
           device_ref = db.collection("devices").document(device_id)
           device_ref.set({
               "firmwareVersion": version,
               "lastProgrammed": datetime.utcnow(),
               "status": "programmed"
           }, merge=True)

   if __name__ == "__main__":
       parser = argparse.ArgumentParser(description="Program ATTINY devices")
       parser.add_argument("--project", default="cannasol-automation-suite", help="GCP Project ID")
       parser.add_argument("--bucket", default="cannasol-firmware", help="Firmware bucket name")
       parser.add_argument("--version", help="Specific firmware version to use")
       parser.add_argument("--device-id", required=True, help="Unique device identifier")
       parser.add_argument("--programmer", default="usbtiny", help="Programmer type")
       parser.add_argument("--port", help="Serial port for functional test")
       args = parser.parse_args()
       
       try:
           # Step 1: Download firmware
           print(f"Downloading firmware {'v' + args.version if args.version else 'production'}...")
           firmware_path, blob_name = download_firmware(args.project, args.bucket, args.version)
           print(f"Downloaded {blob_name} to {firmware_path}")
           
           # Step 2: Program device
           print(f"Programming device {args.device_id}...")
           program_device(firmware_path, args.programmer)
           print("Programming successful")
           
           # Step 3: Verify programming
           print("Verifying device programming...")
           if not verify_device(args.programmer):
               raise Exception("Device verification failed")
           print("Verification successful")
           
           # Step 4: Functional test (if port specified)
           if args.port:
               print(f"Running functional test on port {args.port}...")
               if not run_functional_test(args.port):
                   raise Exception("Functional test failed")
               print("Functional test successful")
           
           # Step 5: Record successful programming
           version = args.version or "production"
           record_programming(args.project, args.device_id, version, True)
           print(f"Device {args.device_id} successfully programmed with firmware version {version}")
           
       except Exception as e:
           print(f"Error: {str(e)}")
           
           # Record failed programming attempt
           try:
               if 'version' in locals():
                   record_programming(args.project, args.device_id, version, False)
           except:
               pass
               
           sys.exit(1)
   ```

3. **Production Firmware Validation Procedure**
   - Define a comprehensive testing protocol for production firmware
   - Create documentation for validation steps

   ```markdown
   # Production Firmware Validation Protocol

   ## Prerequisites
   - ATTINY85 programming device (USBtiny or equivalent)
   - Test fixture with the following components:
     - Arduino for test control
     - PWM signal generator
     - Sonicator signal simulator
     - Air and pump output monitors
   - Programming validation software

   ## Step 1: Pre-deployment Testing
   1. Verify unit tests pass (100% success rate)
   2. Verify emulation tests pass (100% success rate)
   3. Confirm CI/CD pipeline completion
   4. Review code changes since last release

   ## Step 2: Staging Deployment
   1. Flash firmware to staging device
   2. Run electrical validation
     - Verify power consumption is within specifications
     - Verify I/O pins function correctly
   3. Run functional validation
     - Test PWM signal processing
     - Test air pressure control
     - Test pump control
     - Test sonicator detection
   4. Run performance testing
     - Verify timing accuracy
     - Test boundary conditions
     - Test error handling

   ## Step 3: Production Release
   1. Approve release in cloud management console
   2. Verify release metadata is correctly recorded
   3. Deploy to production devices using `tools/program_device.py`
   4. Validate each device after programming

   ## Step 4: Post-deployment Verification
   1. Collect logs from deployed devices
   2. Verify version tracking in cloud database
   3. Monitor device performance over 24-hour period
   4. Document any issues or anomalies
   ```

4. **Device Database Management**
   - Implement system for tracking devices and firmware versions
   - Create tools for managing device database

   ```python
   # tools/device_manager.py
   import argparse
   import csv
   import sys
   from datetime import datetime
   from google.cloud import firestore

   class DeviceManager:
       def __init__(self, project_id):
           """Initialize device manager"""
           self.db = firestore.Client(project=project_id)
       
       def register_device(self, device_id, location, description):
           """Register a new device in the database"""
           device_ref = self.db.collection("devices").document(device_id)
           
           # Check if device already exists
           doc = device_ref.get()
           if doc.exists:
               print(f"Device {device_id} already exists")
               return False
           
           # Create new device record
           device_ref.set({
               "deviceId": device_id,
               "location": location,
               "description": description,
               "status": "registered",
               "registrationDate": datetime.utcnow(),
               "firmwareVersion": None,
               "lastHeartbeat": None
           })
           
           print(f"Device {device_id} registered successfully")
           return True
       
       def update_device(self, device_id, **kwargs):
           """Update device information"""
           device_ref = self.db.collection("devices").document(device_id)
           
           # Check if device exists
           doc = device_ref.get()
           if not doc.exists:
               print(f"Device {device_id} not found")
               return False
           
           # Update device record
           device_ref.update(kwargs)
           
           print(f"Device {device_id} updated successfully")
           return True
       
       def delete_device(self, device_id):
           """Delete a device from the database"""
           device_ref = self.db.collection("devices").document(device_id)
           
           # Check if device exists
           doc = device_ref.get()
           if not doc.exists:
               print(f"Device {device_id} not found")
               return False
           
           # Delete device record
           device_ref.delete()
           
           print(f"Device {device_id} deleted successfully")
           return True
       
       def list_devices(self, filter_expr=None):
           """List all devices in the database"""
           query = self.db.collection("devices")
           
           # Apply filter if provided
           if filter_expr:
               field, op, value = filter_expr.split(":")
               query = query.where(field, op, value)
           
           # Execute query
           docs = query.stream()
           
           # Display results
           devices = []
           for doc in docs:
               devices.append(doc.to_dict())
           
           return devices
       
       def import_devices(self, csv_file):
           """Import devices from CSV file"""
           with open(csv_file, 'r') as f:
               reader = csv.DictReader(f)
               for row in reader:
                   device_id = row.get('deviceId')
                   location = row.get('location')
                   description = row.get('description')
                   
                   if not device_id:
                       continue
                   
                   self.register_device(device_id, location, description)
       
       def export_devices(self, csv_file, filter_expr=None):
           """Export devices to CSV file"""
           devices = self.list_devices(filter_expr)
           
           if not devices:
               print("No devices found")
               return False
           
           # Get all possible fields
           fields = set()
           for device in devices:
               fields.update(device.keys())
           
           with open(csv_file, 'w', newline='') as f:
               writer = csv.DictWriter(f, fieldnames=sorted(fields))
               writer.writeheader()
               writer.writerows(devices)
           
           print(f"Exported {len(devices)} devices to {csv_file}")
           return True

   if __name__ == "__main__":
       parser = argparse.ArgumentParser(description="Manage ATTINY control devices")
       parser.add_argument("--project", default="cannasol-automation-suite", help="GCP Project ID")
       
       subparsers = parser.add_subparsers(dest="command", help="Command to execute")
       
       # Register device
       register_parser = subparsers.add_parser("register", help="Register a new device")
       register_parser.add_argument("--id", required=True, help="Device ID")
       register_parser.add_argument("--location", help="Device location")
       register_parser.add_argument("--description", help="Device description")
       
       # Update device
       update_parser = subparsers.add_parser("update", help="Update device information")
       update_parser.add_argument("--id", required=True, help="Device ID")
       update_parser.add_argument("--location", help="Device location")
       update_parser.add_argument("--description", help="Device description")
       update_parser.add_argument("--status", help="Device status")
       
       # Delete device
       delete_parser = subparsers.add_parser("delete", help="Delete a device")
       delete_parser.add_argument("--id", required=True, help="Device ID")
       
       # List devices
       list_parser = subparsers.add_parser("list", help="List devices")
       list_parser.add_argument("--filter", help="Filter expression (field:op:value)")
       list_parser.add_argument("--output", help="Output CSV file")
       
       # Import devices
       import_parser = subparsers.add_parser("import", help="Import devices from CSV")
       import_parser.add_argument("--file", required=True, help="CSV file to import")
       
       # Export devices
       export_parser = subparsers.add_parser("export", help="Export devices to CSV")
       export_parser.add_argument("--file", required=True, help="CSV file to export")
       export_parser.add_argument("--filter", help="Filter expression (field:op:value)")
       
       args = parser.parse_args()
       
       # Initialize device manager
       device_manager = DeviceManager(args.project)
       
       # Execute command
       if args.command == "register":
           device_manager.register_device(args.id, args.location, args.description)
       elif args.command == "update":
           updates = {}
           if args.location:
               updates["location"] = args.location
           if args.description:
               updates["description"] = args.description
           if args.status:
               updates["status"] = args.status
           
           device_manager.update_device(args.id, **updates)
       elif args.command == "delete":
           device_manager.delete_device(args.id)
       elif args.command == "list":
           devices = device_manager.list_devices(args.filter)
           
           if args.output:
               # Export to CSV
               with open(args.output, 'w', newline='') as f:
                   if not devices:
                       print("No devices found")
                       sys.exit(0)
                   
                   fields = set()
                   for device in devices:
                       fields.update(device.keys())
                   
                   writer = csv.DictWriter(f, fieldnames=sorted(fields))
                   writer.writeheader()
                   writer.writerows(devices)
                   
                   print(f"Exported {len(devices)} devices to {args.output}")
           else:
               # Print to console
               if not devices:
                   print("No devices found")
                   sys.exit(0)
               
               for device in devices:
                   print(f"Device: {device.get('deviceId')}")
                   for key, value in sorted(device.items()):
                       if key != 'deviceId':
                           print(f"  {key}: {value}")
                   print()
       elif args.command == "import":
           device_manager.import_devices(args.file)
       elif args.command == "export":
           device_manager.export_devices(args.file, args.filter)
       else:
           parser.print_help()
   ```

5. **Firmware Rollback Procedure**
   - Define a procedure for rolling back firmware in case of issues
   - Create tools for firmware rollback management

   ```python
   # tools/rollback_firmware.py
   import argparse
   import sys
   from google.cloud import storage
   from google.cloud import firestore
   from datetime import datetime

   def list_firmware_versions(project_id, bucket_name, limit=10):
       """List available firmware versions"""
       storage_client = storage.Client(project=project_id)
       bucket = storage_client.bucket(bucket_name)
       
       # Get firmware versions from GCS
       blobs = list(bucket.list_blobs(prefix='attiny-control/firmware-v'))
       
       # Extract version information
       versions = []
       for blob in blobs:
           name = blob.name
           if name.endswith('.hex'):
               version = name.split('firmware-v')[1].split('.hex')[0]
               updated = blob.updated
               size = blob.size
               versions.append({
                   'version': version,
                   'path': name,
                   'updated': updated,
                   'size': size
               })
       
       # Sort by updated timestamp (most recent first)
       versions.sort(key=lambda x: x['updated'], reverse=True)
       
       return versions[:limit]

   def set_production_firmware(project_id, bucket_name, version):
       """Set the specified version as production firmware"""
       # Initialize clients
       storage_client = storage.Client(project=project_id)
       db = firestore.Client(project=project_id)
       
       bucket = storage_client.bucket(bucket_name)
       
       # Validate version exists
       source_blob = bucket.blob(f'attiny-control/firmware-v{version}.hex')
       if not source_blob.exists():
           raise Exception(f"Firmware version v{version} not found")
       
       # Copy to production path
       dest_blob = bucket.blob('attiny-control/firmware-production.hex')
       bucket.copy_blob(source_blob, bucket, dest_blob.name)
       
       # Update metadata in Firestore
       deployment_ref = db.collection('firmware-deployment').document('production')
       deployment_ref.set({
           'version': version,
           'deployedAt': datetime.utcnow(),
           'deployedBy': 'rollback-tool',
           'notes': 'Rollback to previous version',
           'sourcePath': source_blob.name
       })
       
       return True

   def record_rollback(project_id, version, reason):
       """Record rollback event in Firestore"""
       db = firestore.Client(project=project_id)
       
       # Add rollback record
       rollback_ref = db.collection('firmware-rollbacks').document()
       rollback_ref.set({
           'version': version,
           'timestamp': datetime.utcnow(),
           'reason': reason,
           'performedBy': 'rollback-tool'
       })

   if __name__ == "__main__":
       parser = argparse.ArgumentParser(description="Firmware rollback tool")
       parser.add_argument("--project", default="cannasol-automation-suite", help="GCP Project ID")
       parser.add_argument("--bucket", default="cannasol-firmware", help="Firmware bucket name")
       
       subparsers = parser.add_subparsers(dest="command", help="Command to execute")
       
       # List versions
       list_parser = subparsers.add_parser("list", help="List available firmware versions")
       list_parser.add_argument("--limit", type=int, default=10, help="Maximum versions to show")
       
       # Rollback to version
       rollback_parser = subparsers.add_parser("rollback", help="Rollback to specific version")
       rollback_parser.add_argument("--version", required=True, help="Version to rollback to")
       rollback_parser.add_argument("--reason", required=True, help="Reason for rollback")
       
       args = parser.parse_args()
       
       try:
           if args.command == "list":
               versions = list_firmware_versions(args.project, args.bucket, args.limit)
               
               if not versions:
                   print("No firmware versions found")
                   sys.exit(0)
               
               print(f"Available firmware versions:")
               for v in versions:
                   print(f"v{v['version']} - {v['updated'].strftime('%Y-%m-%d %H:%M:%S')} ({v['size']} bytes)")
           
           elif args.command == "rollback":
               print(f"Rolling back to firmware version v{args.version}...")
               
               # Set as production firmware
               set_production_firmware(args.project, args.bucket, args.version)
               
               # Record rollback event
               record_rollback(args.project, args.version, args.reason)
               
               print(f"Successfully rolled back to version v{args.version}")
               print("Note: Devices must be manually reprogrammed with the rolled back firmware")
           
           else:
               parser.print_help()
       
       except Exception as e:
           print(f"Error: {str(e)}")
           sys.exit(1)
   ```

6. **Production Deployment Documentation**
   - Create comprehensive documentation for production use
   - Include troubleshooting and emergency procedures

   ```markdown
   # ATTINY Control System Production Guide

   ## Deployment Workflow

   ### Prerequisites
   - Google Cloud SDK installed and authenticated with `cannasol-automation-suite` project
   - AVR development tools installed (avr-gcc, avrdude)
   - Python 3.8+ with google-cloud packages
   - Physical access to ATTINY programming hardware

   ### Release Process
   1. **Development**
      - Complete feature development on feature branches
      - Submit pull requests for review
      - Run unit tests and emulation tests locally
      - Merge approved changes to main branch

   2. **CI/CD**
      - Automatic build and testing triggered by GitHub
      - Code quality analysis performed
      - Emulation tests executed
      - Build artifacts stored in Cloud Storage

   3. **Release Preparation**
      - Determine version increment (major, minor, patch)
      - Update changelog with release notes
      - Run `tools/release.sh --major|--minor|--patch` to create release

   4. **Staging**
      - Flash staging hardware with new firmware
      - Run validation tests according to Production Firmware Validation Protocol
      - Document test results and verify all systems function correctly

   5. **Production Deployment**
      - Approve release in firmware management console
      - Use `tools/program_device.py` to program production devices
      - Run post-deployment verification tests on each device
      - Monitor device performance over 24-hour period

   ### Emergency Rollback
   - Use `tools/rollback_firmware.py` to revert to previous stable version
   - Reprogram affected devices with stable firmware
   - Record rollback reason and affected devices

   ## Device Management

   ### Device Registration
   1. Assign unique device ID based on internal numbering scheme
   2. Register device in database using `tools/device_manager.py register`
   3. Record physical device location and description
   4. Apply physical label with device ID and QR code

   ### Device Maintenance
   1. Schedule regular firmware updates via maintenance calendar
   2. Perform quarterly hardware inspection
   3. Replace devices showing abnormal behavior or errors
   4. Record all maintenance activities in device log

   ### Device Replacement
   1. Use `tools/device_manager.py update --id <id> --status "pending-replacement"`
   2. Program replacement device with current firmware
   3. Install replacement device
   4. Update device record with new information
   5. Return defective device for analysis

   ## Troubleshooting

   ### Common Issues

   #### Programming Failures
   - **Symptom**: avrdude reports "programmer not responding"
     - **Solution**: Check physical connections, reset programmer, verify power supply

   - **Symptom**: Verification error during programming
     - **Solution**: Check for damaged hardware, try slower programming speed

   #### Device Malfunction
   - **Symptom**: Air pressure stays on permanently
     - **Solution**: Check sonicator signal connection, reset device, reprogram if needed

   - **Symptom**: PWM output incorrect
     - **Solution**: Measure input signal quality, check for physical damage to pins

   ### Emergency Contacts
   - **Hardware Support**: John Smith (555-123-4567)
   - **Software Support**: Jane Doe (555-234-5678)
   - **Cloud Infrastructure**: Cloud Support Team (<EMAIL>)

   ## Security Procedures

   ### Firmware Protection
   - All production firmware is version-controlled and cryptographically signed
   - Access to release process requires 2-factor authentication
   - Firmware deployment requires documented approval

   ### Device Security
   - Devices contain no sensitive information
   - Physical access to devices should be restricted to authorized personnel
   - All programming activities are logged and auditable

   ### Cloud Security
   - GCP resources secured according to least privilege principle
   - Service account keys rotated quarterly
   - Audit logging enabled for all firmware repository access
   ```

7. **Production Verification Checklist**
   - Create a standardized checklist for deployment verification
   - Define acceptance criteria and sign-off process

   ```markdown
   # Production Deployment Verification Checklist

   ## Firmware Release: v_______________
   ## Date: _________________
   ## Engineer: _________________

   ### Pre-Deployment Verification
   - [ ] All unit tests pass (100% success rate)
   - [ ] All emulation tests pass (100% success rate)
   - [ ] Code review completed and approved by _________________
   - [ ] Release notes prepared and complete
   - [ ] Version number incremented correctly
   - [ ] Firmware successfully builds in CI/CD pipeline
   - [ ] Static code analysis shows no critical issues

   ### Staging Verification
   - [ ] Firmware programmed to staging device successfully
   - [ ] Power consumption measured and within spec: ________ mA
   - [ ] PWM signal processing verified
       - [ ] 10% input → ________ % output (should be ~10%)
       - [ ] 50% input → ________ % output (should be ~50%)
       - [ ] 80% input → ________ % output (should be ~77% cap)
       - [ ] 95% input → Pump OFF verified
   - [ ] Air pressure control verified
       - [ ] Sonicator ON → Air ON: ________ seconds response time
       - [ ] Sonicator OFF → Air remains ON: ________ minutes
       - [ ] After timeout → Air turns OFF verified
   - [ ] EEPROM persistence verified
       - [ ] State maintained after power cycle

   ### Production Deployment
   - [ ] Firmware uploaded to GCS bucket successfully
   - [ ] Production firmware updated in console
   - [ ] Deployment metadata recorded correctly
   - [ ] Firmware version information matches release version

   ### Device Programming (complete for each device)
   
   | Device ID | Programming Success | Verification Success | Functional Test | Notes |
   |-----------|--------------------|--------------------|---------------|-------|
   |           | □ Pass □ Fail      | □ Pass □ Fail      | □ Pass □ Fail |       |
   |           | □ Pass □ Fail      | □ Pass □ Fail      | □ Pass □ Fail |       |
   |           | □ Pass □ Fail      | □ Pass □ Fail      | □ Pass □ Fail |       |
   |           | □ Pass □ Fail      | □ Pass □ Fail      | □ Pass □ Fail |       |
   |           | □ Pass □ Fail      | □ Pass □ Fail      | □ Pass □ Fail |       |

   ### Post-Deployment Monitoring
   - [ ] All devices reporting heartbeats correctly
   - [ ] Device telemetry within normal parameters
   - [ ] No error conditions detected after 24 hours
   - [ ] Performance metrics match or exceed previous version

   ## Sign-off

   ### Engineering Approval
   - Engineer: _________________________ Date: _________________
   - Signature: _________________________

   ### Operations Approval
   - Manager: _________________________ Date: _________________
   - Signature: _________________________

   ### Notes and Observations
   _________________________________________________________________
   _________________________________________________________________
   _________________________________________________________________
   _________________________________________________________________
   ```

## Verification
- Test the release process with a minor version update
- Verify firmware rollback procedure functions correctly
- Test device programming process with an actual device
- Validate that the production verification checklist is complete

## Next Steps
- Congratulations! You've completed the implementation of a comprehensive ATTINY control system with full CI/CD pipeline, testing, and deployment processes.