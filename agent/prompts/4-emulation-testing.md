## Verification
- Verify Docker container builds successfully
- Confirm emulation tests can be run in CI environment
- Ensure test results are properly reported
- Check that different PWM signals are correctly processed
- Verify air pressure control timing with sonicator

## Next Steps
- After setting up the emulation testing, proceed to `5-ci-cd-pipeline.md` Purpose
Set up hardware emulation testing for the ATTINY85 microcontroller code using Simulavr or similar AVR emulator in a Docker container.

## Instructions

1. **Emulation Framework Selection**
   - Use Simulavr for AVR microcontroller emulation
   - Alternative: PlatformIO with simavr backend

2. **Docker Environment Setup**
   ```dockerfile
   # Dockerfile.emulation
   FROM ubuntu:20.04

   # Install dependencies
   RUN apt-get update && apt-get install -y \
       build-essential \
       cmake \
       git \
       python3 \
       python3-pip \
       avr-libc \
       gcc-avr \
       gdb-avr \
       avrdude \
       && rm -rf /var/lib/apt/lists/*

   # Install Simulavr
   RUN git clone https://github.com/Traumflug/simulavr.git /simulavr && \
       cd /simulavr && \
       ./bootstrap && \
       ./configure --enable-python --with-pic && \
       make && \
       make install

   # Install Python dependencies
   RUN pip3 install pytest pyserial matplotlib

   # Set up working directory
   WORKDIR /app

   # Copy source code and tests
   COPY . .

   # Default command
   CMD ["python3", "-m", "pytest", "tests/emulation"]
   ```

3. **Python Test Harness for Emulation**
   - Create a Python interface to interact with Simulavr
   - Implement test procedures that validate hardware behavior

   ```python
   # tests/emulation/test_harness.py
   import os
   import sys
   import time
   import pytest
   from pysimulavr import SimulavrAdapter, Simulator

   class ATTINY85Emulator:
       def __init__(self, hex_file):
           self.hex_file = hex_file
           self.sim = SimulavrAdapter()
           self.device = self.sim.loadDevice("attiny85", self.hex_file)
           self.simulator = Simulator()
           self.simulator.addDevice(self.device)
           
           # Initialize pins
           self.pins = {
               0: self.device.GetPin("B0"),  # AIR_OUT
               1: self.device.GetPin("B1"),  # PWM_OUT
               2: self.device.GetPin("B2"),  # PUMP_OUT
               3: self.device.GetPin("B3"),  # SONIC_IN
               4: self.device.GetPin("B4"),  # PWM_IN
               5: self.device.GetPin("B5"),  # STATUS_OUT
           }
           
       def set_pin(self, pin_num, value):
           """Set input pin to specified value (0 or 1)"""
           pin = self.pins.get(pin_num)
           if pin:
               pin.SetPin(value)
           
       def get_pin(self, pin_num):
           """Get pin state (0 or 1)"""
           pin = self.pins.get(pin_num)
           if pin:
               return 1 if pin.GetPin() else 0
           return None
           
       def run_for_cycles(self, cycles):
           """Run the simulation for specified number of cycles"""
           self.sim.RunTimeRange(self.simulator, cycles)
           
       def run_for_ms(self, milliseconds):
           """Run the simulation for specified milliseconds"""
           # Assuming 8MHz clock, 1ms = 8,000 cycles
           cycles = int(milliseconds * 8000)
           self.run_for_cycles(cycles)
           
       def close(self):
           """Clean up simulation"""
           self.simulator.RemoveDevice(self.device)
           self.sim.doneSimulation()
   ```

4. **PWM Signal Emulation Test**
   ```python
   # tests/emulation/test_pwm_control.py
   import pytest
   import os
   from test_harness import ATTINY85Emulator

   @pytest.fixture
   def emulator():
       """Set up and tear down the ATTINY85 emulator"""
       hex_file = os.path.join(os.path.dirname(__file__), "../../build/firmware.hex")
       emu = ATTINY85Emulator(hex_file)
       yield emu
       emu.close()

   def test_pwm_pass_through(emulator):
       """Test PWM signal passing through with duty cycle cap"""
       # Simulate a 50% duty cycle PWM input
       emulator.set_pwm_input(50)
       
       # Run for a while to let the code process the input
       emulator.run_for_ms(100)
       
       # Verify PWM output signal has ~50% duty cycle
       output_duty_cycle = emulator.measure_pwm_duty_cycle(1)  # PWM_OUT pin
       assert abs(output_duty_cycle - 50) < 2  # Allow small measurement error
       
       # Verify pump is on (LOW = on)
       assert emulator.get_pin(2) == 0
       
       # Now test with high duty cycle (above cap)
       emulator.set_pwm_input(90)
       emulator.run_for_ms(100)
       
       # Verify PWM output is capped at 77%
       output_duty_cycle = emulator.measure_pwm_duty_cycle(1)
       assert abs(output_duty_cycle - 77) < 2
       
       # Verify pump is off (HIGH = off) due to high input PWM
       assert emulator.get_pin(2) == 1

   def test_air_pressure_control(emulator):
       """Test air pressure control based on sonicator input"""
       # Start with sonicator OFF (HIGH)
       emulator.set_pin(3, 1)
       emulator.run_for_ms(100)
       
       # Air should be OFF (LOW)
       assert emulator.get_pin(0) == 0
       
       # Turn sonicator ON (LOW)
       emulator.set_pin(3, 0)
       emulator.run_for_ms(100)
       
       # Air should be ON (HIGH)
       assert emulator.get_pin(0) == 1
       
       # Turn sonicator OFF again
       emulator.set_pin(3, 1)
       
       # Run for less than 5 minutes
       emulator.run_for_ms(100000)  # 100 seconds
       
       # Air should still be ON
       assert emulator.get_pin(0) == 1
       
       # Run for full 5 minutes
       emulator.run_for_ms(200000)  # 200 more seconds (5 min total)
       
       # Air should be OFF now
       assert emulator.get_pin(0) == 0
   ```

5. **Helper Methods for PWM Emulation**
   ```python
   # Add to ATTINY85Emulator class in test_harness.py
   
   def set_pwm_input(self, duty_cycle):
       """
       Simulate a PWM input signal with specified duty cycle
       This requires toggling the pin with the right timing
       """
       # Calculate period based on 8MHz clock
       period_cycles = 1000  # arbitrary period
       high_cycles = int(period_cycles * duty_cycle / 100)
       low_cycles = period_cycles - high_cycles
       
       # Create PWM signal generation task
       def pwm_task():
           self.set_pin(4, 1)  # PWM_IN pin HIGH
           self.run_for_cycles(high_cycles)
           self.set_pin(4, 0)  # PWM_IN pin LOW
           self.run_for_cycles(low_cycles)
           
       # Schedule repetitive PWM signal generation
       # In reality, we'd need to create a background thread 
       # or hook into simulavr's cycle callback mechanism
       # This is simplified for clarity
       self.simulator.AddTask(pwm_task, 0)
   
   def measure_pwm_duty_cycle(self, pin_num):
       """Measure the duty cycle of a PWM signal on specified pin"""
       # Take multiple samples
       samples = []
       for _ in range(100):
           samples.append(self.get_pin(pin_num))
           self.run_for_cycles(10)
           
       # Calculate duty cycle from samples
       high_count = sum(samples)
       return (high_count / len(samples)) * 100
   ```

6. **Complete Emulation Test Suite**
   - Create comprehensive tests for all main functionality
   - Test each module in the system under realistic conditions

7. **Build Integration Script**
   ```python
   # tools/build_for_emulation.py
   import os
   import subprocess
   import argparse

   def build_firmware(output_dir="build"):
       """Build firmware for emulation testing"""
       if not os.path.exists(output_dir):
           os.makedirs(output_dir)
           
       # Compile for ATTINY85
       cmd = [
           "avr-gcc",
           "-mmcu=attiny85",
           "-Os",
           "-o", f"{output_dir}/firmware.elf",
           "-I", "src",
       ] + [f"src/{file}" for file in os.listdir("src") if file.endswith(".c")]
       
       subprocess.run(cmd, check=True)
       
       # Generate HEX file
       subprocess.run([
           "avr-objcopy",
           "-j", ".text", "-j", ".data",
           "-O", "ihex",
           f"{output_dir}/firmware.elf",
           f"{output_dir}/firmware.hex"
       ], check=True)
       
       print(f"Firmware built successfully: {output_dir}/firmware.hex")

   if __name__ == "__main__":
       parser = argparse.ArgumentParser(description="Build firmware for emulation testing")
       parser.add_argument("--output", default="build", help="Output directory")
       args = parser.parse_args()
       
       build_firmware(args.output)
   ```

8. **Docker-Compose for Emulation Tests**
   ```yaml
   # docker-compose.yml
   version: '3'
   
   services:
     emulation-test:
       build:
         context: .
         dockerfile: Dockerfile.emulation
       volumes:
         - .:/app
       command: >
         bash -c "python3 tools/build_for_emulation.py &&
                 python3 -m pytest tests/emulation -v"
   ```

##