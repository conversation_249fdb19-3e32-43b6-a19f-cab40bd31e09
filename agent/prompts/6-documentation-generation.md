## Purpose
Establish comprehensive code documentation and generate developer/user documentation for the ATTINY control system.

## Instructions

1. **Set Up Doxygen Configuration**
   - Create a Doxyfile in the project root:

   ```
   # Doxyfile
   PROJECT_NAME           = "Cannasol ATTINY Control System"
   PROJECT_BRIEF          = "Microcontroller software for PWM control, air pressure, and pump management"
   OUTPUT_DIRECTORY       = docs
   OPTIMIZE_OUTPUT_FOR_C  = YES
   EXTRACT_ALL            = YES
   EXTRACT_PRIVATE        = YES
   EXTRACT_STATIC         = YES
   RECURSIVE              = YES
   INPUT                  = src README.md
   FILE_PATTERNS          = *.c *.h
   GENERATE_HTML          = YES
   GENERATE_LATEX         = NO
   HAVE_DOT               = YES
   CALL_GRAPH             = YES
   CALLER_GRAPH           = YES
   DOT_IMAGE_FORMAT       = svg
   INTERACTIVE_SVG        = YES
   UML_LOOK               = YES
   ```

2. **Code Documentation Standards**
   - Define documentation style for functions, modules, and variables
   - Create a documentation header template:

   ```c
   /**
    * @file pwm_control.h
    * @brief PWM signal control functionality for ATTINY85 microcontroller
    *
    * Provides functions for reading PWM inputs, controlling PWM outputs,
    * and managing duty cycle limitations.
    *
    * <AUTHOR> Technologies
    * @date 2023-03-26
    */

   /**
    * @brief Measures the duty cycle of a PWM signal on specified pin
    *
    * @param pin Arduino pin number to read
    * @return byte Duty cycle as percentage (0-100)
    */
   byte GetPWM(byte pin);
   ```

3. **Create System Architecture Documentation**
   - Document the overall system architecture in `docs/architecture.md`:

   ```markdown
   # System Architecture

   ## Overview
   The ATTINY Control System manages PWM signals, air pressure, and pump control 
   based on sensor inputs. It's designed to operate on an ATTINY85 microcontroller.

   ## Component Diagram
   ```mermaid
   graph TD
     A[PWM Input] --> B[PWM Control Module]
     B --> C[PWM Output]
     B --> D[Pump Control]
     E[Sonicator Input] --> F[Air Pressure Control]
     F --> G[Air Output]
     H[EEPROM] <--> F
   ```

   ## Modules
   1. **PWM Control** - Processes input PWM signals and controls output
   2. **Air Pressure** - Manages air pressure based on sonicator input
   3. **Pump Control** - Controls pump operation based on PWM signals
   4. **Sonicator Interface** - Handles sonicator signal detection
   5. **Timer** - Manages timing functionality for operation
   6. **EEPROM Management** - Handles persistent storage

   ## Signal Flow
   1. PWM input signal is read from PIN 4
   2. Signal is processed and output to PIN 1 with duty cycle capped at 77%
   3. When input duty cycle exceeds 94%, pump is turned off
   4. When sonicator is ON (LOW signal), air pressure is turned ON
   5. When sonicator is OFF, air pressure is turned OFF after 5 minutes
   ```

4. **User Documentation**
   - Create clear user-focused documentation in `docs/user_manual.md`:

   ```markdown
   # Cannasol ATTINY Control System
   ## User Manual

   ### Overview
   This device controls air pressure and pump functionality based on inputs from 
   a sonicator and PWM signals. The system runs on an ATTINY85 microcontroller.

   ### Connections
   - **PIN PB0 (Air Out)**: Air control output (HIGH = ON, LOW = OFF)
   - **PIN PB1 (PWM Out)**: PWM signal output for flow control
   - **PIN PB2 (Pump Out)**: Pump control output (LOW = ON, HIGH = OFF)
   - **PIN PB3 (Sonic In)**: Sonicator input (LOW = ON, HIGH = OFF)
   - **PIN PB4 (PWM In)**: PWM signal input for flow control
   - **PIN PB5 (Status Out)**: Status indicator output

   ### Operation
   1. **PWM Flow Control**:
      - Input PWM signal on PIN 4 controls output PWM on PIN 1
      - Output PWM is capped at 77% duty cycle
      - When input exceeds 94% duty cycle, pump is automatically shut off

   2. **Air Pressure Control**:
      - When sonicator is ON (LOW signal), air pressure is automatically turned ON
      - When sonicator turns OFF, air pressure remains ON for 5 minutes, then turns OFF
      - Air pressure state is saved to EEPROM for persistence across power cycles

   ### LED Indicators
   - **Status LED**: Indicates system operation status

   ### Troubleshooting
   - If pump doesn't turn on, check PWM input signal is below 94% duty cycle
   - If air pressure doesn't turn on, verify sonicator signal is reaching PIN 3
   - For persistent problems, check power supply and connections
   ```

5. **Developer Guide**
   - Create a comprehensive developer guide in `docs/developer_guide.md`:

   ```markdown
   # Developer Guide

   ## Development Environment Setup
   1. **Required Tools**:
      - avr-gcc compiler
      - avrdude for firmware upload
      - CMake for build management
      - Unity for unit testing
      - Simulavr for emulation testing

   2. **Building the Project**:
      ```bash
      mkdir build && cd build
      cmake ..
      make
      ```

   3. **Uploading to ATTINY85**:
      ```bash
      avrdude -c usbtiny -p attiny85 -U flash:w:firmware.hex
      ```

   ## Code Organization
   - **src/**: Source code organized by module
   - **tests/**: Unit and emulation tests
   - **tools/**: Development and build tools

   ## Test Process
   1. **Unit Tests**: Verify individual function behavior
      ```bash
      cd tests/unit && make test
      ```

   2. **Emulation Tests**: Test hardware behavior using Simulavr
      ```bash
      cd tests/emulation && pytest
      ```

   ## Adding Features
   1. Identify the appropriate module for your feature
   2. Add function declarations to header file
   3. Implement functions in source file
   4. Create unit and emulation tests
   5. Update documentation
   6. Submit a pull request

   ## Code Style Guidelines
   - Use clear, descriptive variable and function names
   - Document all functions with Doxygen comments
   - Keep functions small and focused on a single task
   - Follow the existing modular approach
   ```

6. **Technical Reference**
   - Create a detailed technical reference in `docs/technical_reference.md`:

   ```markdown
   # Technical Reference

   ## Hardware Specifications
   - **Microcontroller**: ATTINY85
   - **Clock Speed**: 8 MHz internal oscillator
   - **Memory**: 8KB Flash, 512B EEPROM, 512B SRAM

   ## Pin Assignments
   | Pin | Name        | Direction | Description                      |
   |-----|-------------|-----------|----------------------------------|
   | PB0 | AIR_OUT     | Output    | Air control (HIGH=ON, LOW=OFF)   |
   | PB1 | PWM_OUT     | Output    | PWM signal output                |
   | PB2 | PUMP_OUT    | Output    | Pump control (LOW=ON, HIGH=OFF)  |
   | PB3 | SONIC_IN    | Input     | Sonicator input (LOW=ON)         |
   | PB4 | PWM_IN      | Input     | PWM signal input                 |
   | PB5 | STATUS_OUT  | Output    | Status indicator LED             |

   ## EEPROM Usage
   | Address | Size | Description      |
   |---------|------|------------------|
   | 0       | 1B   | Air status state |

   ## Timer Usage
   - **Timer0**: Used for PWM output generation
   - **Timer1**: Used for timing the 5-minute delay for air pressure

   ## Interrupt Vectors
   - **TIM1_COMPA_vect**: Timer1 compare match interrupt, used for air pressure control timing

   ## Constants
   | Name                | Value | Description                             |
   |---------------------|-------|-----------------------------------------|
   | INTERRUPT_COUNT_MAX | 579   | 5-minute countdown for air pressure     |
   | PWM_DUTY_CYCLE_CAP  | 77    | Maximum duty cycle percentage for output|
   | PWM_PUMP_THRESHOLD  | 94    | Duty cycle threshold to turn pump off   |
   ```

7. **Documentation Scripts**
   - Create a script to generate documentation automatically:

   ```bash
   #!/bin/bash
   # tools/generate_docs.sh

   # Generate Doxygen documentation
   doxygen Doxyfile

   # Convert markdown to HTML (needs pandoc)
   for file in docs/*.md; do
     filename=$(basename -- "$file")
     name="${filename%.*}"
     pandoc "$file" -o "docs/html/${name}.html" --standalone --metadata title="${name}"
   done

   echo "Documentation generated in docs/html/"
   ```

8. **README Update**
   - Update the project README with clear documentation information:

   ```markdown
   # Cannasol ATTINY Control System

   Microcontroller firmware for PWM control, air pressure, and pump management
   using ATTINY85 microcontrollers.

   ## Features
   - PWM signal processing with duty cycle capping
   - Automated pump control based on PWM thresholds
   - Air pressure control based on sonicator input
   - Persistent state storage using EEPROM

   ## Documentation
   - [User Manual](docs/user_manual.md) - For end users
   - [Technical Reference](docs/technical_reference.md) - Hardware details
   - [Developer Guide](docs/developer_guide.md) - For contributors
   - [Architecture](docs/architecture.md) - System design
   - [API Reference](https://sboyett31.github.io/attiny-control/) - Generated API docs

   ## Getting Started
   See the [Developer Guide](docs/developer_guide.md) for information on
   building, testing, and deploying the firmware.

   ## License
   Copyright (c) 2023 Cannasol Technologies
   ```

9. **Documentation Integration with CI/CD**
   - Ensure documentation is automatically generated and deployed
   - Add documentation validation step

10. **Setup Automated Documentation Updates**
    - Configure GitHub Pages for hosting documentation
    - Ensure documentation is rebuilt on code changes

## Verification
- Run Doxygen and verify HTML documentation is generated properly
- Review all documentation files for accuracy and completeness
- Ensure documentation builds successfully in CI pipeline
- Verify documentation is accessible via GitHub Pages

## Next Steps
- After setting up the documentation generation, proceed to `7-google-cloud-integration.md`