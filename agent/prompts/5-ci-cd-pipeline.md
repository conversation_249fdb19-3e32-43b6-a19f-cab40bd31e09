## Purpose
Implement a comprehensive CI/CD pipeline using GitHub Actions to automate testing, validation, and deployment of the ATTINY control system.

## Instructions

1. **Define GitHub Workflow File**
   - Create `.github/workflows/ci-cd.yml` with the following structure:

   ```yaml
   name: ATTINY Control CI/CD Pipeline

   on:
     push:
       branches: [ main, develop ]
     pull_request:
       branches: [ main, develop ]
     workflow_dispatch:
       # Allow manual triggering

   jobs:
     build-and-test:
       runs-on: ubuntu-latest
       container:
         image: arduino:latest
       
       steps:
         - name: Checkout code
           uses: actions/checkout@v3
         
         - name: Set up build environment
           run: |
             apt-get update && apt-get install -y \
               gcc-avr \
               avr-libc \
               avrdude \
               build-essential \
               cmake \
               python3 \
               python3-pip
         
         - name: Compile firmware
           run: |
             mkdir -p build
             avr-gcc -mmcu=attiny85 -Os -o build/firmware.elf -I src src/*.c
             avr-objcopy -j .text -j .data -O ihex build/firmware.elf build/firmware.hex
             avr-size --format=avr --mcu=attiny85 build/firmware.elf
         
         - name: Run unit tests
           run: |
             cd tests/unit
             cmake .
             make
             ./run_tests
         
         - name: Upload firmware artifact
           uses: actions/upload-artifact@v3
           with:
             name: firmware
             path: build/firmware.hex
     
     emulation-test:
       needs: build-and-test
       runs-on: ubuntu-latest
       
       steps:
         - name: Checkout code
           uses: actions/checkout@v3
         
         - name: Download firmware artifact
           uses: actions/download-artifact@v3
           with:
             name: firmware
             path: build
         
         - name: Run emulation tests
           uses: docker/build-push-action@v3
           with:
             context: .
             file: ./Dockerfile.emulation
             push: false
             tags: attiny-emulation:latest
             build-args: |
               TEST_MODE=true
         
         - name: Execute emulation tests
           run: docker run --rm attiny-emulation:latest
     
     deploy-to-gcp:
       needs: [build-and-test, emulation-test]
       runs-on: ubuntu-latest
       if: github.ref == 'refs/heads/main' && github.event_name == 'push'
       
       steps:
         - name: Checkout code
           uses: actions/checkout@v3
         
         - name: Download firmware artifact
           uses: actions/download-artifact@v3
           with:
             name: firmware
             path: build
         
         - name: Authenticate to Google Cloud
           uses: google-github-actions/auth@v1
           with:
             credentials_json: ${{ secrets.GCP_SA_KEY }}
         
         - name: Set up Cloud SDK
           uses: google-github-actions/setup-gcloud@v1
           with:
             project_id: cannasol-automation-suite
         
         - name: Upload firmware to GCS
           run: |
             VERSION=$(git describe --tags --always)
             gsutil cp build/firmware.hex gs://cannasol-automation-suite-firmware/attiny-control/firmware-${VERSION}.hex
             gsutil cp build/firmware.hex gs://cannasol-automation-suite-firmware/attiny-control/firmware-latest.hex
         
         - name: Create release notes
           run: |
             echo "ATTINY Control Firmware v${VERSION}" > release-notes.md
             echo "Built on $(date)" >> release-notes.md
             echo "SHA: $(git rev-parse HEAD)" >> release-notes.md
             echo "Build artifacts available at:" >> release-notes.md
             echo "gs://cannasol-automation-suite-firmware/attiny-control/firmware-${VERSION}.hex" >> release-notes.md
         
         - name: Create GitHub Release
           uses: softprops/action-gh-release@v1
           if: startsWith(github.ref, 'refs/tags/')
           with:
             files: |
               build/firmware.hex
               release-notes.md
             body_path: release-notes.md
           env:
             GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
   ```

2. **Google Cloud Setup for Deployments**
   - Create a storage bucket for firmware files
   - Set up service account with appropriate permissions

   ```bash
   # Create storage bucket
   gsutil mb -p cannasol-automation-suite gs://cannasol-automation-suite-firmware
   
   # Set public read permissions (if needed)
   gsutil iam ch allUsers:objectViewer gs://cannasol-automation-suite-firmware
   ```

3. **Service Account Setup**
   - Create a service account with Storage Admin role
   - Generate a JSON key for GitHub Actions
   - Add the key as a GitHub secret named `GCP_SA_KEY`

   ```bash
   # Create service account
   gcloud iam service-accounts create github-actions --display-name="GitHub Actions"
   
   # Assign Storage Admin role
   gcloud projects add-iam-policy-binding cannasol-automation-suite \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/storage.admin"
   
   # Create and download key
   gcloud iam service-accounts keys create key.json \
     --iam-account=<EMAIL>
   ```

4. **Configure GitHub Repository Secrets**
   - Add the following secrets to your GitHub repository:
     - `GCP_SA_KEY`: The content of the service account key JSON file
     - `GCP_PROJECT_ID`: 'cannasol-automation-suite'

5. **Release Process Integration**
   - Implement semantic versioning for releases
   - Create a version tag generation script

   ```bash
   # tools/generate_version.sh
   #!/bin/bash
   
   # Get the latest version tag
   LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
   
   # Extract components
   MAJOR=$(echo $LATEST_TAG | sed 's/v\([0-9]*\)\.\([0-9]*\)\.\([0-9]*\)/\1/')
   MINOR=$(echo $LATEST_TAG | sed 's/v\([0-9]*\)\.\([0-9]*\)\.\([0-9]*\)/\2/')
   PATCH=$(echo $LATEST_TAG | sed 's/v\([0-9]*\)\.\([0-9]*\)\.\([0-9]*\)/\3/')
   
   # Increment version based on parameter
   case "$1" in
     major)
       MAJOR=$((MAJOR + 1))
       MINOR=0
       PATCH=0
       ;;
     minor)
       MINOR=$((MINOR + 1))
       PATCH=0
       ;;
     patch)
       PATCH=$((PATCH + 1))
       ;;
     *)
       echo "Usage: $0 [major|minor|patch]"
       exit 1
       ;;
   esac
   
   NEW_VERSION="v$MAJOR.$MINOR.$PATCH"
   echo $NEW_VERSION
   ```

6. **Add Versioning to Firmware**
   - Create a version header file that's updated during build
   - Ensure version information is embedded in firmware

   ```c
   // src/version.h.in
   #ifndef VERSION_H
   #define VERSION_H

   #define FIRMWARE_VERSION "@PROJECT_VERSION@"
   #define BUILD_DATE "@BUILD_DATE@"
   #define GIT_COMMIT "@GIT_COMMIT@"

   #endif
   ```

   ```cmake
   # Add to CMakeLists.txt
   execute_process(
     COMMAND git describe --tags --always
     OUTPUT_VARIABLE GIT_VERSION
     OUTPUT_STRIP_TRAILING_WHITESPACE
   )
   
   execute_process(
     COMMAND git log -1 --format=%h
     OUTPUT_VARIABLE GIT_COMMIT
     OUTPUT_STRIP_TRAILING_WHITESPACE
   )
   
   string(TIMESTAMP BUILD_DATE "%Y-%m-%d %H:%M:%S")
   
   configure_file(
     "${PROJECT_SOURCE_DIR}/src/version.h.in"
     "${PROJECT_BINARY_DIR}/src/version.h"
   )
   ```

7. **Deployment Environment Validation**
   - Add a post-deployment verification step
   - Implement health check for deployed artifacts

   ```yaml
   # Add to ci-cd.yml under deploy-to-gcp job
   - name: Verify deployment
     run: |
       # Check if the firmware file exists in GCS
       if gsutil stat gs://cannasol-automation-suite-firmware/attiny-control/firmware-latest.hex; then
         echo "Deployment successful!"
       else
         echo "Deployment failed - firmware not found in GCS"
         exit 1
       fi
   ```

8. **Notifications Setup**
   - Configure Slack/Email notifications for build status
   - Set up alerts for deployment success/failure

   ```yaml
   # Add to ci-cd.yml as a new job
   notify:
     needs: [deploy-to-gcp]
     runs-on: ubuntu-latest
     if: always()
     
     steps:
       - name: Check build status
         uses: technote-space/workflow-conclusion-action@v3
       
       - name: Send Slack notification
         uses: 8398a7/action-slack@v3
         with:
           status: ${{ env.WORKFLOW_CONCLUSION }}
           fields: repo,message,commit,author,action,eventName,ref,workflow
         env:
           SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
         if: always()
   ```

9. **Documentation Update Automation**
   - Generate documentation from code comments
   - Deploy documentation to GitHub Pages

   ```yaml
   # Add to ci-cd.yml as a new job
   deploy-docs:
     needs: [build-and-test]
     runs-on: ubuntu-latest
     
     steps:
       - name: Checkout code
         uses: actions/checkout@v3
       
       - name: Install Doxygen
         run: |
           sudo apt-get update
           sudo apt-get install -y doxygen graphviz
       
       - name: Generate documentation
         run: |
           doxygen Doxyfile
       
       - name: Deploy to GitHub Pages
         uses: peaceiris/actions-gh-pages@v3
         with:
           github_token: ${{ secrets.GITHUB_TOKEN }}
           publish_dir: ./docs/html
   ```

## Verification
- Test GitHub Actions workflow locally using act (if possible)
- Verify each job succeeds independently
- Ensure artifacts are correctly passed between jobs
- Confirm deployment to GCS works properly
- Test version tagging and release creation

## Next Steps
- After setting up the CI/CD pipeline, proceed to `6-documentation-generation.md`