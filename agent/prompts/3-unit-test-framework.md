## Purpose
Establish a comprehensive unit testing framework for the ATTINY85 control system using C++ with Google Test or Unity Test.

## Instructions

1. **Test Framework Selection**
   - Use Unity Test framework for C-based testing
   - Alternative: Google Test for C++ interface testing

2. **Test Directory Structure**
   ```
   tests/
   ├── unit/
   │   ├── pwm_control_test.c
   │   ├── air_pressure_test.c
   │   ├── pump_control_test.c
   │   ├── sonicator_test.c
   │   ├── timer_test.c
   │   └── eeprom_manager_test.c
   ├── mocks/
   │   ├── mock_avr_io.h
   │   ├── mock_eeprom.h
   │   └── mock_pin_control.h
   ├── test_runners/
   │   └── all_tests.c
   └── CMakeLists.txt
   ```

3. **Set Up Unity Test Framework**
   ```bash
   # Add Unity as a submodule or download directly
   git submodule add https://github.com/ThrowTheSwitch/Unity.git tests/unity
   ```

4. **Create Mock Objects**
   - Create mock implementations of AVR hardware functions
   - Example mock for EEPROM:

   ```c
   // mock_eeprom.h
   #ifndef MOCK_EEPROM_H
   #define MOCK_EEPROM_H

   #include <stdint.h>

   // Simulated EEPROM memory
   extern uint8_t mock_eeprom_memory[512];
   
   // Mock EEPROM functions
   void mock_EEPROM_write(uint16_t address, uint8_t value);
   uint8_t mock_EEPROM_read(uint16_t address);
   
   // EEPROM library function substitutions
   #define EEPROM_read(addr) mock_EEPROM_read(addr)
   #define EEPROM_write(addr, val) mock_EEPROM_write(addr, val)
   
   #endif
   ```

5. **Create Unit Tests for Each Module**
   - Example test for pump control module:

   ```c
   // pump_control_test.c
   #include "unity.h"
   #include "pump_control.h"
   #include "mocks/mock_pin_control.h"

   // Test fixtures
   void setUp(void) {
     // Initialize before each test
     resetMockPins();
   }

   void tearDown(void) {
     // Clean up after each test
   }

   void test_pump_initialization(void) {
     // Test that initialization sets pump to off state
     initialize_pump();
     TEST_ASSERT_EQUAL(0, getPinState(PUMP_OUT_PIN));
     TEST_ASSERT_FALSE(is_pump_on());
   }

   void test_turn_pump_on(void) {
     // Test turning pump on
     turn_pump_on();
     TEST_ASSERT_EQUAL(1, getPinState(PUMP_OUT_PIN));
     TEST_ASSERT_TRUE(is_pump_on());
   }

   void test_turn_pump_off(void) {
     // Test turning pump off
     turn_pump_on();  // First turn on
     turn_pump_off(); // Then turn off
     TEST_ASSERT_EQUAL(0, getPinState(PUMP_OUT_PIN));
     TEST_ASSERT_FALSE(is_pump_on());
   }

   // Main test runner
   int main(void) {
     UNITY_BEGIN();
     
     RUN_TEST(test_pump_initialization);
     RUN_TEST(test_turn_pump_on);
     RUN_TEST(test_turn_pump_off);
     
     return UNITY_END();
   }
   ```

6. **PWM Control Tests**
   ```c
   // pwm_control_test.c
   #include "unity.h"
   #include "pwm_control.h"
   #include "mocks/mock_avr_io.h"
   #include "mocks/mock_pin_control.h"

   void setUp(void) {
     resetMockPins();
     resetMockRegisters();
   }

   void tearDown(void) {
     // Clean up
   }

   void test_GetPWM_high_signal(void) {
     // Mock a high signal
     setMockPulseTime(PWM_IN_PIN, HIGH, 800);
     setMockPulseTime(PWM_IN_PIN, LOW, 200);
     
     // Should be 80% duty cycle
     TEST_ASSERT_EQUAL(80, GetPWM(PWM_IN_PIN));
   }

   void test_flow_pump_control_normal_operation(void) {
     // Test with 50% duty cycle PWM
     setMockPulseTime(PWM_IN_PIN, HIGH, 500);
     setMockPulseTime(PWM_IN_PIN, LOW, 500);
     
     flow_pump_control();
     
     // Check PWM output calculation is correct
     TEST_ASSERT_EQUAL(127, mock_OCR0B); // 50% of 255 is ~127
     TEST_ASSERT_TRUE(is_pump_on());
   }

   void test_flow_pump_control_duty_cycle_cap(void) {
     // Test with 90% duty cycle PWM (above 77% cap)
     setMockPulseTime(PWM_IN_PIN, HIGH, 900);
     setMockPulseTime(PWM_IN_PIN, LOW, 100);
     
     flow_pump_control();
     
     // Check output is capped at 77%
     TEST_ASSERT_EQUAL((int)(255 * 0.77), mock_OCR0B);
   }

   void test_flow_pump_control_pump_shutoff(void) {
     // Test with 95% duty cycle PWM (above 94% threshold)
     setMockPulseTime(PWM_IN_PIN, HIGH, 950);
     setMockPulseTime(PWM_IN_PIN, LOW, 50);
     
     flow_pump_control();
     
     // Pump should be off
     TEST_ASSERT_FALSE(is_pump_on());
   }

   int main(void) {
     UNITY_BEGIN();
     
     RUN_TEST(test_GetPWM_high_signal);
     RUN_TEST(test_flow_pump_control_normal_operation);
     RUN_TEST(test_flow_pump_control_duty_cycle_cap);
     RUN_TEST(test_flow_pump_control_pump_shutoff);
     
     return UNITY_END();
   }
   ```

7. **Air Pressure Control Tests**
   ```c
   // air_pressure_test.c
   #include "unity.h"
   #include "air_pressure.h"
   #include "sonicator.h"
   #include "mocks/mock_pin_control.h"
   #include "mocks/mock_avr_io.h"
   #include "mocks/mock_eeprom.h"

   void setUp(void) {
     resetMockPins();
     resetMockRegisters();
     mock_TCNT1 = 0;
     mock_interrupt_count = 0;
   }

   void tearDown(void) {
     // Clean up
   }

   void test_air_pressure_control_sonicator_on(void) {
     // Mock sonicator on (LOW signal)
     setPinState(SONIC_IN_PIN, LOW);
     
     air_pressure_control();
     
     // Air should be turned on
     TEST_ASSERT_TRUE(is_air_on());
     TEST_ASSERT_EQUAL(HIGH, getPinState(AIR_OUT_PIN));
     TEST_ASSERT_EQUAL(0, mock_TCNT1);
     TEST_ASSERT_EQUAL(0, mock_interrupt_count);
   }

   void test_air_pressure_control_sonicator_off(void) {
     // First with sonicator on to initialize
     setPinState(SONIC_IN_PIN, LOW);
     air_pressure_control();
     
     // Now turn sonicator off
     setPinState(SONIC_IN_PIN, HIGH);
     
     // Simulate timer interrupt reaching max count
     mock_interrupt_count = INTERRUPT_COUNT_MAX + 1;
     
     // Call ISR
     TIM1_COMPA_vect_call();
     
     // Air should be turned off
     TEST_ASSERT_FALSE(is_air_on());
     TEST_ASSERT_EQUAL(LOW, getPinState(AIR_OUT_PIN));
   }

   int main(void) {
     UNITY_BEGIN();
     
     RUN_TEST(test_air_pressure_control_sonicator_on);
     RUN_TEST(test_air_pressure_control_sonicator_off);
     
     return UNITY_END();
   }
   ```

8. **EEPROM Management Tests**
   ```c
   // eeprom_manager_test.c
   #include "unity.h"
   #include "eeprom_manager.h"
   #include "air_pressure.h"
   #include "mocks/mock_eeprom.h"
   #include "mocks/mock_pin_control.h"

   extern bool air_status;
   extern bool prev_air_status;

   void setUp(void) {
     resetMockPins();
     for (int i = 0; i < 512; i++) {
       mock_eeprom_memory[i] = 0;
     }
     air_status = false;
     prev_air_status = false;
   }

   void tearDown(void) {
     // Clean up
   }

   void test_update_air_status_eeprom_when_changed(void) {
     // Initial state
     air_status = true;
     prev_air_status = false;
     
     // Update EEPROM
     update_air_status_eeprom();
     
     // Check EEPROM value is updated
     TEST_ASSERT_EQUAL(0, mock_eeprom_memory[0]); // Inverted in function
     TEST_ASSERT_EQUAL(air_status, prev_air_status);
   }

   void test_read_eeprom(void) {
     // Set mock EEPROM value
     mock_eeprom_memory[0] = 1;
     
     // Read EEPROM
     read_eeprom();
     
     // Status should be inverted from EEPROM value
     TEST_ASSERT_FALSE(air_status);
     TEST_ASSERT_EQUAL(air_status, prev_air_status);
   }

   int main(void) {
     UNITY_BEGIN();
     
     RUN_TEST(test_update_air_status_eeprom_when_changed);
     RUN_TEST(test_read_eeprom);
     
     return UNITY_END();
   }
   ```

9. **Create Test Runner**
   ```c
   // all_tests.c
   #include "unity_fixture.h"

   static void RunAllTests(void) {
     RUN_TEST_GROUP(PumpControl);
     RUN_TEST_GROUP(PWMControl);
     RUN_TEST_GROUP(AirPressure);
     RUN_TEST_GROUP(EEPROMManager);
     RUN_TEST_GROUP(Sonicator);
     RUN_TEST_GROUP(Timer);
     RUN_TEST_GROUP(SystemInit);
   }

   int main(int argc, char **argv) {
     return UnityMain(argc, argv, RunAllTests);
   }
   ```

10. **Setup Build System for Tests**
    - Create a CMakeLists.txt for building and running tests
    - Configure to include required mocks and source files

## Verification
- Verify that all critical functions have appropriate test coverage
- Ensure mocks properly simulate hardware behavior
- Confirm tests run successfully on the development machine

## Next Steps
- After setting up the unit test framework, proceed to `4-emulation-testing.md`