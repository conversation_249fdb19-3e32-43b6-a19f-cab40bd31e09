## Purpose
Integrate the ATTINY control system with Google Cloud Platform for firmware storage, version management, and potential future IoT connectivity.

## Instructions

1. **GCP Project Configuration**
   - Ensure GCP project 'cannasol-automation-suite' is properly set up
   - Configure IAM roles and permissions for CI/CD integration
   - Set up service accounts for automated deployment

   ```bash
   # Set default project
   gcloud config set project cannasol-automation-suite
   
   # Create service account for automation
   gcloud iam service-accounts create attiny-automation \
     --display-name="ATTINY Automation Service Account"
   
   # Assign required roles
   gcloud projects add-iam-policy-binding cannasol-automation-suite \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/storage.admin"
   
   gcloud projects add-iam-policy-binding cannasol-automation-suite \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/cloudbuild.builds.editor"
   ```

2. **Cloud Storage Setup for Firmware Management**
   - Create storage buckets for firmware versions and releases
   - Set up appropriate access controls

   ```bash
   # Create firmware storage bucket
   gsutil mb -p cannasol-automation-suite -l us-central1 gs://cannasol-firmware
   
   # Set lifecycle policy for version retention
   cat > lifecycle.json << EOF
   {
     "rule": [
       {
         "action": {"type": "Delete"},
         "condition": {
           "age": 90,
           "isLive": true,
           "matchesPrefix": ["dev/"]
         }
       }
     ]
   }
   EOF
   
   gsutil lifecycle set lifecycle.json gs://cannasol-firmware
   
   # Set object versioning
   gsutil versioning set on gs://cannasol-firmware
   ```

3. **Firmware Version Management System**
   - Create a Cloud Function for firmware version tracking
   - Set up a metadata tracking system in Firestore

   ```javascript
   // firmware-tracker/index.js
   const {Firestore} = require('@google-cloud/firestore');
   const firestore = new Firestore();
   
   exports.trackFirmwareUpload = async (data, context) => {
     const fileBucket = data.bucket;
     const filePath = data.name;
     
     // Only process firmware files
     if (!filePath.includes('attiny-control') || !filePath.endsWith('.hex')) {
       return;
     }
     
     // Extract version from filename
     const versionMatch = filePath.match(/firmware-v?(\d+\.\d+\.\d+)\.hex$/);
     if (!versionMatch) {
       console.log('No version found in filename:', filePath);
       return;
     }
     
     const version = versionMatch[1];
     const timestamp = new Date();
     
     // Store firmware metadata in Firestore
     try {
       await firestore.collection('firmware').doc(`attiny-${version}`).set({
         version,
         filePath,
         bucket: fileBucket,
         uploadedAt: timestamp,
         fullPath: `gs://${fileBucket}/${filePath}`
       });
       
       console.log(`Tracked firmware version ${version} at ${timestamp}`);
     } catch (error) {
       console.error('Error tracking firmware:', error);
     }
   };
   ```

   ```yaml
   # firmware-tracker/deployment.yaml
   name: firmware-tracker
   runtime: nodejs14
   trigger:
     event: google.storage.object.finalize
     resource: projects/cannasol-automation-suite/buckets/cannasol-firmware
   entryPoint: trackFirmwareUpload
   ```

4. **Cloud Build Integration**
   - Set up Cloud Build for automated firmware building
   - Configure build triggers for GitHub integration

   ```yaml
   # cloudbuild.yaml
   steps:
   - name: 'gcr.io/cloud-builders/docker'
     args: ['build', '-t', 'gcr.io/cannasol-automation-suite/attiny-builder', '.']
     
   - name: 'gcr.io/cannasol-automation-suite/attiny-builder'
     args: ['sh', '-c', 'mkdir -p build && cd build && cmake .. && make']
     
   - name: 'gcr.io/cannasol-automation-suite/attiny-builder'
     args: ['sh', '-c', 'cd build && ctest --output-on-failure']
     
   - name: 'gcr.io/cloud-builders/gsutil'
     args: ['-m', 'cp', 'build/firmware.hex', 'gs://cannasol-firmware/attiny-control/firmware-$TAG_NAME.hex']
     
   - name: 'gcr.io/cloud-builders/gsutil'
     args: ['cp', 'build/firmware.hex', 'gs://cannasol-firmware/attiny-control/firmware-latest.hex']
     
   artifacts:
     objects:
       location: 'gs://cannasol-firmware/attiny-control/builds/'
       paths: ['build/firmware.hex', 'build/firmware.elf']
   ```

5. **Custom Builder for AVR Compilation**
   - Create a custom Docker image for AVR compilation on Cloud Build

   ```dockerfile
   # Dockerfile.builder
   FROM ubuntu:20.04
   
   # Install dependencies
   RUN apt-get update && apt-get install -y \
       build-essential \
       cmake \
       gcc-avr \
       avr-libc \
       avrdude \
       python3 \
       python3-pip \
       git \
       && rm -rf /var/lib/apt/lists/*
   
   # Install Unity test framework
   RUN git clone https://github.com/ThrowTheSwitch/Unity.git /opt/unity
   
   # Set environment variables
   ENV UNITY_PATH=/opt/unity
   
   WORKDIR /workspace
   ```

   ```bash
   # Build and push custom builder
   docker build -t gcr.io/cannasol-automation-suite/attiny-builder -f Dockerfile.builder .
   docker push gcr.io/cannasol-automation-suite/attiny-builder
   ```

6. **Firmware Deployment Web Interface**
   - Create a simple web application for managing firmware versions
   - Deploy using Google App Engine

   ```javascript
   // app.js
   const express = require('express');
   const {Firestore} = require('@google-cloud/firestore');
   const {Storage} = require('@google-cloud/storage');
   
   const app = express();
   const firestore = new Firestore();
   const storage = new Storage();
   const firmwareBucket = storage.bucket('cannasol-firmware');
   
   app.set('view engine', 'ejs');
   app.use(express.static('public'));
   app.use(express.urlencoded({extended: true}));
   
   // List all firmware versions
   app.get('/', async (req, res) => {
     try {
       const snapshot = await firestore.collection('firmware')
         .where('filePath', '>=', 'attiny-control/')
         .orderBy('filePath', 'desc')
         .get();
       
       const versions = [];
       snapshot.forEach(doc => {
         versions.push(doc.data());
       });
       
       res.render('index', {versions});
     } catch (error) {
       res.status(500).send(`Error: ${error.message}`);
     }
   });
   
   // Set a version as current production version
   app.post('/set-production', async (req, res) => {
     const {version} = req.body;
     if (!version) {
       return res.status(400).send('Version is required');
     }
     
     try {
       // Copy the specified version to production
       const sourcePath = `attiny-control/firmware-${version}.hex`;
       const destPath = 'attiny-control/firmware-production.hex';
       
       await firmwareBucket.file(sourcePath).copy(firmwareBucket.file(destPath));
       
       // Update metadata in Firestore
       await firestore.collection('firmware-deployment').doc('production').set({
         version,
         deployedAt: new Date(),
         deployedBy: req.user?.email || 'unknown',
         sourcePath
       });
       
       res.redirect('/?success=Version set as production');
     } catch (error) {
       res.status(500).send(`Error: ${error.message}`);
     }
   });
   
   const PORT = process.env.PORT || 8080;
   app.listen(PORT, () => {
     console.log(`Firmware management app listening on port ${PORT}`);
   });
   ```

   ```yaml
   # app.yaml
   runtime: nodejs14
   service: firmware-manager
   ```

7. **Firmware Analytics**
   - Setup BigQuery dataset for firmware analytics
   - Create Cloud Functions to track firmware downloads and usage

   ```javascript
   // firmware-analytics/index.js
   const {BigQuery} = require('@google-cloud/bigquery');
   const bigquery = new BigQuery();
   
   exports.trackFirmwareDownload = async (req, res) => {
     // Enable CORS
     res.set('Access-Control-Allow-Origin', '*');
     
     if (req.method === 'OPTIONS') {
       res.set('Access-Control-Allow-Methods', 'GET');
       res.set('Access-Control-Allow-Headers', 'Content-Type');
       res.status(204).send('');
       return;
     }
     
     const {version, deviceId} = req.query;
     if (!version || !deviceId) {
       res.status(400).send('Missing required parameters');
       return;
     }
     
     // Track download in BigQuery
     try {
       await bigquery.dataset('firmware_analytics').table('downloads').insert([
         {
           version,
           deviceId,
           timestamp: new Date().toISOString(),
           userAgent: req.headers['user-agent'],
           ipAddress: req.ip
         }
       ]);
       
       res.status(200).send('Download tracked');
     } catch (error) {
       console.error('Error tracking download:', error);
       res.status(500).send('Error tracking download');
     }
   };
   ```

8. **IoT Core Setup for Future Device Management**
   - Configure Google Cloud IoT Core for future device connectivity
   - Set up device registry and communication protocols

   ```bash
   # Create IoT Core registry
   gcloud iot registries create attiny-devices \
     --project=cannasol-automation-suite \
     --region=us-central1 \
     --event-notification-config=topic=projects/cannasol-automation-suite/topics/device-events

   # Create Pub/Sub topics for device communication
   gcloud pubsub topics create device-events
   gcloud pubsub topics create device-commands
   
   # Create subscription for processing device events
   gcloud pubsub subscriptions create process-device-events \
     --topic=device-events
   ```

9. **Production Monitoring Dashboard**
   - Create a Cloud Monitoring dashboard for production devices
   - Set up alerts for anomalous behavior

   ```bash
   # Create custom metrics
   gcloud beta monitoring metrics-scopes create \
     --project=cannasol-automation-suite \
     --scoping-project=cannasol-automation-suite
   
   # Set up alert policy
   gcloud alpha monitoring policies create \
     --display-name="ATTINY Device Offline" \
     --conditions="condition-type=metric, filter=metric.type=\"custom.googleapis.com/attiny/heartbeat\" AND resource.type=\"generic_node\", comparison=COMPARISON_LT, threshold-value=1, duration=300s" \
     --notification-channels="projects/cannasol-automation-suite/notificationChannels/123456789" \
     --combiner=OR
   ```

10. **Security Configuration**
    - Implement secure access to Cloud resources
    - Set up appropriate IAM roles and permissions

    ```bash
    # Create custom IAM role for firmware management
    gcloud iam roles create firmwareManager \
      --project=cannasol-automation-suite \
      --title="Firmware Manager" \
      --description="Manages firmware versions and deployments" \
      --permissions="storage.objects.get,storage.objects.list,storage.objects.create,firestore.documents.create,firestore.documents.update,firestore.documents.get,firestore.documents.list"
    
    # Assign role to authorized users
    gcloud projects add-iam-policy-binding cannasol-automation-suite \
      --member="user:<EMAIL>" \
      --role="projects/cannasol-automation-suite/roles/firmwareManager"
    ```

## Verification
- Verify GCP project setup and permissions
- Test Cloud Storage buckets for firmware storage
- Confirm Cloud Functions deploy and trigger correctly
- Validate firmware version tracking in Firestore
- Test firmware management web interface
- Verify security configurations and access controls

## Next Steps
- After setting up Google Cloud integration, proceed to `8-monitoring-and-alerts.md`