# Component Registry and Code Reuse Documentation

This document serves as a registry of all components, modules, utilities, and services created during development.

## Components

### Core Components

#### PinConfig
- **Purpose**: Manages pin configuration and initialization
- **Interface**: 
  - `static void init()`: Initialize all pins
  - Constants for all pin numbers
- **Dependencies**: Arduino
- **Source Files**: `include/modules/pin_config.h`, `src/modules/pin_config.cpp`

#### PwmControl
- **Purpose**: Handles PWM signal processing and output control
- **Interface**: 
  - `static void init()`: Initialize PWM control
  - `static byte getDutyCycle(byte pin)`: Gets duty cycle from a pin
  - `static void setOutput(byte percentage, byte cappedAt = 77)`: Sets PWM output with capping
  - `static byte processInputOutput()`: Processes input to output signal
- **Dependencies**: Arduino, PinConfig
- **Source Files**: `include/modules/pwm_control.h`, `src/modules/pwm_control.cpp`

#### TimerManager
- **Purpose**: Manages timers and interrupt handling
- **Interface**: 
  - `static void initTimer1()`: Initialize Timer1
  - `static uint16_t getInterruptCount()`: Get interrupt counter
  - `static void setInterruptCount(uint16_t count)`: Set interrupt counter
  - `static void resetCounter()`: Reset timer counter
  - `static uint16_t getMaxInterruptCount()`: Get max interrupt count
- **Dependencies**: Arduino, AirControl
- **Source Files**: `include/modules/timer_manager.h`, `src/modules/timer_manager.cpp`

#### AirControl
- **Purpose**: Manages air pressure control functionality
- **Interface**: 
  - `static void init()`: Initialize air control
  - `static void turnOn()`: Turn air on
  - `static void turnOff()`: Turn air off
  - `static bool isOn()`: Check if air is on
  - `static void process()`: Process air control based on sonicator
  - `static void setStatus(bool status)`: Set air status
- **Dependencies**: Arduino, PinConfig, TimerManager, EEPROM
- **Source Files**: `include/modules/air_control.h`, `src/modules/air_control.cpp`

#### PumpControl
- **Purpose**: Manages pump control functionality
- **Interface**: 
  - `static void init()`: Initialize pump control
  - `static void turnOn()`: Turn pump on
  - `static void turnOff()`: Turn pump off
  - `static bool isOn()`: Check if pump is on
  - `static void process(byte dutyCycle)`: Process pump control based on duty cycle
- **Dependencies**: Arduino, PinConfig
- **Source Files**: `include/modules/pump_control.h`, `src/modules/pump_control.cpp`

#### System
- **Purpose**: High-level system integration
- **Interface**: 
  - `static void init()`: Initialize the entire system
  - `static void process()`: Process system control loop
- **Dependencies**: All other modules
- **Source Files**: `include/modules/system.h`, `src/modules/system.cpp`

## Utility Functions

No standalone utility functions registered yet. Core functionality is encapsulated in the modules above.

## Services

No services registered yet.

## Dependencies

### External Libraries
- PlatformIO Core
- AVR-GCC
- Arduino AVR ATtiny Core
- Unity Test Framework

### Project Dependencies
- EEPROM: Library used for persistence between power cycles 