# ATtiny85 Control System User Manual
Version: {{version}}

![Cannasol Technologies Logo](https://example.com/logo.png)

## Introduction

Thank you for choosing the Cannasol Technologies ATtiny85 Control System. This manual provides instructions for installation, configuration, and operation of the control system firmware.

## Table of Contents

1. [System Overview](#system-overview)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Operation](#operation)
5. [Troubleshooting](#troubleshooting)
6. [Support](#support)

## System Overview

The ATtiny85 Control System is designed for precise timing control of air flow and other peripherals in Cannasol Technologies equipment. The system operates by:

- Reading PWM input signals from a controller
- Managing timing of air solenoid valve activation
- Monitoring system health and reporting errors
- Providing visual status indication

## Installation

### Prerequisites

- ATtiny85 microcontroller (programmed with this firmware)
- 5V power supply
- Controller with PWM output capability
- Air solenoid valve (compatible with ATtiny85 voltage/current specifications)

### Wiring Diagram

```
                       +---------------+
                       |               |
 Controller PWM -------|PB0     VCC    |------ 5V
                       |               |
 Air Solenoid  --------|PB1     PB5/RST|------ Reset Switch
                       |               |
 Status LED    --------|PB2     PB4    |------ Pressure Sensor
                       |               |
 Error Output  --------|PB3     GND    |------ Ground
                       |               |
                       +---------------+
                          ATtiny85
```

### Installation Steps

1. Ensure power is disconnected before installation
2. Connect the ATtiny85 according to the wiring diagram
3. Verify all connections are secure
4. Apply power to the system

## Configuration

The firmware is pre-configured for optimal operation with Cannasol Technologies equipment. Default settings include:

- PWM input frequency: 50-1000 Hz
- Air control timing: 50-500ms (based on PWM duty cycle)
- Safety timeout: 2000ms
- Status LED: Blinks during normal operation, solid during errors

## Operation

### Normal Operation Sequence

1. Apply power to the system
2. The status LED will flash three times to indicate successful initialization
3. The system will begin responding to PWM input signals
4. The air solenoid will activate according to the PWM duty cycle
5. The status LED will blink at a rate proportional to the PWM duty cycle

### PWM Control Ranges

| PWM Duty Cycle | Air Solenoid Timing | LED Behavior |
|----------------|---------------------|--------------|
| 0-10%          | Inactive            | Slow blink   |
| 11-50%         | 50-200ms            | Medium blink |
| 51-90%         | 201-400ms           | Fast blink   |
| 91-100%        | 401-500ms           | Rapid blink  |

## Troubleshooting

### Error Codes (Status LED Pattern)

| Pattern                | Error                     | Solution                                    |
|------------------------|---------------------------|---------------------------------------------|
| Solid ON               | No PWM signal detected    | Check controller connection                 |
| 2 blinks, pause, repeat| Air pressure timeout      | Check air supply and solenoid connections   |
| 3 blinks, pause, repeat| System voltage low        | Check power supply                          |
| 4 blinks, pause, repeat| System error              | Cycle power or contact support              |

### Common Issues

1. **System does not power on**
   - Check power connections
   - Verify 5V power supply is functioning
   - Inspect for shorts or damaged components

2. **Air solenoid does not activate**
   - Verify PWM signal is being received
   - Check solenoid wiring
   - Confirm air pressure is available
   - Test solenoid with direct power (disconnect from microcontroller first)

3. **Erratic operation**
   - Check for loose connections
   - Verify power supply is stable
   - Ensure controller is providing consistent PWM signal

## Support

For technical assistance, please contact Cannasol Technologies support:

- Email: <EMAIL>
- Phone: (*************
- Web: https://www.cannasoltech.com/support

When contacting support, please provide:
- Firmware version: {{version}}
- Serial number of your equipment
- Detailed description of the issue
- Any error codes observed

---

© {{current_year}} Cannasol Technologies. All rights reserved. 