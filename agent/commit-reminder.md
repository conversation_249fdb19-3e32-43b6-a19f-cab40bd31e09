# Git Commit Reminder

To ensure we maintain a good history of changes and don't lose work, please remember to:

1. Commit changes regularly after completing meaningful work
2. Use descriptive commit messages that explain what was changed and why
3. Push changes to GitHub after significant milestones

## Sample Git Workflow

```bash
# Check status
git status

# Stage changes (exclude credentials)
git add -A .
git reset agent/credentials/

# Commit with descriptive message
git commit -m "Add [feature]: [brief description of changes]"

# Push to GitHub
git push origin main
```

## Commit Message Prefixes

- `Add`: New feature or file
- `Fix`: Bug fix
- `Update`: Enhancement to existing feature
- `Refactor`: Code restructuring
- `Test`: Adding or updating tests
- `Docs`: Documentation updates
- `CI`: Changes to CI/CD pipeline
- `Style`: Formatting, no code change
- `Perf`: Performance improvements 