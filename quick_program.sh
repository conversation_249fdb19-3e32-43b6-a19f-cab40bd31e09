#!/bin/bash

# Quick ATtiny85 Programming Script
# For use with Arduino R4 WiFi as ISP

echo "Quick ATtiny85 Programming"
echo "=========================="
echo
echo "Make sure your connections are correct:"
echo "- Arduino pin 13 → ATtiny85 pin 7 (SCK)"
echo "- Arduino pin 12 → ATtiny85 pin 6 (MISO)"
echo "- Arduino pin 11 → ATtiny85 pin 5 (MOSI)"
echo "- Arduino pin 10 → ATtiny85 pin 1 (RESET)"
echo "- Arduino 5V   → ATtiny85 pin 8 (VCC)"
echo "- Arduino GND  → ATtiny85 pin 4 (GND)"
echo
echo "Press ENTER when ready or Ctrl+C to cancel"
read

# Set the path to avrdude
AVRDUDE="$HOME/.platformio/packages/tool-avrdude/bin/avrdude"
CONF="$HOME/.platformio/packages/tool-avrdude/avrdude.conf"

# Find Arduino port
PORT=$(ls /dev/tty.usb* 2>/dev/null | head -n 1)

if [ -z "$PORT" ]; then
    echo "❌ No Arduino found! Check connections."
    exit 1
fi

echo "Found Arduino at: $PORT"
echo "Starting programming..."

# Program the ATtiny85
"$AVRDUDE" -C "$CONF" -v -pattiny85 -carduino -P"$PORT" -b19200 -e -Uflash:w:.pio/build/attiny85/firmware.hex:i -F

if [ $? -eq 0 ]; then
    echo
    echo "✅ Programming successful!"
else
    echo
    echo "❌ Programming failed. Try the following:"
    echo "1. Check all connections"
    echo "2. Press the reset button on Arduino just before programming"
    echo "3. Try a different ATtiny85 chip"
fi 