## Implementation Instructions

The implementation will follow the defined prompt files in sequence, with each file building on the previous ones. The development process will adhere to the following principles:

1. **Complete Implementation**: Each prompt file's requirements must be fulfilled completely before proceeding to the next file. This ensures a solid foundation for subsequent development steps and prevents incomplete or fragmented implementation.

2. **Modular Design**: The code should be organized into well-defined modules with clear interfaces. Each function should have a single responsibility, and modules should encapsulate related functionality. This approach enhances maintainability, testability, and code reuse.

3. **Object-Oriented Principles**: Even though working in C, apply object-oriented design principles where applicable, using structures and function pointers to simulate object behavior when beneficial. Keep related data and the functions that operate on that data together.

4. **Comprehensive Documentation**: All code must be thoroughly documented with clear comments explaining functionality, parameters, return values, and side effects. Documentation should be generated using Doxygen and maintained in sync with code changes.

5. **Test-Driven Development**: Write tests before or concurrently with implementation code. Ensure all functionality has corresponding test coverage, including normal operation, edge cases, and error conditions.

6. **CI/CD Integration**: Implement automated build, test, and deployment processes using GitHub Actions. Ensure that all tests are run automatically on code changes and that deployment follows a controlled release process.

7. **Security Considerations**: Implement appropriate security measures for cloud resources, ensure secure deployment processes, and follow best practices for credential management.

8. **Performance Optimization**: Optimize code for the constrained ATTINY85 environment, considering memory usage, execution speed, and power consumption.

9. **Robust Error Handling**: Implement comprehensive error detection and recovery mechanisms. The system should handle unexpected conditions gracefully.

10. **Version Control**: Maintain a clear version history with semantic versioning. Tag releases appropriately and document changes thoroughly.

By following these principles and the detailed instructions in each prompt file, the implementation will result in a professional, maintainable, and robust ATTINY85 control system suitable for a corporate production environment.You are responsible for ensuring consistency and proper structure across the established markdown files when generating code based on the following prompt files. Each prompt file outlines a specific feature or component to be implemented:

1. **1-initial-prompt.md**: Project setup and initialization
2. **2-source-code-organization.md**: Refactoring and organizing ATTINY code into modular components
3. **3-unit-test-framework.md**: Establishing comprehensive unit testing framework
4. **4-emulation-testing.md**: Setting up hardware emulation testing with Simulavr
5. **5-ci-cd-pipeline.md**: Implementing CI/CD pipeline using GitHub Actions
6. **6-documentation-generation.md**: Creating comprehensive documentation with Doxygen
7. **7-google-cloud-integration.md**: Integrating with Google Cloud Platform for firmware storage
8. **8-monitoring-and-alerts.md**: Setting up monitoring and alerting systems
9. **9-final-integration-testing.md**: Implementing end-to-end integration testing
10. **10-production-deployment.md**: Establishing production deployment procedures

## Software Stack

- **Frontend Framework**: None (embedded system)
- **Backend Framework**: None (embedded system)
- **Database**: Google Firestore for device/firmware tracking
- **State Management**: Custom C implementation for ATTINY85
- **UI Component Library**: None (embedded system)
- **API Integration**: Google Cloud APIs
- **Testing Framework**: Unity Test for C code, Simulavr for emulation
- **Build Tools**: AVR-GCC, CMake, Makefile
- **Deployment Platform**: Google Cloud Storage, GitHub Actions
- **Version Control**: Git/GitHub

## Documentation and File Maintenance

Throughout the development process, you will maintain several critical documentation files to ensure organization, prevent duplication, and track progress. After completing each task, you must update these files to reflect the current state of the project:

1. **agent/implementation-plan.md**: The primary task tracking document that lists all pending and completed tasks with their statuses. Update this file immediately after completing any task by changing its checkbox from `[ ]` to `[x]`.

2. **agent/component-registry.md**: The comprehensive inventory of all components, services, and utilities created during development. Document each new component with its purpose, API, dependencies, and usage examples.

3. **agent/test-coverage.md**: The detailed record of test coverage for all implemented features. Update this file with new test scenarios, edge cases, and coverage metrics for each completed feature.

4. **agent/codebase-structure.md**: A hierarchical representation of the project's file and directory structure, including relationships between components and modules. Revise this document whenever new files are added or existing ones are modified.

5. **agent/development-notes.md**: A chronological log of implementation decisions, architectural choices, and important considerations. Document any significant decisions made during implementation, especially those that deviate from initial plans.

6. **agent/context-memory.md**: The agent's persistent memory file that preserves working knowledge between sessions. This critical file must be updated at the conclusion of each working session and contains:
   - Current implementation phase and specific task in progress
   - Contextual understanding of the overall system architecture
   - Recent implementation decisions and their rationale
   - Identified challenges and planned approaches for resolution
   - References to specific code areas that require attention
   - Working hypotheses about system behavior and design trade-offs
   - Search patterns used to locate relevant code sections
   - Documentation of any partial implementations
   - Human guidance explicitly requested to be remembered

7. **agent/version-history.md**: A chronological record of all version tags, releases, and significant commits. This file should document the semantic version number, date, associated tasks completed, and key changes for each version.

8. **agent/credentials/**: A directory containing credential files required for various services:
   - **agent/credentials/github.md**: GitHub access credentials for repository operations
   - Additional credential files should be created whenever new service credentials are required

9. **agent/requests_to_human.md**: A communication channel for making requests to the human user. This file should be used to:
   - Request credentials or API keys needed for implementation
   - Ask for clarification on requirements or implementation details
   - Notify the human when new credential files have been created and need to be populated
   - Request feedback on implementation decisions or approaches
   - Highlight issues that require human intervention

10. **agent/documentation-reviews.md**: A record of periodic documentation reviews, consolidation efforts, and improvements to the documentation system.

After completing each task, reference these files by:
- Marking the task as complete in the implementation plan
- Registering any new components in the component registry
- Updating test coverage documentation with new tests
- Revising the codebase structure to reflect changes
- Adding relevant notes about implementation decisions
- Refreshing the context memory with latest working knowledge
- Creating a commit following the Version Control Protocol and pushing to GitHub
- Updating the version history when appropriate for significant milestones
- Using the appropriate credentials from the credentials directory for authentication
- Communicating any necessary requests to the human via requests_to_human.md

Each of these documents should be reviewed and updated before proceeding to the next task to maintain a complete and accurate representation of the project's state.

## Human Guidance Retention

To ensure important guidance, preferences, and decisions from human-agent interactions are properly preserved:

1. **Capturing Key Insights**
   - When the human explicitly uses phrases like "memorize this," "save this to memory," or "remember this for future reference," immediately document the information in context-memory.md
   - Create a dedicated "Human Guidance" section in the context-memory file for these insights
   - Structure entries with timestamps, context of the conversation, and the exact guidance provided
   - Tag entries with relevant categories (e.g., #CodeStyle, #ProjectPreference, #ImplementationDecision)

2. **Guidance Application**
   - Review the Human Guidance section during the Session Resumption Procedure
   - Prioritize these explicit human preferences when making implementation decisions
   - Reference the specific guidance entry when applying the preference in future work
   - If guidance appears to conflict with other requirements, seek clarification

3. **Guidance Maintenance**
   - Include Human Guidance entries in documentation reviews
   - Consolidate related guidance points to form coherent preference patterns
   - Flag potentially outdated guidance for verification with the human
   - Maintain a version history of guidance to track evolution of preferences

4. **Guidance Integration**
   - Incorporate human guidance into relevant documentation beyond context-memory.md
   - Update component-registry.md with style preferences for specific components
   - Add implementation principles to development-notes.md based on guidance
   - Reference human guidance in commit messages when applying specific preferences

## Documentation Maintenance and Review

To prevent documentation bloat and ensure all materials remain accurate and relevant, implement a structured review process throughout the development lifecycle:

1. **Scheduled Documentation Reviews**
   - Conduct thorough documentation reviews at key milestones:
     - After completing each major feature implementation
     - When reaching significant version increments (e.g., MINOR version changes)
     - When transitioning between major implementation phases
   - Schedule additional reviews if documentation expands rapidly or becomes unwieldy

2. **Review Process**
   - Analyze all documentation files for accuracy, completeness, and relevance
   - Remove outdated information and consolidate redundant content
   - Ensure consistent terminology and formatting across all documents
   - Verify that all references to code structures remain accurate
   - Update architectural diagrams and process flows to match current implementation
   - Validate that component registry entries match actual codebase components

3. **Documentation Refactoring**
   - Identify opportunities to restructure documentation for improved clarity
   - Consolidate fragmented information that belongs together
   - Extract common patterns or frequently referenced information into dedicated sections
   - Archive historical information that remains valuable but isn't immediately relevant
   - Create appropriate cross-references between related documentation

4. **Review Documentation**
   - After each review, document the changes made in agent/documentation-reviews.md
   - Include the date, scope of review, files modified, and summary of changes
   - Note any significant restructuring or consolidation decisions
   - Identify areas that may need deeper review in the future
   - Track documentation health metrics over time (e.g., file sizes, information density)

5. **Knowledge Integration**
   - Following each review, update context-memory.md with the refined understanding
   - Ensure the current architectural understanding reflects documentation improvements
   - Incorporate key insights from the review into future implementation work
   - Update the implementation plan with any new tasks identified during the review

This structured approach to documentation maintenance ensures that the knowledge base remains valuable and accessible throughout the development process, preventing the accumulation of outdated or contradictory information while preserving the comprehensive nature of the documentation system.

## Credential Management

1. **Credential Discovery and Creation**
   - Whenever a new external service, API, or authentication mechanism is required, create a new credential file in the agent/credentials/ directory
   - Name the file according to the service (e.g., agent/credentials/aws.md, agent/credentials/stripe.md)
   - Structure the credential file with appropriate sections for that service
   - Include placeholders for all required credentials
   - Document clear instructions for obtaining and configuring the credentials

2. **Credential Request Process**
   - After creating a new credential file, add an entry to agent/requests_to_human.md
   - Clearly explain what credentials are needed and why they are required
   - Provide instructions for how the human should fill out the credential file
   - Include any relevant links or documentation for obtaining the credentials
   - Reference the specific tasks that are blocked pending credential provision

3. **Credential Usage**
   - Always reference the appropriate credential file when implementing service integrations
   - Never hardcode credentials directly in implementation code
   - Use environment variables or secure credential managers when appropriate
   - Document which credentials are being used in the development notes

## Session Continuity Protocol

To ensure seamless continuity between development sessions:

1. **End-of-Session Procedure**
   - Before concluding any session, update the context-memory.md file with all relevant working knowledge
   - Document the exact task currently in progress with specific details about implementation status
   - Capture all relevant context that would be needed to resume work efficiently
   - Save any temporary working notes or partial solutions
   - Review requests_to_human.md to ensure all pending requests are clear and up-to-date

2. **Session Resumption Procedure**
   - Begin each new session by loading the context-memory.md file
   - Review the implementation-plan.md to confirm the current task and overall progress
   - Examine the component-registry.md to reacquaint with the component architecture
   - Inspect the codebase-structure.md to understand the current state of the project
   - Review development-notes.md for recent decisions that impact the current work
   - Verify access to necessary credentials in the credentials directory
   - Check requests_to_human.md for any responses or newly provided information

3. **Context Verification**
   - After reviewing documentation, verify understanding by summarizing the current state
   - Identify any potential knowledge gaps that need to be addressed
   - Confirm understanding of the immediate next steps before proceeding
   - Ensure all required authentication is properly configured
   - Acknowledge any human responses to previous requests

This protocol ensures that development can proceed efficiently even when context is lost between sessions, minimizing ramp-up time and preventing redundant or inconsistent work.

## First Steps 
1. **Implementation-plan** 
   - Create and maintain the file agent/implementation-plan.md
   - This document serves as the definitive task tracking system
   - Each task must be formatted as a markdown checkbox item:
     - `[ ]` indicates a pending task
     - `[x]` indicates a completed task
   - Tasks must be organized by feature implementation based on prompt files
   - Always refer to this document before beginning any new implementation
   - Update task status immediately upon completion
   - Never remove tasks, only mark them complete

## Code Quality and Duplication Prevention

1. **Pre-implementation Analysis**
   - Before implementing any new feature or component, conduct a thorough analysis of the existing codebase
   - Identify any partial, broken, or similar implementations that may already exist
   - Document findings in the implementation plan with references to existing code
   - Determine whether to refactor, extend, or replace existing implementations

2. **Duplication Prevention Strategy**
   - Maintain a component registry in file agent/component-registry.md titled "Component Registry and Code Reuse Documentation"
   - Document all created components, utility functions, and services with their purposes, interfaces, and dependencies
   - Follow the DRY (Don't Repeat Yourself) principle across all implementations
   - Create abstractions for common patterns that emerge during development
   - When similar functionality is needed, always extend or reuse existing code

3. **Code Review Process**
   - After completing each implementation task, review for potential duplication
   - Verify proper integration with existing architecture
   - Ensure consistent naming conventions and code style
   - Document any technical debt or areas for future refactoring

## Version Control Protocol

1. **Authentication and Setup**
   - Verify Git credentials are properly configured before starting any development work
   - Use the credentials stored in agent/credentials/github.md for authentication
   - Ensure successful authentication with the GitHub repository
   - Confirm access permissions are sufficient for the required operations
   - Verify the local repository is on the correct branch before making any changes
   - Pull the latest changes from the remote repository to ensure synchronized state

2. **Commit Strategy**
   - Create a commit after completing each discrete task in the implementation plan
   - Include the task ID or reference number in the commit message for traceability
   - Use a consistent commit message format: `[Task-ID] Brief description of changes`
   - Include relevant details about implementation decisions in the commit description
   - Separate commits for feature implementation and test implementation when appropriate

3. **Commit Content Guidelines**
   - Each commit should represent a logical, self-contained change
   - Avoid combining unrelated changes in a single commit
   - Include all relevant files needed for the feature to function correctly
   - Ensure the codebase builds successfully and passes all tests before committing
   - Do not commit partial implementations unless explicitly marking them as work-in-progress

4. **GitHub Integration**
   - Push to GitHub after each significant milestone or completed task
   - Create descriptive branch names based on the feature or task being implemented
   - Include references to related documentation files in commit messages
   - Tag commits that represent completion of major implementation phases
   - Update remote repository after updating all documentation files

5. **Version Tagging**
   - Create version tags after completing tasks and subtasks in the implementation plan
   - Use semantic versioning format (MAJOR.MINOR.PATCH) for version tags
   - Document version history in agent/version-history.md with detailed release notes

## Feature Implementation Process

1. **Initial Project Setup (1-initial-prompt.md)**
   - Follow all setup requirements precisely
   - Generate the initial codebase as specified
   - Document the project structure
   - Verify all initialization steps are complete
   - Only proceed after successfully completing the initial setup
   - Log completion status in the implementation plan

2. **Subsequent Feature Implementations**
   - Process each prompt file in the order provided or as directed by the human
   - Each prompt file represents a discrete feature to implement
   - Complete all aspects of a feature before moving to the next prompt file
   - Verify feature functionality against requirements
   - Document component interfaces and dependencies in the component registry
   - Ensure comprehensive test coverage for each feature
   - Mark features as complete only when fully implemented and tested
   - Update all documentation after completing each feature

3. **Feature Dependencies**
   - Identify and document dependencies between features
   - Implement foundation features before dependent features
   - When feature dependencies exist across prompt files, sequence implementation accordingly
   - Communicate any dependency conflicts via requests_to_human.md

4. **Feature Completion Verification**
   - Review the feature against all specified requirements
   - Ensure all aspects of the prompt file have been addressed
   - Verify all tests are passing for the feature
   - Confirm the feature integrates properly with existing functionality
   - Update implementation-plan.md with completion status
   - Create a version tag for the completed feature
 
## Critical Processing Rules 
 
- Process each prompt file as a complete feature unit
- Only mark a feature as complete when ALL requirements are fulfilled
- NO partial feature implementations
- NO skipping between unrelated features unless explicitly directed
- COMPLETE verification of each feature
- MAINTAIN structural integrity across the application
- ENHANCE existing code without breaking established functionality
- COMMIT code and documentation after EACH completed task
- PUSH to GitHub after each significant milestone
- MAINTAIN detailed version history with semantic versioning
 
## Completion Requirements 
 
- Each feature MUST be fully implemented according to its prompt file
- ALL code MUST be generated and verified
- Features MUST be implemented in an order that respects dependencies
- NO mixing implementation steps between unrelated features
- All features MUST have appropriate test coverage before being marked complete
- NO task can be considered complete without passing all relevant tests

## Testing Requirements

1. **Test-Driven Development**
   - Implement tests concurrently with each feature rather than as an afterthought
   - Create test files alongside implementation files using the same directory structure
   - Ensure each module has corresponding test coverage before marking its task complete
   - Document all test expectations and scenarios in the implementation plan

2. **Testing Levels**
   - Unit Tests: Write tests for individual functions and components in isolation
   - Integration Tests: Verify interactions between related components and modules
   - End-to-End Tests: Validate complete user flows and critical application paths
   - Accessibility Tests: Ensure the application meets accessibility standards (WCAG)

3. **Testing Process**
   - Before implementation: Define testable requirements and acceptance criteria
   - During implementation: Write unit tests that validate component behavior
   - After implementation: Conduct integration testing with dependent systems
   - Prior to completion: Execute the full test suite to verify system stability

4. **Test Documentation**
   - Maintain a test coverage report in agent/test-coverage.md
   - Document edge cases and important test scenarios
   - Include examples of expected inputs and outputs for complex functions
   - Update test documentation when refactoring or extending functionality

5. **Test Quality Metrics**
   - Achieve minimum code coverage as defined in the software stack requirements
   - Ensure all tests are deterministic (consistently pass or fail)
   - Minimize test execution time through proper mocking and efficient test design
   - Validate tests by temporarily introducing failures to confirm detection
 
## UI Best Practices 
 
- Ensure layout components (sidebar/nav) extend full viewport height and width using `min-h-screen` and `w-full`. 
- MUST maintain readable contrast by avoiding light gray text (#808080 or lighter) on white backgrounds.