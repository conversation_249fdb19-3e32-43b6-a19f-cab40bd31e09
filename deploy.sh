#!/bin/bash
# ATTINY Control System Deployment Script
# This script handles the deployment of firmware to different environments

# Exit on error
set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
VERSION=""
CANARY=0
ROLLOUT=100
DRY_RUN=false
FORCE=false
SKIP_TESTS=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ID="cannasol-automation-suite"
REGION="us-central1"

# Load environment variables if .env file exists
if [ -f "$SCRIPT_DIR/.env" ]; then
    source "$SCRIPT_DIR/.env"
fi

function print_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --environment <env>   Deployment environment: development, staging, production (default: development)"
    echo "  --version <version>   Version to deploy (required, format: v1.x.x)"
    echo "  --canary <percent>    Percentage for canary deployment (default: 0, ignored for development)"
    echo "  --rollout <percent>   Percentage for full rollout (default: 100, ignored for development)"
    echo "  --dry-run             Show what would be done without actually deploying"
    echo "  --force               Force deployment, bypassing confirmation prompts"
    echo "  --skip-tests          Skip running tests before deployment"
    echo "  --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --environment=development --version=v1.0.0"
    echo "  $0 --environment=staging --version=v1.0.0 --canary=10"
    echo "  $0 --environment=production --version=v1.0.0 --canary=5 --rollout=25"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    arg="$1"
    case $arg in
        --environment=*)
            ENVIRONMENT="${arg#*=}"
            shift
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --version=*)
            VERSION="${arg#*=}"
            shift
            ;;
        --version)
            VERSION="$2"
            shift 2
            ;;
        --canary=*)
            CANARY="${arg#*=}"
            shift
            ;;
        --canary)
            CANARY="$2"
            shift 2
            ;;
        --rollout=*)
            ROLLOUT="${arg#*=}"
            shift
            ;;
        --rollout)
            ROLLOUT="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Error: Unknown option: $arg${NC}"
            print_usage
            exit 1
            ;;
    esac
done

# Validate arguments
if [ -z "$VERSION" ]; then
    echo -e "${RED}Error: Version is required${NC}"
    print_usage
    exit 1
fi

# Validate version format
if ! [[ $VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "${RED}Error: Version must be in format v1.x.x${NC}"
    exit 1
fi

# Validate environment
if ! [[ "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    echo -e "${RED}Error: Environment must be one of: development, staging, production${NC}"
    exit 1
fi

# Validate canary and rollout percentages
if ! [[ "$CANARY" =~ ^[0-9]+$ ]] || [ "$CANARY" -gt 100 ]; then
    echo -e "${RED}Error: Canary must be a number between 0 and 100${NC}"
    exit 1
fi

if ! [[ "$ROLLOUT" =~ ^[0-9]+$ ]] || [ "$ROLLOUT" -lt 1 ] || [ "$ROLLOUT" -gt 100 ]; then
    echo -e "${RED}Error: Rollout must be a number between 1 and 100${NC}"
    exit 1
fi

# Define environment-specific settings
case "$ENVIRONMENT" in
    development)
        BUCKET_NAME="attiny-dev-firmware"
        REQUIRED_APPROVALS=0
        ;;
    staging)
        BUCKET_NAME="attiny-staging-firmware"
        REQUIRED_APPROVALS=1
        ;;
    production)
        BUCKET_NAME="attiny-prod-firmware"
        REQUIRED_APPROVALS=2
        ;;
esac

# Display deployment configuration
echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}  ATTINY Control System Deployment${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""
echo -e "Environment: ${GREEN}$ENVIRONMENT${NC}"
echo -e "Version:     ${GREEN}$VERSION${NC}"
echo -e "Bucket:      ${GREEN}$BUCKET_NAME${NC}"

if [ "$ENVIRONMENT" != "development" ]; then
    echo -e "Canary:      ${GREEN}$CANARY%${NC}"
    echo -e "Rollout:     ${GREEN}$ROLLOUT%${NC}"
fi

if [ "$DRY_RUN" = true ]; then
    echo -e "${YELLOW}DRY RUN: No actual changes will be made${NC}"
fi

echo ""

# Check for Google Cloud SDK
if ! command -v gcloud >/dev/null 2>&1; then
    echo -e "${RED}Error: Google Cloud SDK (gcloud) is required but not installed.${NC}"
    echo "Please install from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check for authentication
if ! gcloud auth print-identity-token >/dev/null 2>&1; then
    echo -e "${RED}Error: Not authenticated with Google Cloud.${NC}"
    echo "Please run: gcloud auth login"
    exit 1
fi

# Check project access
if ! gcloud projects describe "$PROJECT_ID" >/dev/null 2>&1; then
    echo -e "${RED}Error: Cannot access project $PROJECT_ID.${NC}"
    echo "Please make sure you have the correct permissions."
    exit 1
fi

# Set default project
gcloud config set project "$PROJECT_ID"

# Run tests if not skipped
if [ "$SKIP_TESTS" = false ]; then
    echo -e "${BLUE}Running tests for $VERSION...${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        # Run integration tests
        if ! "$SCRIPT_DIR/test/integration/run_integration_tests.sh" --dry-run; then
            echo -e "${RED}Error: Tests failed. Deployment aborted.${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}(DRY RUN) Would run integration tests${NC}"
    fi
else
    echo -e "${YELLOW}Skipping tests as requested.${NC}"
fi

# Check if version exists in repository
if [ "$DRY_RUN" = false ]; then
    if ! git show-ref --tags | grep -q "refs/tags/$VERSION"; then
        echo -e "${RED}Error: Version $VERSION does not exist in the repository.${NC}"
        echo "Please create and push the tag first:"
        echo "git tag -a $VERSION -m \"Release $VERSION\""
        echo "git push origin $VERSION"
        exit 1
    fi
fi

# Find or build firmware
FIRMWARE_PATH="$SCRIPT_DIR/.pio/build/attiny85/firmware.hex"
if [ ! -f "$FIRMWARE_PATH" ]; then
    echo -e "${BLUE}Building firmware for $VERSION...${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        # Build the firmware
        if ! platformio run -e attiny85; then
            echo -e "${RED}Error: Failed to build firmware. Deployment aborted.${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}(DRY RUN) Would build firmware${NC}"
    fi
fi

# Confirm deployment for production
if [ "$ENVIRONMENT" = "production" ] && [ "$FORCE" = false ]; then
    echo -e "${YELLOW}WARNING: You are about to deploy to PRODUCTION!${NC}"
    echo "This will affect real devices in the field."
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        echo -e "${RED}Deployment aborted.${NC}"
        exit 1
    fi
    
    # Additional approval check for production
    echo -e "${BLUE}Checking deployment approvals...${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        # In a real implementation, this would check a database or API for approvals
        # For this example, we'll simulate by checking for a file
        APPROVAL_FILE="$SCRIPT_DIR/approvals/$VERSION.approved"
        
        if [ ! -f "$APPROVAL_FILE" ]; then
            echo -e "${RED}Error: $VERSION has not been approved for production deployment.${NC}"
            echo "Please obtain approvals through the release management system."
            exit 1
        fi
    else
        echo -e "${YELLOW}(DRY RUN) Would check for deployment approvals${NC}"
    fi
fi

# Deploy firmware to Cloud Storage
echo -e "${BLUE}Deploying firmware to $ENVIRONMENT...${NC}"

# Create firmware path in bucket
STORAGE_PATH="firmware/$ENVIRONMENT/$VERSION/attiny85.hex"

if [ "$DRY_RUN" = false ]; then
    # Upload firmware to Cloud Storage
    echo -e "${BLUE}Uploading firmware to gs://$BUCKET_NAME/$STORAGE_PATH...${NC}"
    
    # Create metadata
    METADATA="version=$VERSION,environment=$ENVIRONMENT,deploy-time=$(date +%s),deployed-by=$(whoami)"
    
    # Upload the firmware
    if ! gsutil -h "Content-Type:text/plain" -h "x-goog-meta-$METADATA" cp "$FIRMWARE_PATH" "gs://$BUCKET_NAME/$STORAGE_PATH"; then
        echo -e "${RED}Error: Failed to upload firmware to Cloud Storage.${NC}"
        exit 1
    fi
    
    # Update Firestore deployment record
    echo -e "${BLUE}Recording deployment in Firestore...${NC}"
    
    # Run Node.js script to record deployment
    DEPLOY_RECORD_SCRIPT="$SCRIPT_DIR/monitoring/tools/deployment-tracker.js"
    
    if [ -f "$DEPLOY_RECORD_SCRIPT" ]; then
        if ! node "$DEPLOY_RECORD_SCRIPT" \
            --version="$VERSION" \
            --environment="$ENVIRONMENT" \
            --status="deployed" \
            --storage-path="gs://$BUCKET_NAME/$STORAGE_PATH"; then
                
            echo -e "${YELLOW}Warning: Failed to record deployment in Firestore.${NC}"
            echo "Deployment will continue, but tracking information may be incomplete."
        fi
    else
        echo -e "${YELLOW}Warning: Deployment tracking script not found at $DEPLOY_RECORD_SCRIPT${NC}"
    fi
else
    echo -e "${YELLOW}(DRY RUN) Would upload firmware to gs://$BUCKET_NAME/$STORAGE_PATH${NC}"
    echo -e "${YELLOW}(DRY RUN) Would record deployment in Firestore${NC}"
fi

# Configure rollout settings
if [ "$ENVIRONMENT" != "development" ]; then
    echo -e "${BLUE}Configuring rollout settings...${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        # Update rollout configuration in Firestore
        # In a real implementation, this would update a Firestore document with rollout settings
        # For this example, we'll simulate by logging the action
        echo "Setting $ENVIRONMENT rollout: canary=$CANARY%, rollout=$ROLLOUT%"
        
        # Create rollout metadata file
        ROLLOUT_CONFIG="{
            \"version\": \"$VERSION\",
            \"environment\": \"$ENVIRONMENT\",
            \"canaryPercentage\": $CANARY,
            \"rolloutPercentage\": $ROLLOUT,
            \"deployTime\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",
            \"deployedBy\": \"$(whoami)\"
        }"
        
        # Save rollout configuration to Cloud Storage
        echo "$ROLLOUT_CONFIG" > /tmp/rollout-config.json
        
        if ! gsutil cp /tmp/rollout-config.json "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/$VERSION/rollout-config.json"; then
            echo -e "${YELLOW}Warning: Failed to upload rollout configuration.${NC}"
            echo "Deployment will continue, but rollout settings may not be applied."
        fi
        
        rm /tmp/rollout-config.json
    else
        echo -e "${YELLOW}(DRY RUN) Would configure rollout: canary=$CANARY%, rollout=$ROLLOUT%${NC}"
    fi
fi

# Mark version as current for the environment
echo -e "${BLUE}Setting $VERSION as current for $ENVIRONMENT...${NC}"

if [ "$DRY_RUN" = false ]; then
    # Create current version file
    echo "$VERSION" > /tmp/current-version
    
    if ! gsutil cp /tmp/current-version "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/current-version"; then
        echo -e "${YELLOW}Warning: Failed to update current version marker.${NC}"
        echo "Devices may not automatically pick up the new version."
    fi
    
    rm /tmp/current-version
else
    echo -e "${YELLOW}(DRY RUN) Would set $VERSION as current for $ENVIRONMENT${NC}"
fi

# Deployment complete
echo ""
echo -e "${GREEN}=======================================${NC}"
echo -e "${GREEN}  Deployment completed successfully!  ${NC}"
echo -e "${GREEN}=======================================${NC}"
echo ""
echo -e "Version ${GREEN}$VERSION${NC} has been deployed to ${GREEN}$ENVIRONMENT${NC}"
echo ""

if [ "$ENVIRONMENT" = "production" ]; then
    echo -e "${BLUE}Post-deployment steps:${NC}"
    echo "1. Monitor the deployment in the dashboard"
    echo "2. Verify devices are updating correctly"
    echo "3. Complete the deployment sign-off process"
    echo ""
fi

exit 0 