#!/bin/bash

# ATtiny85 Diagnostic Script
# This script helps diagnose common issues with ATtiny85 programming

echo "============================================="
echo "  ATtiny85 Programming Diagnostic Tool"
echo "============================================="
echo

# Check for PlatformIO
PLATFORMIO="$HOME/Library/Python/3.9/bin/platformio"
if [ ! -f "$PLATFORMIO" ]; then
    echo "❌ PlatformIO not found at $PLATFORMIO"
    echo "   This will prevent programming the ATtiny85"
else
    echo "✅ PlatformIO found at $PLATFORMIO"
fi

# Check for Arduino port
ARDUINO_PORT=$(ls /dev/tty.usb* 2>/dev/null)
if [ -z "$ARDUINO_PORT" ]; then
    echo "❌ No Arduino detected on any USB port"
    echo "   Check if Ard<PERSON>o is connected and powered"
else
    echo "✅ Detected Arduino on port(s):"
    for port in $ARDUINO_PORT; do
        echo "   - $port"
    done
fi

# Check platformio.ini settings
if [ -f "platformio.ini" ]; then
    echo "✅ platformio.ini file exists"
    
    # Check upload settings
    if grep -q "upload_protocol = arduino" platformio.ini; then
        echo "✅ Upload protocol is correctly set to 'arduino'"
    else
        echo "❌ Upload protocol may not be set correctly in platformio.ini"
        echo "   Should be: upload_protocol = arduino"
    fi
    
    # Check upload flags
    if grep -q "\-F" platformio.ini; then
        echo "✅ Force programming flag (-F) is set"
    else
        echo "❌ Force programming flag (-F) not found in platformio.ini"
        echo "   Consider adding it to upload_flags"
    fi
    
    # Check baud rate
    if grep -q "\-b9600" platformio.ini; then
        echo "✅ Baud rate is set to 9600 (slow and reliable)"
    elif grep -q "\-b19200" platformio.ini; then
        echo "⚠️ Baud rate is set to 19200. Consider lowering to 9600 for more reliability"
    else
        echo "⚠️ Baud rate setting not found or not standard"
    fi
    
    # Check port setting
    if grep -q "upload_port" platformio.ini; then
        echo "✅ Upload port is explicitly set in platformio.ini"
    else
        echo "⚠️ Upload port not explicitly set in platformio.ini"
        echo "   PlatformIO will try to auto-detect the port"
    fi
else
    echo "❌ platformio.ini file not found in current directory"
fi

echo
echo "============================================="
echo "  Connection Checklist"
echo "============================================="
echo "Please verify the following connections:"
echo "✓ Arduino D13 → ATtiny85 pin 7 (SCK)"
echo "✓ Arduino D12 → ATtiny85 pin 6 (MISO)"
echo "✓ Arduino D11 → ATtiny85 pin 5 (MOSI)"
echo "✓ Arduino D10 → ATtiny85 pin 1 (RESET)"
echo "✓ Arduino 5V  → ATtiny85 pin 8 (VCC)"
echo "✓ Arduino GND → ATtiny85 pin 4 (GND)"
echo
echo "Optional but recommended:"
echo "✓ 10μF capacitor between Arduino's RESET and GND pins (+ to RESET)"
echo

echo "============================================="
echo "  Recommendations"
echo "============================================="
echo "1. Upload the ArduinoISP sketch to your Arduino again"
echo "   Use the latest version from Arduino IDE → Examples → 11.ArduinoISP"
echo "   Make sure to change RESET to pin 10 in the sketch if needed"
echo
echo "2. Try pressing the reset button on Arduino just before uploading"
echo
echo "3. Try running this command for a verbose upload:"
echo "   $PLATFORMIO run -t upload -v"
echo
echo "4. If all else fails, consider using Arduino IDE directly:"
echo "   - Install ATtiny support in Arduino IDE"
echo "   - Set board to ATtiny85"
echo "   - Set programmer to 'Arduino as ISP'"
echo "   - Upload using 'Upload using Programmer' option"
echo
echo "=============================================" 