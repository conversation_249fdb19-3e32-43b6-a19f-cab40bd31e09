# ATtiny85 Connection Troubleshooting Guide

## Connection Issues

If you're seeing a "Device signature" error like this:
```
avrdude: Device signature = 0x0000ff
avrdude: Expected signature for ATtiny85 is 1E 93 0B
```

It usually indicates one of the following issues:

## Check Your Connections

1. **Double-check all wiring**:
   - Arduino D13 → ATtiny pin 7 (SCK)
   - Arduino D12 → ATtiny pin 6 (MISO)
   - Arduino D11 → ATtiny pin 5 (MOSI)
   - <PERSON>rd<PERSON>o D10 → ATtiny pin 1 (RESET)
   - Arduino 5V → ATtiny pin 8 (VCC)
   - Arduino GND → ATtiny pin 4 (GND)

2. **Check the ATtiny85 orientation**:
   - The notch or dot on the chip should be at the top
   - Pin 1 is to the left of the notch/dot
   - Pin 8 is to the right of the notch/dot on the opposite side

3. **Add a 10μF capacitor between RESET and GND on the Arduino**:
   - This prevents the Arduino from resetting during the upload process
   - Connect the negative lead to GND and the positive lead to RESET

4. **Ensure stable connections**:
   - Use a breadboard for more stable connections
   - Ensure all wires are firmly inserted
   - Try different wires if available

5. **Power supply issues**:
   - Make sure the ATtiny85 is getting proper 5V power
   - Try using an external power supply if available

6. **Clock speed**:
   - Try a slower baud rate by changing `-b19200` to `-b9600` in the platformio.ini file

## Other Possible Issues

1. **ArduinoISP sketch**:
   - Make sure the ArduinoISP sketch is properly loaded on the Arduino
   - Try re-uploading the ArduinoISP sketch

2. **ATtiny may be damaged or incompatible**:
   - Try with another ATtiny85 if available
   
3. **ATtiny may have incorrect or corrupted fuses**:
   - In this case, you might need to use a high-voltage programmer to reset the fuses

## After Fixing Connections

Once you've checked and fixed the connections, try uploading again:

```bash
./program_attiny.sh
```

If it still fails, try adding the `-v` flag to get more verbose output:

1. Edit platformio.ini:
   ```
   upload_flags = 
       -P$UPLOAD_PORT
       -b19200
       -F
       -v
   ```

2. Then run the script again. 