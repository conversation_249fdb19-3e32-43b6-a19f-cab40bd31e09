# Dockerfile.builder
FROM ubuntu:20.04

# Prevent interactive dialogs during apt-get install
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    gcc-avr \
    avr-libc \
    avrdude \
    python3 \
    python3-pip \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install PlatformIO
RUN pip3 install platformio

# Install Unity test framework
RUN git clone https://github.com/ThrowTheSwitch/Unity.git /opt/unity

# Set environment variables
ENV UNITY_PATH=/opt/unity

WORKDIR /workspace 