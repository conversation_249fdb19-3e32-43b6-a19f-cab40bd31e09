# PlatformIO
.pio
.pioenvs
.piolibdeps
.vscode
.vscode/.browse.c_cpp.db*
.vscode/c_cpp_properties.json
.vscode/launch.json
.vscode/settings.json
.vscode/ipch
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Build output
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Jupyter Notebook
.ipynb_checkpoints

# Credentials and sensitive information
agent/credentials/

# Simulation output
*.vcd

# Documentation generated files
doc/html/
doc/latex/

# Temporary files
*~
*.swp
*.swo
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Test reports
test-reports/
combined-report/
