#include <unity.h>
#include "modules/timer_manager.h"
#include <Arduino.h>

// Mock for air_turnOff_from_ISR function
extern "C" {
    void air_turnOff_from_ISR() {
        // Just a mock for testing
    }
}

// Add register bit definitions that were previously in Arduino.h
#define CS10 0
#define CS11 1
#define CS12 2
#define CS13 3
#define OCIE1A 6
#define PSR1 0

// Test helper class for TimerManager
class TestTimerManager {
public:
    static uint16_t getTestInterruptCount() {
        return TimerManager::getInterruptCount();
    }
    
    static void resetRegisters() {
        ArduinoMock::TCCR1_value = 0;
        ArduinoMock::OCR0A_value = 0;
        ArduinoMock::TIMSK_value = 0;
        ArduinoMock::GTCCR_value = 0;
        ArduinoMock::TCNT1_value = 0;
    }
};

// Define register access macros to match Arduino.cpp
#define TCCR1 ArduinoMock::TCCR1_value
#define OCR1A ArduinoMock::OCR0A_value
#define TIMSK ArduinoMock::TIMSK_value
#define GTCCR ArduinoMock::GTCCR_value
#define TCNT1 ArduinoMock::TCNT1_value

void setUp(void) {
    // Reset Arduino mock
    ArduinoMock::reset();
    
    // Reset state before each test
    TestTimerManager::resetRegisters();
    TimerManager::setInterruptCount(0);
}

void tearDown(void) {
    // Clean up after each test
}

void test_timer_init(void) {
    // Test timer initialization
    TimerManager::initTimer1();
    
    // Check that registers are set correctly
    TEST_ASSERT_EQUAL(244, OCR1A);
    TEST_ASSERT_BITS(0xFF, (1 << CS10) | (1 << CS11) | (1 << CS12) | (1 << CS13), TCCR1);
    TEST_ASSERT_BITS(0xFF, (1 << PSR1), GTCCR);
    TEST_ASSERT_BITS(0xFF, (1 << OCIE1A), TIMSK);
    TEST_ASSERT_EQUAL_UINT16(0, TimerManager::getInterruptCount());
}

void test_interrupt_count(void) {
    // Test get/set interrupt count
    TEST_ASSERT_EQUAL(0, TimerManager::getInterruptCount());
    
    TimerManager::setInterruptCount(100);
    TEST_ASSERT_EQUAL(100, TimerManager::getInterruptCount());
    
    TimerManager::setInterruptCount(0);
    TEST_ASSERT_EQUAL(0, TimerManager::getInterruptCount());
}

void test_reset_counter(void) {
    // Set up counter with non-zero values
    TCNT1 = 123;
    TimerManager::setInterruptCount(456);
    
    // Test reset
    TimerManager::resetCounter();
    
    // Check that counters are reset
    TEST_ASSERT_EQUAL(0, TCNT1);
    TEST_ASSERT_EQUAL(0, TimerManager::getInterruptCount());
}

void test_get_max_interrupt_count(void) {
    // Test that max count is as expected
    TEST_ASSERT_EQUAL(579, TimerManager::getMaxInterruptCount());
}

void test_isr(void) {
    // Test ISR behavior by calling it directly
    
    // First test with count less than max
    TimerManager::setInterruptCount(100);
    TIM1_COMPA_vect(); // Call ISR
    TEST_ASSERT_EQUAL(101, TimerManager::getInterruptCount());
    
    // Now test with count at max
    TimerManager::setInterruptCount(TimerManager::getMaxInterruptCount());
    TIM1_COMPA_vect(); // Call ISR
    
    // Verify that counter was incremented and air_turnOff_from_ISR was called
    TEST_ASSERT_EQUAL(TimerManager::getMaxInterruptCount() + 1, TimerManager::getInterruptCount());
}

int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_timer_init);
    RUN_TEST(test_interrupt_count);
    RUN_TEST(test_reset_counter);
    RUN_TEST(test_get_max_interrupt_count);
    RUN_TEST(test_isr);
    
    return UNITY_END();
} 