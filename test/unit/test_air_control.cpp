/**
 * @file test_air_control.cpp
 * @brief Enhanced unit tests for air control module
 */

#include <unity.h>
#include "modules/air_control.h"
#include "modules/pin_config.h"
#include "modules/timer_manager.h"
#include <EEPROM.h>

// Mock EEPROM data
static uint8_t mockEepromData[16] = {0};

// Function to reset test environment before each test
void setUp(void) {
    // Reset pins and states
    mockEepromData[0] = 1; // Default to off in EEPROM (inverted logic)
    EEPROM.begin(mockEepromData, sizeof(mockEepromData));
    
    // Reset pin configuration
    pinMode(PinConfig::AIR_OUT, OUTPUT);
    digitalWrite(PinConfig::AIR_OUT, HIGH); // Default to OFF
    
    // Reset timer
    TimerManager::init();
    TimerManager::resetCounter();
    
    // Reset Arduino mock
    ArduinoMock::reset();
}

void tearDown(void) {
    // Nothing to do for tear down
}

/**
 * @brief Test initialization from EEPROM
 */
void test_init_from_eeprom(void) {
    // Test init with air OFF in EEPROM
    mockEepromData[0] = 1; // 1 = OFF (inverted)
    AirControl::init();
    TEST_ASSERT_FALSE(AirControl::isOn());
    TEST_ASSERT_EQUAL(LOW, ArduinoMock::getDigitalPinState(PinConfig::AIR_OUT));
    
    // Test init with air ON in EEPROM
    mockEepromData[0] = 0; // 0 = ON (inverted)
    AirControl::init();
    TEST_ASSERT_TRUE(AirControl::isOn());
    TEST_ASSERT_EQUAL(HIGH, ArduinoMock::getDigitalPinState(PinConfig::AIR_OUT));
}

/**
 * @brief Test turning air on and off
 */
void test_on_off_functions(void) {
    // Turn air on
    AirControl::turnOn();
    TEST_ASSERT_TRUE(AirControl::isOn());
    TEST_ASSERT_EQUAL(HIGH, ArduinoMock::getDigitalPinState(PinConfig::AIR_OUT));
    TEST_ASSERT_EQUAL(0, mockEepromData[0]); // Verify EEPROM update (inverted)
    
    // Turn air off
    AirControl::turnOff();
    TEST_ASSERT_FALSE(AirControl::isOn());
    TEST_ASSERT_EQUAL(LOW, ArduinoMock::getDigitalPinState(PinConfig::AIR_OUT));
    TEST_ASSERT_EQUAL(1, mockEepromData[0]); // Verify EEPROM update (inverted)
}

/**
 * @brief Test processing logic based on sonicator input
 */
void test_process_function(void) {
    // Initialize air to off
    AirControl::turnOff();
    TEST_ASSERT_FALSE(AirControl::isOn());
    
    // Simulate sonicator on (LOW)
    digitalWrite(PinConfig::SONIC_IN, LOW);
    
    // Process the air control
    AirControl::process();
    
    // Verify air is turned on
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Verify timer was reset
    TEST_ASSERT_EQUAL(0, TimerManager::getCount());
    
    // Simulate sonicator off (HIGH)
    digitalWrite(PinConfig::SONIC_IN, HIGH);
    
    // Process again (should leave air on since the timer hasn't expired)
    AirControl::process();
    
    // Verify air is still on
    TEST_ASSERT_TRUE(AirControl::isOn());
}

/**
 * @brief Test setting air status with boolean parameter
 */
void test_set_status(void) {
    // Set to on
    AirControl::setStatus(true);
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Set to off
    AirControl::setStatus(false);
    TEST_ASSERT_FALSE(AirControl::isOn());
}

/**
 * @brief Test ISR function that turns off air pressure
 */
void test_interrupt_handler(void) {
    // Turn air on first
    AirControl::turnOn();
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Call the ISR handler function
    air_turnOff_from_ISR();
    
    // Verify air is now off
    TEST_ASSERT_FALSE(AirControl::isOn());
}

/**
 * @brief Main test function
 */
int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_init_from_eeprom);
    RUN_TEST(test_on_off_functions);
    RUN_TEST(test_process_function);
    RUN_TEST(test_set_status);
    RUN_TEST(test_interrupt_handler);
    
    return UNITY_END();
} 