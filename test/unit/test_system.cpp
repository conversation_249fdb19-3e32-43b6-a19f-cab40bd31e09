/**
 * @file test_system.cpp
 * @brief Unit tests for System module
 * 
 * Tests the system module which coordinates all other modules in the 
 * ATtiny85 Control System.
 */

#include <unity.h>
#include "mocks/Arduino.h"
#include "mocks/EEPROM.h"
#include "modules/system.h"
#include "modules/pin_config.h"
#include "modules/pwm_control.h"
#include "modules/air_control.h"
#include "modules/pump_control.h"
#include "modules/timer_manager.h"

// Mock function declarations - we'll use these to track initialization calls
extern bool pinConfigInitCalled;
extern bool pwmControlInitCalled;
extern bool timerManagerInitCalled;
extern bool airControlInitCalled;
extern bool pumpControlInitCalled;

// Reset mocked state before each test
void setUp(void) {
    ArduinoMock::reset();
    EEPROMMock::reset();
    
    // Reset module initialization flags
    pinConfigInitCalled = false;
    pwmControlInitCalled = false;
    timerManagerInitCalled = false;
    airControlInitCalled = false;
    pumpControlInitCalled = false;
}

void tearDown(void) {
    // Clean up after each test
}

/**
 * Test that System::init() initializes all required modules
 */
void test_system_init_calls_all_module_inits(void) {
    // Call the system initialization
    System::init();
    
    // Verify that all module init functions were called
    TEST_ASSERT_TRUE(pinConfigInitCalled);
    TEST_ASSERT_TRUE(pwmControlInitCalled);
    TEST_ASSERT_TRUE(timerManagerInitCalled);
    TEST_ASSERT_TRUE(airControlInitCalled);
    TEST_ASSERT_TRUE(pumpControlInitCalled);
}

/**
 * Test that System::process() calls all required module process functions
 */
void test_system_process_calls_required_modules(void) {
    // Reset Arduino mock to clear call history
    ArduinoMock::reset();
    
    // Set up test conditions for PWM input
    ArduinoMock::setDigitalPinValue(PinConfig::PWM_IN, HIGH);
    ArduinoMock::setPulseInValues(PinConfig::PWM_IN, 1500, 1500); // 50% duty cycle
    
    // Call the system process function
    System::process();
    
    // Verify PWM was processed by checking OCR0B was set
    // We can't directly verify function calls, but we can check side effects
    TEST_ASSERT_TRUE(ArduinoMock::digitalWriteCalled);
    
    // For more precise testing, we'd need to add more mock verification points
    // This is a basic verification that the process function is executing
}

/**
 * Test system behavior with different PWM inputs
 */
void test_system_pwm_input_processing(void) {
    // First test with low duty cycle (10%)
    ArduinoMock::reset();
    ArduinoMock::setPulseInValues(PinConfig::PWM_IN, 100, 900); // 10% duty cycle
    System::process();
    
    // Expect pump to be off with low duty cycle
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Now test with high duty cycle (80%)
    ArduinoMock::reset();
    ArduinoMock::setPulseInValues(PinConfig::PWM_IN, 800, 200); // 80% duty cycle
    System::process();
    
    // Expect pump to be on with high duty cycle
    TEST_ASSERT_TRUE(PumpControl::isOn());
}

/**
 * Test system state with sonicator input variations
 */
void test_system_sonicator_input_processing(void) {
    // Test with sonicator off
    ArduinoMock::reset();
    ArduinoMock::setDigitalPinValue(PinConfig::SONIC_IN, HIGH); // Inactive
    System::process();
    
    // Verify air control is not enabled
    TEST_ASSERT_FALSE(AirControl::isOn());
    
    // Test with sonicator on
    ArduinoMock::reset();
    ArduinoMock::setDigitalPinValue(PinConfig::SONIC_IN, LOW); // Active (LOW)
    System::process();
    
    // Verify air control is enabled
    TEST_ASSERT_TRUE(AirControl::isOn());
}

// Main test program
int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_system_init_calls_all_module_inits);
    RUN_TEST(test_system_process_calls_required_modules);
    RUN_TEST(test_system_pwm_input_processing);
    RUN_TEST(test_system_sonicator_input_processing);
    
    return UNITY_END();
} 