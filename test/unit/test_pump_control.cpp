/**
 * @file test_pump_control.cpp
 * @brief Enhanced unit tests for pump control module
 */

#include <unity.h>
#include "modules/pump_control.h"
#include "modules/pin_config.h"
#include <Arduino.h>

// Test friend class to access private members
class TestPumpControl {
public:
    static bool& getPumpStatus() {
        static bool status = false;
        return status;
    }
};

// Mock functions for Arduino
void digitalWrite(uint8_t pin, uint8_t val) {
    // Store the pin state for verification
    if (pin == PinConfig::PUMP_OUT) {
        TestPumpControl::getPumpStatus() = val == HIGH;
    }
}

void setUp(void) {
    // Reset Arduino mock state
    ArduinoMock::reset();
    
    // Set up the pump pin as output
    pinMode(PinConfig::PUMP_OUT, OUTPUT);
    
    // Initialize pump to known state
    PumpControl::init();
    PumpControl::turnOff();
}

void tearDown(void) {
    // Nothing to do for tear down
}

/**
 * @brief Test pump initialization
 */
void test_pump_init(void) {
    // Reset pin state
    ArduinoMock::reset();
    
    // Run initialization
    PumpControl::init();
    
    // Verify pump pin is configured as output
    TEST_ASSERT_EQUAL(OUTPUT, ArduinoMock::getPinMode(PinConfig::PUMP_OUT));
    
    // Verify pump starts in OFF state
    TEST_ASSERT_FALSE(PumpControl::isOn());
}

/**
 * @brief Test turning pump on
 */
void test_pump_turn_on(void) {
    // Ensure pump is off to start
    PumpControl::turnOff();
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Turn on pump
    PumpControl::turnOn();
    
    // Verify pump is on
    TEST_ASSERT_TRUE(PumpControl::isOn());
    
    // Verify pump output pin is set correctly (HIGH for active)
    TEST_ASSERT_EQUAL(HIGH, ArduinoMock::getDigitalPinState(PinConfig::PUMP_OUT));
}

/**
 * @brief Test turning pump off
 */
void test_pump_turn_off(void) {
    // Start with pump on
    PumpControl::turnOn();
    TEST_ASSERT_TRUE(PumpControl::isOn());
    
    // Turn off pump
    PumpControl::turnOff();
    
    // Verify pump is off
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Verify pump output pin is set correctly (LOW for inactive)
    TEST_ASSERT_EQUAL(LOW, ArduinoMock::getDigitalPinState(PinConfig::PUMP_OUT));
}

/**
 * @brief Test pump process with high duty cycle (above threshold)
 */
void test_pump_process_high_duty(void) {
    // Start with pump off
    PumpControl::turnOff();
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Process with duty cycle above threshold (testing transition)
    PumpControl::process(60); // Threshold is typically 50
    
    // Verify pump is ON when duty cycle is above threshold
    TEST_ASSERT_TRUE(PumpControl::isOn());
    TEST_ASSERT_EQUAL(HIGH, ArduinoMock::getDigitalPinState(PinConfig::PUMP_OUT));
}

/**
 * @brief Test pump process with low duty cycle (below threshold)
 */
void test_pump_process_low_duty(void) {
    // Start with pump on
    PumpControl::turnOn();
    TEST_ASSERT_TRUE(PumpControl::isOn());
    
    // Process with duty cycle below threshold (testing transition)
    PumpControl::process(40); // Threshold is typically 50
    
    // Verify pump is OFF when duty cycle is below threshold
    TEST_ASSERT_FALSE(PumpControl::isOn());
    TEST_ASSERT_EQUAL(LOW, ArduinoMock::getDigitalPinState(PinConfig::PUMP_OUT));
}

/**
 * @brief Test threshold boundary conditions
 */
void test_threshold_boundaries(void) {
    // Test at exactly the threshold (50)
    PumpControl::turnOff();
    PumpControl::process(50);
    TEST_ASSERT_TRUE(PumpControl::isOn()); // At threshold should turn on
    
    // Test just below threshold (49)
    PumpControl::turnOn();
    PumpControl::process(49);
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Test just above threshold (51)
    PumpControl::turnOff();
    PumpControl::process(51);
    TEST_ASSERT_TRUE(PumpControl::isOn());
}

/**
 * @brief Test extreme duty cycle values
 */
void test_extreme_values(void) {
    // Test with zero duty cycle
    PumpControl::turnOn();
    PumpControl::process(0);
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Test with maximum duty cycle
    PumpControl::turnOff();
    PumpControl::process(100);
    TEST_ASSERT_TRUE(PumpControl::isOn());
}

/**
 * @brief Main test function
 */
int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_pump_init);
    RUN_TEST(test_pump_turn_on);
    RUN_TEST(test_pump_turn_off);
    RUN_TEST(test_pump_process_high_duty);
    RUN_TEST(test_pump_process_low_duty);
    RUN_TEST(test_threshold_boundaries);
    RUN_TEST(test_extreme_values);
    
    return UNITY_END();
} 