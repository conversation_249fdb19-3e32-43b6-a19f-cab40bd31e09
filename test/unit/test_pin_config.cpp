/**
 * @file test_pin_config.cpp
 * @brief Unit tests for pin configuration module
 */

#include "unity.h"
#include "modules/pin_config.h"

void setUp(void) {
    // Set up testing environment
}

void tearDown(void) {
    // Clean up after tests
}

/**
 * Test pin constants have correct values
 */
void test_pin_constants(void) {
    // Test that pin values are as expected
    TEST_ASSERT_EQUAL(0, PinConfig::PWM_OUT);
    TEST_ASSERT_EQUAL(1, PinConfig::AIR_OUT);
    TEST_ASSERT_EQUAL(2, PinConfig::PUMP_OUT);
    TEST_ASSERT_EQUAL(3, PinConfig::SONIC_IN);
    TEST_ASSERT_EQUAL(4, PinConfig::BUTTON_IN);
}

/**
 * Test pins are unique
 */
void test_pins_are_unique(void) {
    // Test that pins don't overlap
    TEST_ASSERT_NOT_EQUAL(PinConfig::PWM_OUT, PinConfig::AIR_OUT);
    TEST_ASSERT_NOT_EQUAL(PinConfig::PWM_OUT, PinConfig::PUMP_OUT);
    TEST_ASSERT_NOT_EQUAL(PinConfig::PWM_OUT, PinConfig::SONIC_IN);
    TEST_ASSERT_NOT_EQUAL(PinConfig::PWM_OUT, PinConfig::BUTTON_IN);
    
    TEST_ASSERT_NOT_EQUAL(PinConfig::AIR_OUT, PinConfig::PUMP_OUT);
    TEST_ASSERT_NOT_EQUAL(PinConfig::AIR_OUT, PinConfig::SONIC_IN);
    TEST_ASSERT_NOT_EQUAL(PinConfig::AIR_OUT, PinConfig::BUTTON_IN);
    
    TEST_ASSERT_NOT_EQUAL(PinConfig::PUMP_OUT, PinConfig::SONIC_IN);
    TEST_ASSERT_NOT_EQUAL(PinConfig::PUMP_OUT, PinConfig::BUTTON_IN);
    
    TEST_ASSERT_NOT_EQUAL(PinConfig::SONIC_IN, PinConfig::BUTTON_IN);
}

int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_pin_constants);
    RUN_TEST(test_pins_are_unique);
    
    return UNITY_END();
} 