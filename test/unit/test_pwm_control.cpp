/**
 * @file test_pwm_control.cpp
 * @brief Enhanced unit tests for PWM control module
 */

#include <unity.h>
#include "modules/pwm_control.h"
#include "modules/pin_config.h"
#include <Arduino.h>

// Mock values for pulseIn function
static unsigned long mockPulseHighValue = 1500; // Default 1.5ms high time
static unsigned long mockPulseLowValue = 500;   // Default 0.5ms low time
static unsigned long mockPulseTotal = 2000;     // Total period (high + low)

void setUp(void) {
    // Reset Arduino mock state
    ArduinoMock::reset();
    
    // Reset AVR registers
    TCCR0A = 0;
    TCCR0B = 0;
    OCR0B = 0;
    DDRB = 0;
    
    // Set default pulse values (75% duty cycle)
    mockPulseHighValue = 1500;
    mockPulseLowValue = 500;
    mockPulseTotal = mockPulseHighValue + mockPulseLowValue;
}

void tearDown(void) {
    // Nothing to do for teardown
}

/**
 * @brief Override of pulseIn function for testing
 */
unsigned long pulseIn(uint8_t pin, uint8_t state, unsigned long timeout) {
    // Return appropriate mock value based on state
    return (state == HIGH) ? mockPulseHighValue : mockPulseLowValue;
}

/**
 * @brief Set mock PWM duty cycle for testing
 * 
 * @param dutyCycle Duty cycle percentage (0-100)
 */
void setMockPwmDutyCycle(byte dutyCycle) {
    if (dutyCycle > 100) dutyCycle = 100;
    
    // Calculate high and low times based on duty cycle
    mockPulseHighValue = (dutyCycle * 20); // 2000 * (duty/100)
    mockPulseLowValue = 2000 - mockPulseHighValue;
    mockPulseTotal = mockPulseHighValue + mockPulseLowValue;
}

/**
 * @brief Test PWM initialization
 */
void test_init(void) {
    // Test initialization function
    PwmControl::init();
    
    // Check that registers are set correctly for PWM operation
    TEST_ASSERT_BITS(0xFF, (1 << WGM01) | (1 << WGM00) | (1 << COM0B1), TCCR0A);
    TEST_ASSERT_BITS(0xFF, (1 << CS02), TCCR0B);
    
    // Verify pin directions
    TEST_ASSERT_BITS(0xFF, (1 << DDB1), DDRB); // PWM output pin
}

/**
 * @brief Test duty cycle reading
 */
void test_getDutyCycle(void) {
    // Test multiple duty cycle values
    
    // Test 0% duty cycle
    setMockPwmDutyCycle(0);
    TEST_ASSERT_EQUAL(0, PwmControl::getDutyCycle(PinConfig::PWM_IN));
    
    // Test 25% duty cycle
    setMockPwmDutyCycle(25);
    TEST_ASSERT_EQUAL(25, PwmControl::getDutyCycle(PinConfig::PWM_IN));
    
    // Test 50% duty cycle
    setMockPwmDutyCycle(50);
    TEST_ASSERT_EQUAL(50, PwmControl::getDutyCycle(PinConfig::PWM_IN));
    
    // Test 75% duty cycle
    setMockPwmDutyCycle(75);
    TEST_ASSERT_EQUAL(75, PwmControl::getDutyCycle(PinConfig::PWM_IN));
    
    // Test 100% duty cycle
    setMockPwmDutyCycle(100);
    TEST_ASSERT_EQUAL(100, PwmControl::getDutyCycle(PinConfig::PWM_IN));
}

/**
 * @brief Test setting output duty cycle
 */
void test_setOutput(void) {
    // Initialize PWM
    PwmControl::init();
    
    // Test with various duty cycle values
    
    // Test 0% duty cycle
    PwmControl::setOutput(0);
    TEST_ASSERT_EQUAL(0, OCR0B);
    
    // Test 25% duty cycle
    PwmControl::setOutput(25);
    TEST_ASSERT_EQUAL(64, OCR0B); // 25% of 255 ≈ 64
    
    // Test 50% duty cycle
    PwmControl::setOutput(50);
    TEST_ASSERT_EQUAL(128, OCR0B); // 50% of 255 ≈ 128
    
    // Test 75% duty cycle
    PwmControl::setOutput(75);
    TEST_ASSERT_EQUAL(191, OCR0B); // 75% of 255 ≈ 191
    
    // Test 100% duty cycle
    PwmControl::setOutput(100);
    TEST_ASSERT_EQUAL(255, OCR0B); // 100% of 255 = 255
}

/**
 * @brief Test setting output with capping
 */
void test_setOutput_with_capping(void) {
    // Initialize PWM
    PwmControl::init();
    
    // Test with various duty cycles and caps
    
    // Test duty cycle below cap
    PwmControl::setOutput(30, 50);
    TEST_ASSERT_EQUAL(77, OCR0B); // 30% of 255 ≈ 77 (not capped)
    
    // Test duty cycle at cap
    PwmControl::setOutput(50, 50);
    TEST_ASSERT_EQUAL(128, OCR0B); // 50% of 255 ≈ 128 (at cap)
    
    // Test duty cycle above cap
    PwmControl::setOutput(70, 50);
    TEST_ASSERT_EQUAL(128, OCR0B); // Capped at 50% of 255 ≈ 128
    
    // Test with different cap
    PwmControl::setOutput(90, 80);
    TEST_ASSERT_EQUAL(204, OCR0B); // Capped at 80% of 255 ≈ 204
    
    // Test with 100% cap (no capping)
    PwmControl::setOutput(90, 100);
    TEST_ASSERT_EQUAL(230, OCR0B); // 90% of 255 ≈ 230 (not capped)
}

/**
 * @brief Test processing input to output
 */
void test_processInputOutput(void) {
    // Initialize PWM
    PwmControl::init();
    
    // Test with various input duty cycles
    
    // Test with very low duty cycle
    setMockPwmDutyCycle(5);
    byte result = PwmControl::processInputOutput();
    TEST_ASSERT_EQUAL(5, result);  // Should return the input duty cycle
    TEST_ASSERT_EQUAL(13, OCR0B);  // 5% of 255 ≈ 13
    
    // Test with medium duty cycle
    setMockPwmDutyCycle(50);
    result = PwmControl::processInputOutput();
    TEST_ASSERT_EQUAL(50, result);
    TEST_ASSERT_EQUAL(128, OCR0B); // 50% of 255 ≈ 128
    
    // Test with high duty cycle (below default 77% cap)
    setMockPwmDutyCycle(75);
    result = PwmControl::processInputOutput();
    TEST_ASSERT_EQUAL(75, result);
    TEST_ASSERT_EQUAL(191, OCR0B); // 75% of 255 ≈ 191
    
    // Test with very high duty cycle (above default 77% cap)
    setMockPwmDutyCycle(90);
    result = PwmControl::processInputOutput();
    TEST_ASSERT_EQUAL(90, result);
    TEST_ASSERT_EQUAL(196, OCR0B); // Capped at 77% of 255 ≈ 196
}

/**
 * @brief Test processing with custom maximum duty cycle
 */
void test_processInputOutput_customMax(void) {
    // Initialize PWM
    PwmControl::init();
    
    // Test with a custom maximum duty cycle
    
    // Set a lower maximum cap (50%)
    setMockPwmDutyCycle(75);
    byte result = PwmControl::processInputOutput(50);
    TEST_ASSERT_EQUAL(75, result);  // Should return the input duty cycle
    TEST_ASSERT_EQUAL(128, OCR0B);  // Capped at 50% of 255 ≈ 128
    
    // Set a higher maximum cap (90%)
    setMockPwmDutyCycle(85);
    result = PwmControl::processInputOutput(90);
    TEST_ASSERT_EQUAL(85, result);
    TEST_ASSERT_EQUAL(217, OCR0B); // 85% of 255 ≈ 217 (below 90% cap)
}

int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_init);
    RUN_TEST(test_getDutyCycle);
    RUN_TEST(test_setOutput);
    RUN_TEST(test_setOutput_with_capping);
    RUN_TEST(test_processInputOutput);
    RUN_TEST(test_processInputOutput_customMax);
    
    return UNITY_END();
} 