/**
 * @file test_timer_manager_enhanced.cpp
 * @brief Enhanced unit tests for the timer manager module
 */

#include <unity.h>
#include "Arduino.h"
#include "modules/timer_manager.h"
#include "modules/air_control.h"

// Add register bit definitions that were previously in Arduino.h
#define CS10 0
#define CS11 1
#define CS12 2
#define CS13 3
#define OCIE1A 6
#define PSR1 0

// Define register access macros to match Arduino.cpp
#define TCCR1 ArduinoMock::TCCR1_value
#define OCR1A ArduinoMock::OCR0A_value
#define TIMSK ArduinoMock::TIMSK_value
#define GTCCR ArduinoMock::GTCCR_value
#define TCNT1 ArduinoMock::TCNT1_value

// Track if air_turnOff_from_ISR was called
bool air_off_from_isr_called = false;

// Override the air_turnOff_from_ISR to track calls
void air_turnOff_from_ISR() {
    air_off_from_isr_called = true;
    AirControl::turnOff();
}

void setUp(void) {
    // Reset the Arduino mock state
    ArduinoMock::reset();
    
    // Reset the timer manager state
    TimerManager::setInterruptCount(0);
    
    // Reset the air control state
    AirControl::init();
    
    // Reset our tracking
    air_off_from_isr_called = false;
}

void tearDown(void) {
    // Nothing to clean up
}

/**
 * Test that the timer initialization sets up the timer registers correctly
 */
void test_timer_initialization(void) {
    // Start with zeroed registers
    TCCR1 = 0;
    TIMSK = 0;
    GTCCR = 0;
    OCR1A = 0;
    
    // Call the initialization function
    TimerManager::initTimer1();
    
    // Check that the registers were set up correctly
    TEST_ASSERT_EQUAL(244, OCR1A);
    TEST_ASSERT_EQUAL(0x0F, TCCR1 & 0x0F);  // CS10, CS11, CS12, CS13 bits should be set
    TEST_ASSERT_EQUAL(0x40, TIMSK & 0x40);  // OCIE1A bit should be set
    TEST_ASSERT_EQUAL(0x01, GTCCR & 0x01);  // PSR1 bit should be set
}

/**
 * Test that the interrupt count is properly managed
 */
void test_interrupt_count_management(void) {
    // Set a random value
    TimerManager::setInterruptCount(42);
    
    // Verify the value was set
    TEST_ASSERT_EQUAL(42, TimerManager::getInterruptCount());
    
    // Reset the counter
    TimerManager::resetCounter();
    
    // Verify the counter was reset
    TEST_ASSERT_EQUAL(0, TimerManager::getInterruptCount());
    TEST_ASSERT_EQUAL(0, TCNT1);
}

/**
 * Test the maximum interrupt count constant
 */
void test_max_interrupt_count(void) {
    // The value should be as defined in the header
    TEST_ASSERT_EQUAL(579, TimerManager::getMaxInterruptCount());
}

/**
 * Test the interrupt service routine behavior
 */
void test_isr_behavior(void) {
    // Set up initial count
    TimerManager::setInterruptCount(0);
    
    // Call ISR once
    TIM1_COMPA_vect();
    
    // Count should be incremented
    TEST_ASSERT_EQUAL(1, TimerManager::getInterruptCount());
    
    // Air control should not be turned off yet
    TEST_ASSERT_FALSE(AirControl::isRunning());
    
    // Turn air on
    AirControl::turnOn();
    TEST_ASSERT_TRUE(AirControl::isRunning());
    
    // Set count to just below max
    TimerManager::setInterruptCount(TimerManager::getMaxInterruptCount() - 1);
    
    // Call ISR again
    TIM1_COMPA_vect();
    
    // Count should be at the max
    TEST_ASSERT_EQUAL(TimerManager::getMaxInterruptCount(), TimerManager::getInterruptCount());
    
    // Air control should still be on
    TEST_ASSERT_TRUE(AirControl::isRunning());
    
    // Call ISR one more time
    TIM1_COMPA_vect();
    
    // Count should be at max + 1
    TEST_ASSERT_EQUAL(TimerManager::getMaxInterruptCount() + 1, TimerManager::getInterruptCount());
    
    // Air control should now be turned off
    TEST_ASSERT_FALSE(AirControl::isRunning());
}

/**
 * Test timeout behavior with rapid ISR calls
 */
void test_timeout_behavior(void) {
    // Set up initial count
    TimerManager::setInterruptCount(0);
    
    // Turn air on
    AirControl::turnOn();
    TEST_ASSERT_TRUE(AirControl::isRunning());
    
    // Call ISR multiple times, just short of the max
    for (uint16_t i = 0; i < TimerManager::getMaxInterruptCount(); i++) {
        TIM1_COMPA_vect();
    }
    
    // Air should still be on
    TEST_ASSERT_TRUE(AirControl::isRunning());
    
    // Call ISR one more time
    TIM1_COMPA_vect();
    
    // Air should now be off
    TEST_ASSERT_FALSE(AirControl::isRunning());
}

int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_timer_initialization);
    RUN_TEST(test_interrupt_count_management);
    RUN_TEST(test_max_interrupt_count);
    RUN_TEST(test_isr_behavior);
    RUN_TEST(test_timeout_behavior);
    
    return UNITY_END();
} 