#!/usr/bin/env python3
"""
Test Helper Utilities

This module provides common helper functions for integration tests.
"""

import os
import sys
import logging
import datetime
import json
import xml.etree.ElementTree as ET
from pathlib import Path


def setup_logger(log_level=logging.INFO, log_file=None):
    """
    Set up and configure the logger.
    
    Args:
        log_level: The logging level
        log_file: Optional file path for logging output
        
    Returns:
        Logger instance
    """
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if log file specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def generate_html_report(test_name, test_result, output_file):
    """
    Generate an HTML report from test results.
    
    Args:
        test_name: Name of the test suite
        test_result: unittest.TestResult object
        output_file: Output file path for the HTML report
    """
    # Create report directory if it doesn't exist
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Generate HTML report
    html = []
    html.append('<!DOCTYPE html>')
    html.append('<html lang="en">')
    html.append('<head>')
    html.append('  <meta charset="UTF-8">')
    html.append('  <meta name="viewport" content="width=device-width, initial-scale=1.0">')
    html.append(f'  <title>{test_name} - Test Report</title>')
    html.append('  <style>')
    html.append('    body { font-family: Arial, sans-serif; margin: 20px; }')
    html.append('    h1 { color: #333; }')
    html.append('    .summary { background-color: #f8f8f8; padding: 10px; border-radius: 5px; }')
    html.append('    .test-case { margin: 10px 0; padding: 10px; border-radius: 5px; }')
    html.append('    .success { background-color: #dff0d8; border: 1px solid #d6e9c6; }')
    html.append('    .failure { background-color: #f2dede; border: 1px solid #ebccd1; }')
    html.append('    .error { background-color: #fcf8e3; border: 1px solid #faebcc; }')
    html.append('    .details { margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 3px; }')
    html.append('    .timestamp { color: #666; font-size: 0.9em; }')
    html.append('  </style>')
    html.append('</head>')
    html.append('<body>')
    html.append(f'  <h1>{test_name} - Test Report</h1>')
    html.append('  <div class="timestamp">')
    html.append(f'    Generated on {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    html.append('  </div>')
    
    # Test Summary
    html.append('  <div class="summary">')
    html.append('    <h2>Summary</h2>')
    html.append(f'    <p>Total Tests: {test_result.testsRun}</p>')
    html.append(f'    <p>Passed: {test_result.testsRun - len(test_result.failures) - len(test_result.errors)}</p>')
    html.append(f'    <p>Failed: {len(test_result.failures)}</p>')
    html.append(f'    <p>Errors: {len(test_result.errors)}</p>')
    html.append(f'    <p>Success Rate: {(test_result.testsRun - len(test_result.failures) - len(test_result.errors)) / test_result.testsRun * 100:.2f}%</p>')
    html.append('  </div>')
    
    # Test Details
    html.append('  <h2>Test Details</h2>')
    
    # List of all test cases
    test_cases = []
    for test_case, error in test_result.failures:
        test_cases.append((test_case, 'failure', error))
    
    for test_case, error in test_result.errors:
        test_cases.append((test_case, 'error', error))
    
    # Add successful tests
    successful_tests = [test for test in test_result.cases] if hasattr(test_result, 'cases') else []
    for test_case in successful_tests:
        if test_case not in [tc for tc, _, _ in test_cases]:
            test_cases.append((test_case, 'success', None))
    
    # Sort test cases by name
    test_cases.sort(key=lambda x: str(x[0]))
    
    # Generate HTML for each test case
    for test_case, status, error in test_cases:
        test_name = test_case.id().split('.')[-1]
        html.append(f'  <div class="test-case {status}">')
        html.append(f'    <h3>{test_name}</h3>')
        html.append(f'    <p>Status: {status.upper()}</p>')
        
        if error:
            html.append('    <div class="details">')
            html.append('      <pre>')
            html.append(error)
            html.append('      </pre>')
            html.append('    </div>')
        
        html.append('  </div>')
    
    html.append('</body>')
    html.append('</html>')
    
    # Write HTML report to file
    with open(output_file, 'w') as f:
        f.write('\n'.join(html))


def get_gcp_credentials():
    """
    Get GCP credentials from environment variable or default location.
    
    Returns:
        Path to credentials file or None if not found
    """
    # Check environment variable
    creds_path = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
    if creds_path and os.path.exists(creds_path):
        return creds_path
    
    # Check default locations
    home = os.path.expanduser("~")
    default_paths = [
        os.path.join(home, '.config', 'gcloud', 'application_default_credentials.json'),
        os.path.join(home, '.gcloud', 'application_default_credentials.json')
    ]
    
    for path in default_paths:
        if os.path.exists(path):
            return path
    
    return None


def create_test_device(device_id, firmware_version, region="us-central1"):
    """
    Create a test device configuration.
    
    Args:
        device_id: Unique identifier for the device
        firmware_version: Firmware version
        region: GCP region
        
    Returns:
        Dict containing device configuration
    """
    return {
        "deviceId": device_id,
        "firmwareVersion": firmware_version,
        "status": "online",
        "region": region,
        "metrics": {
            "temperature": 25.0,
            "voltage": 3.3,
            "uptime": 3600
        },
        "lastSeen": datetime.datetime.utcnow().isoformat() + "Z"
    }


def read_junit_report(junit_path):
    """
    Parse a JUnit XML report.
    
    Args:
        junit_path: Path to JUnit XML file
        
    Returns:
        Dict with test results summary
    """
    tree = ET.parse(junit_path)
    root = tree.getroot()
    
    # Extract testsuite attributes
    testsuite = root
    if root.tag != 'testsuite':
        testsuite = root.find('.//testsuite')
    
    if testsuite is None:
        return {
            "name": "Unknown",
            "tests": 0,
            "failures": 0,
            "errors": 0,
            "skipped": 0,
            "success_rate": 0.0
        }
    
    # Get test counts
    tests = int(testsuite.get('tests', 0))
    failures = int(testsuite.get('failures', 0))
    errors = int(testsuite.get('errors', 0))
    skipped = len(testsuite.findall('.//skipped'))
    
    # Calculate success rate
    success_count = tests - failures - errors - skipped
    success_rate = (success_count / tests * 100) if tests > 0 else 0
    
    return {
        "name": testsuite.get('name', 'Unknown'),
        "tests": tests,
        "failures": failures,
        "errors": errors,
        "skipped": skipped,
        "success_rate": success_rate
    }


def simulate_device_heartbeat(device_id, firmware_version, status="online"):
    """
    Simulate a device heartbeat by sending HTTP request to device monitor function.
    
    Args:
        device_id: Unique identifier for the device
        firmware_version: Firmware version
        status: Device status (online, degraded, offline)
        
    Returns:
        Response from the device monitor function
    """
    import requests
    
    # Get project ID and region from environment
    project_id = os.environ.get('GCP_PROJECT_ID', 'cannasol-automation-suite')
    region = os.environ.get('GCP_REGION', 'us-central1')
    
    # Create heartbeat data
    heartbeat_data = {
        "deviceId": device_id,
        "firmwareVersion": firmware_version,
        "status": status,
        "metrics": {
            "temperature": 25.0,
            "voltage": 3.3,
            "uptime": 3600
        }
    }
    
    # Send heartbeat to device monitor function
    url = f"https://{region}-{project_id}.cloudfunctions.net/device-monitor"
    
    try:
        response = requests.post(url, json=heartbeat_data, timeout=10)
        return response
    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to simulate device heartbeat: {e}")
        return None 