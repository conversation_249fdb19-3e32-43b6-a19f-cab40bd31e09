/**
 * @file test_system_integration.cpp
 * @brief Comprehensive integration tests for the ATTINY85 Control System
 */

#include <unity.h>
#include <Arduino.h>
#include "modules/system.h"
#include "modules/air_control.h"
#include "modules/pump_control.h"
#include "modules/pwm_control.h"
#include "modules/timer_manager.h"
#include "modules/pin_config.h"
#include "../mocks/ATTINY85_emulator.h"

// Define test scenarios
#define SCENARIO_NORMAL_OPERATION 1
#define SCENARIO_HIGH_DUTY_CYCLE 2
#define SCENARIO_LOW_DUTY_CYCLE 3
#define SCENARIO_AIR_TIMEOUT 4

// Current test scenario
static int currentScenario = SCENARIO_NORMAL_OPERATION;

// Setup for each test
void setUp(void) {
    // Initialize the emulator
    EmulatorUtils::initEmulator();
    
    // Initialize system components
    System::init();
    
    // Wait for initialization to complete
    EmulatorUtils::advanceTime(100);
}

// Teardown after each test
void tearDown(void) {
    // Ensure system is in a safe state
    AirControl::turnOff();
    PumpControl::turnOff();
}

/**
 * @brief Test normal system operation with moderate PWM input
 * 
 * This test verifies that the system behaves correctly under normal operation
 * with a moderate PWM input signal.
 */
void test_normal_operation(void) {
    // Set current scenario
    currentScenario = SCENARIO_NORMAL_OPERATION;
    
    // Generate a 50% duty cycle PWM signal on the sonicator input pin
    EmulatorUtils::generatePwmSignal(PinConfig::SONIC_IN, 50);
    
    // Process the system for several cycles
    for (int i = 0; i < 5; i++) {
        System::process();
        EmulatorUtils::advanceTime(100);
    }
    
    // Verify expected system state
    TEST_ASSERT_TRUE(AirControl::isOn());
    TEST_ASSERT_FALSE(PumpControl::isOn()); // Pump should be off at medium duty cycle
    
    // Verify PWM output is correctly processed
    byte outputDuty = PwmControl::getDutyCycle(PinConfig::PWM_OUT);
    TEST_ASSERT_GREATER_THAN(0, outputDuty);
    TEST_ASSERT_LESS_THAN(100, outputDuty);
    
    // Verify pin states
    TEST_ASSERT_EQUAL(LOW, EmulatorUtils::readPinValue(PinConfig::AIR_OUT)); // LOW = ON for air
    TEST_ASSERT_EQUAL(HIGH, EmulatorUtils::readPinValue(PinConfig::PUMP_OUT)); // HIGH = OFF for pump
}

/**
 * @brief Test system behavior with high duty cycle input
 * 
 * This test verifies that the system responds correctly to high duty cycle input,
 * which should turn off the pump.
 */
void test_high_duty_cycle(void) {
    // Set current scenario
    currentScenario = SCENARIO_HIGH_DUTY_CYCLE;
    
    // Generate a high duty cycle PWM signal (95%)
    EmulatorUtils::generatePwmSignal(PinConfig::SONIC_IN, 95);
    
    // Process the system for several cycles
    for (int i = 0; i < 5; i++) {
        System::process();
        EmulatorUtils::advanceTime(100);
    }
    
    // Verify expected system state
    TEST_ASSERT_TRUE(AirControl::isOn());
    TEST_ASSERT_FALSE(PumpControl::isOn()); // Pump should be off at high duty cycle
    
    // Verify PWM output is correctly processed and capped
    byte outputDuty = PwmControl::getDutyCycle(PinConfig::PWM_OUT);
    TEST_ASSERT_LESS_OR_EQUAL(77, outputDuty); // Should be capped at 77%
    
    // Verify pin states
    TEST_ASSERT_EQUAL(LOW, EmulatorUtils::readPinValue(PinConfig::AIR_OUT)); // LOW = ON for air
    TEST_ASSERT_EQUAL(HIGH, EmulatorUtils::readPinValue(PinConfig::PUMP_OUT)); // HIGH = OFF for pump
}

/**
 * @brief Test system behavior with low duty cycle input
 * 
 * This test verifies that the system responds correctly to low duty cycle input,
 * which should turn on the pump.
 */
void test_low_duty_cycle(void) {
    // Set current scenario
    currentScenario = SCENARIO_LOW_DUTY_CYCLE;
    
    // Generate a low duty cycle PWM signal (5%)
    EmulatorUtils::generatePwmSignal(PinConfig::SONIC_IN, 5);
    
    // Process the system for several cycles
    for (int i = 0; i < 5; i++) {
        System::process();
        EmulatorUtils::advanceTime(100);
    }
    
    // Verify expected system state
    TEST_ASSERT_TRUE(AirControl::isOn());
    TEST_ASSERT_TRUE(PumpControl::isOn()); // Pump should be ON at low duty cycle
    
    // Verify PWM output is correctly processed
    byte outputDuty = PwmControl::getDutyCycle(PinConfig::PWM_OUT);
    TEST_ASSERT_LESS_THAN(10, outputDuty); // Should be low
    
    // Verify pin states
    TEST_ASSERT_EQUAL(LOW, EmulatorUtils::readPinValue(PinConfig::AIR_OUT)); // LOW = ON for air
    TEST_ASSERT_EQUAL(LOW, EmulatorUtils::readPinValue(PinConfig::PUMP_OUT)); // LOW = ON for pump
}

/**
 * @brief Test air pressure timeout functionality
 * 
 * This test verifies that the air pressure is automatically turned off
 * after the timeout period expires.
 */
void test_air_timeout(void) {
    // Set current scenario
    currentScenario = SCENARIO_AIR_TIMEOUT;
    
    // Turn on air
    AirControl::turnOn();
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Fast-forward through most of the timeout period
    uint16_t timeoutCount = TimerManager::getMaxInterruptCount();
    
    // Trigger timer interrupts up to timeout
    for (uint16_t i = 0; i < timeoutCount; i++) {
        EmulatorUtils::triggerInterrupt(1); // Timer1 interrupt
        EmulatorUtils::advanceTime(10);
    }
    
    // Verify air is still on
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Trigger one more interrupt to exceed timeout
    EmulatorUtils::triggerInterrupt(1);
    EmulatorUtils::advanceTime(10);
    
    // Verify air turned off
    TEST_ASSERT_FALSE(AirControl::isOn());
    TEST_ASSERT_EQUAL(HIGH, EmulatorUtils::readPinValue(PinConfig::AIR_OUT)); // HIGH = OFF for air
}

/**
 * @brief Test system state transitions
 * 
 * This test verifies that the system can transition correctly between
 * different states based on changing inputs.
 */
void test_state_transitions(void) {
    // Start with normal operation
    EmulatorUtils::generatePwmSignal(PinConfig::SONIC_IN, 50);
    System::process();
    EmulatorUtils::advanceTime(100);
    
    // Verify initial state
    TEST_ASSERT_TRUE(AirControl::isOn());
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Transition to low duty cycle
    EmulatorUtils::generatePwmSignal(PinConfig::SONIC_IN, 1);
    System::process();
    EmulatorUtils::advanceTime(100);
    
    // Verify pump turned on
    TEST_ASSERT_TRUE(PumpControl::isOn());
    
    // Transition to high duty cycle
    EmulatorUtils::generatePwmSignal(PinConfig::SONIC_IN, 99);
    System::process();
    EmulatorUtils::advanceTime(100);
    
    // Verify pump turned off
    TEST_ASSERT_FALSE(PumpControl::isOn());
    
    // Turn off sonicator (simulated by setting a very high duty cycle)
    EmulatorUtils::generatePwmSignal(PinConfig::SONIC_IN, 100);
    EmulatorUtils::setPinValue(PinConfig::SONIC_IN, LOW);
    System::process();
    EmulatorUtils::advanceTime(100);
    
    // Verify system responds to no signal
    TEST_ASSERT_FALSE(PumpControl::isOn());
}

void setup() {
    // Allow time for system initialization
    EmulatorUtils::advanceTime(2000);
    
    // Initialize Unity
    UNITY_BEGIN();
    
    // Run the tests
    RUN_TEST(test_normal_operation);
    RUN_TEST(test_high_duty_cycle);
    RUN_TEST(test_low_duty_cycle);
    RUN_TEST(test_air_timeout);
    RUN_TEST(test_state_transitions);
    
    // Report test results
    UNITY_END();
}

void loop() {
    // Empty loop - all tests run in setup()
} 