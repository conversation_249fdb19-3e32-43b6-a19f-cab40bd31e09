#!/usr/bin/env python3
"""
Device Heartbeat Tests for ATTINY Control System

This module contains integration tests that verify the device heartbeat
functionality and its integration with Google Cloud services.
"""

import os
import sys
import time
import logging
import argparse
import uuid
import unittest
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime, timedelta

# Add parent directory to path for importing common modules
sys.path.append(str(Path(__file__).parent.parent))
from utils.test_helpers import setup_logger, generate_html_report, simulate_device_heartbeat

# Import Google Cloud libraries
try:
    from google.cloud import firestore
    from google.cloud import bigquery
except ImportError:
    logging.warning("Google Cloud libraries not installed. Some tests may fail.")


class DeviceHeartbeatTests(unittest.TestCase):
    """Test cases for device heartbeat functionality."""

    @classmethod
    def setUpClass(cls):
        """Set up the test environment."""
        # Generate a unique device ID for testing
        cls.test_device_id = f"test-device-{uuid.uuid4()}"
        cls.test_firmware_version = "1.0.0-test"
        
        # Check for GCP credentials
        if not os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
            logging.warning("GOOGLE_APPLICATION_CREDENTIALS not set. Cloud tests may be skipped.")
        
        # Set up Firestore and BigQuery clients if possible
        try:
            cls.firestore_client = firestore.Client()
            cls.bigquery_client = bigquery.Client()
            cls.cloud_clients_available = True
        except Exception as e:
            logging.warning(f"Failed to initialize cloud clients: {e}")
            cls.cloud_clients_available = False
        
        logging.info("Test environment setup complete")

    @classmethod
    def tearDownClass(cls):
        """Clean up the test environment."""
        logging.info("Cleaning up test environment...")
        
        # Clean up test data from Firestore if available
        if hasattr(cls, 'cloud_clients_available') and cls.cloud_clients_available:
            try:
                # Delete test device data from Firestore
                device_ref = cls.firestore_client.collection('devices').document(cls.test_device_id)
                device_ref.delete()
                
                # Note: We don't delete from BigQuery as it's not easily reversible
                logging.info("Test data cleaned up from Firestore")
            except Exception as e:
                logging.warning(f"Failed to clean up test data: {e}")
        
        logging.info("Test environment cleanup complete")

    def test_device_heartbeat_simulation(self):
        """Test that device heartbeat simulation works."""
        logging.info("Testing device heartbeat simulation...")
        
        # Simulate a device heartbeat
        response = simulate_device_heartbeat(
            self.test_device_id,
            self.test_firmware_version,
            "online"
        )
        
        # Check if simulation was successful
        if response is None:
            self.skipTest("Device heartbeat simulation failed - cloud function may not be deployed")
        
        # Verify response
        self.assertEqual(response.status_code, 200, f"Unexpected status code: {response.status_code}")
        
        # Verify response content
        response_json = response.json()
        self.assertIn("success", response_json, "Response missing 'success' field")
        self.assertTrue(response_json["success"], "Heartbeat was not successful")
        
        logging.info("Device heartbeat simulation test passed")

    def test_firestore_record_creation(self):
        """Test that a Firestore record is created for the device heartbeat."""
        logging.info("Testing Firestore record creation...")
        
        # Skip test if cloud clients are not available
        if not hasattr(self, 'cloud_clients_available') or not self.cloud_clients_available:
            self.skipTest("Cloud clients not available")
        
        # Simulate a device heartbeat
        response = simulate_device_heartbeat(
            self.test_device_id,
            self.test_firmware_version,
            "online"
        )
        
        if response is None or response.status_code != 200:
            self.skipTest("Device heartbeat simulation failed")
        
        # Wait for Firestore to be updated
        time.sleep(3)
        
        # Check if device record exists in Firestore
        device_ref = self.firestore_client.collection('devices').document(self.test_device_id)
        device_doc = device_ref.get()
        
        self.assertTrue(device_doc.exists, "Device record not found in Firestore")
        
        # Verify record fields
        device_data = device_doc.to_dict()
        self.assertEqual(device_data['deviceId'], self.test_device_id)
        self.assertEqual(device_data['firmwareVersion'], self.test_firmware_version)
        self.assertEqual(device_data['status'], "online")
        
        logging.info("Firestore record creation test passed")

    def test_bigquery_record_creation(self):
        """Test that a BigQuery record is created for the device heartbeat."""
        logging.info("Testing BigQuery record creation...")
        
        # Skip test if cloud clients are not available
        if not hasattr(self, 'cloud_clients_available') or not self.cloud_clients_available:
            self.skipTest("Cloud clients not available")
        
        # Simulate multiple device heartbeats to ensure we have data
        for _ in range(3):
            response = simulate_device_heartbeat(
                self.test_device_id,
                self.test_firmware_version,
                "online"
            )
            time.sleep(1)
        
        if response is None or response.status_code != 200:
            self.skipTest("Device heartbeat simulation failed")
        
        # Wait for BigQuery to be updated
        time.sleep(5)
        
        # Query BigQuery for the device heartbeat records
        query = f"""
        SELECT deviceId, firmwareVersion, status
        FROM `cannasol-automation-suite.device_metrics.heartbeats`
        WHERE deviceId = '{self.test_device_id}'
        ORDER BY timestamp DESC
        LIMIT 1
        """
        
        try:
            query_job = self.bigquery_client.query(query)
            results = list(query_job.result())
            
            # Verify at least one record was found
            self.assertGreater(len(results), 0, "No records found in BigQuery")
            
            # Verify record fields
            row = results[0]
            self.assertEqual(row['deviceId'], self.test_device_id)
            self.assertEqual(row['firmwareVersion'], self.test_firmware_version)
            self.assertEqual(row['status'], "online")
            
            logging.info("BigQuery record creation test passed")
        except Exception as e:
            logging.error(f"BigQuery query failed: {e}")
            self.skipTest(f"BigQuery query failed: {e}")

    def test_device_status_change(self):
        """Test that device status changes are properly recorded."""
        logging.info("Testing device status change...")
        
        # Skip test if cloud clients are not available
        if not hasattr(self, 'cloud_clients_available') or not self.cloud_clients_available:
            self.skipTest("Cloud clients not available")
        
        # First, set device status to online
        response1 = simulate_device_heartbeat(
            self.test_device_id,
            self.test_firmware_version,
            "online"
        )
        
        if response1 is None or response1.status_code != 200:
            self.skipTest("Device heartbeat simulation failed")
        
        time.sleep(2)
        
        # Then, change status to degraded
        response2 = simulate_device_heartbeat(
            self.test_device_id,
            self.test_firmware_version,
            "degraded"
        )
        
        if response2 is None or response2.status_code != 200:
            self.skipTest("Device heartbeat simulation failed")
        
        time.sleep(2)
        
        # Check if device status is updated in Firestore
        device_ref = self.firestore_client.collection('devices').document(self.test_device_id)
        device_doc = device_ref.get()
        
        self.assertTrue(device_doc.exists, "Device record not found in Firestore")
        
        # Verify updated status
        device_data = device_doc.to_dict()
        self.assertEqual(device_data['status'], "degraded", "Device status not updated in Firestore")
        
        logging.info("Device status change test passed")


def main():
    """Main function to run the tests and generate reports."""
    parser = argparse.ArgumentParser(description='Run device heartbeat tests')
    parser.add_argument('--log-file', default=None, help='Log file path')
    parser.add_argument('--report-file', default=None, help='HTML report file path')
    parser.add_argument('--junit-file', default=None, help='JUnit XML report file path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger(log_level, args.log_file)
    
    # Run tests
    logger.info("Starting device heartbeat tests...")
    
    # Run tests with unittest
    test_suite = unittest.TestLoader().loadTestsFromTestCase(DeviceHeartbeatTests)
    test_result = unittest.TextTestRunner(verbosity=2).run(test_suite)
    
    # Generate JUnit XML report if requested
    if args.junit_file:
        junit_path = Path(args.junit_file)
        junit_path.parent.mkdir(parents=True, exist_ok=True)
        
        test_suite = unittest.TestLoader().loadTestsFromTestCase(DeviceHeartbeatTests)
        
        # Create JUnit XML
        root = ET.Element('testsuite')
        root.set('name', 'DeviceHeartbeatTests')
        root.set('tests', str(test_result.testsRun))
        root.set('failures', str(len(test_result.failures)))
        root.set('errors', str(len(test_result.errors)))
        root.set('skipped', str(len(test_result.skipped)))
        
        for test_case, error in test_result.failures + test_result.errors:
            case = ET.SubElement(root, 'testcase')
            case.set('name', test_case.id().split('.')[-1])
            case.set('classname', 'DeviceHeartbeatTests')
            
            if test_case in [f[0] for f in test_result.failures]:
                failure = ET.SubElement(case, 'failure')
                failure.set('type', 'AssertionError')
                failure.text = error
            else:
                error_elem = ET.SubElement(case, 'error')
                error_elem.set('type', 'Exception')
                error_elem.text = error
        
        # Add skipped tests
        for test_case, reason in test_result.skipped:
            case = ET.SubElement(root, 'testcase')
            case.set('name', test_case.id().split('.')[-1])
            case.set('classname', 'DeviceHeartbeatTests')
            
            skipped = ET.SubElement(case, 'skipped')
            skipped.set('message', reason)
        
        # Write JUnit XML
        tree = ET.ElementTree(root)
        tree.write(args.junit_file, encoding='utf-8', xml_declaration=True)
        logger.info(f"JUnit XML report written to {args.junit_file}")
    
    # Generate HTML report if requested
    if args.report_file:
        report_path = Path(args.report_file)
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        generate_html_report(
            'Device Heartbeat Tests',
            test_result,
            args.report_file
        )
        logger.info(f"HTML report written to {args.report_file}")
    
    logger.info("Device heartbeat tests completed")
    
    # Return exit code based on test results
    return 0 if test_result.wasSuccessful() else 1


if __name__ == '__main__':
    sys.exit(main()) 