/**
 * @file test_air_control_integration.cpp
 * @brief Integration test for air control functionality
 */

#ifdef EMULATION_MODE

#include <unity.h>
#include <Arduino.h>
#include "modules/air_control.h"
#include "modules/pin_config.h"
#include "modules/timer_manager.h"

// Define timeout for tests to prevent infinite loops
#ifndef EMULATION_TIMEOUT
#define EMULATION_TIMEOUT 30000
#endif

// Time tracking
unsigned long startTime = 0;
bool timeoutOccurred = false;

void setUp(void) {
    // Reset pins and state before each test
    for (int i = 0; i < 6; i++) {
        pinMode(i, INPUT);
        digitalWrite(i, LOW);
    }
    
    // Initialize modules
    pinMode(PinConfig::AIR_OUT, OUTPUT);
    pinMode(PinConfig::SONIC_IN, INPUT);
    
    // Reset timer
    TimerManager::setInterruptCount(0);
    
    // Track start time for timeout detection
    startTime = millis();
    timeoutOccurred = false;
}

void tearDown(void) {
    // Clean up after each test
}

/**
 * Check if too much time has elapsed
 */
bool checkTimeout() {
    if (millis() - startTime > EMULATION_TIMEOUT) {
        timeoutOccurred = true;
        return true;
    }
    return false;
}

/**
 * Test initializing air control module
 */
void test_air_control_initialization(void) {
    // Initialize air control
    AirControl::init();
    
    // Air should be off initially (default state)
    TEST_ASSERT_EQUAL(LOW, digitalRead(PinConfig::AIR_OUT));
    TEST_ASSERT_FALSE(AirControl::isOn());
}

/**
 * Test turning air on and off
 */
void test_air_on_off(void) {
    // Initialize
    AirControl::init();
    
    // Turn air on
    AirControl::turnOn();
    TEST_ASSERT_EQUAL(HIGH, digitalRead(PinConfig::AIR_OUT));
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Turn air off
    AirControl::turnOff();
    TEST_ASSERT_EQUAL(LOW, digitalRead(PinConfig::AIR_OUT));
    TEST_ASSERT_FALSE(AirControl::isOn());
}

/**
 * Test sonicator input triggering air
 */
void test_sonicator_input(void) {
    // Initialize
    AirControl::init();
    
    // Simulate sonicator turning on (active LOW)
    digitalWrite(PinConfig::SONIC_IN, LOW);
    
    // Process should turn air on
    AirControl::process();
    TEST_ASSERT_EQUAL(HIGH, digitalRead(PinConfig::AIR_OUT));
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Simulate sonicator turning off
    digitalWrite(PinConfig::SONIC_IN, HIGH);
    
    // Air should stay on until timer expires
    AirControl::process();
    TEST_ASSERT_EQUAL(HIGH, digitalRead(PinConfig::AIR_OUT));
    TEST_ASSERT_TRUE(AirControl::isOn());
}

/**
 * Test automatic shutoff after timeout
 */
void test_timeout_shutoff(void) {
    // Initialize
    AirControl::init();
    TimerManager::initTimer1();
    
    // Turn air on
    AirControl::turnOn();
    TEST_ASSERT_TRUE(AirControl::isOn());
    
    // Simulate timer reaching max count
    TimerManager::setInterruptCount(TimerManager::getMaxInterruptCount());
    
    // Trigger ISR
    TIM1_COMPA_vect();
    
    // Air should now be off
    TEST_ASSERT_FALSE(AirControl::isOn());
    TEST_ASSERT_EQUAL(LOW, digitalRead(PinConfig::AIR_OUT));
}

/**
 * Run tests with timeout safety
 */
#ifdef SIMULATOR_DEBUG
#define RUN_TEST_WITH_TIMEOUT(test) \
    do { \
        startTime = millis(); \
        timeoutOccurred = false; \
        RUN_TEST(test); \
        if (timeoutOccurred) { \
            TEST_FAIL_MESSAGE("Test timed out"); \
        } \
    } while(0)
#else
#define RUN_TEST_WITH_TIMEOUT(test) RUN_TEST(test)
#endif

/**
 * Main function for integration tests
 */
void setup() {
    delay(2000);  // Allow simulator to stabilize
    UNITY_BEGIN();
    
    RUN_TEST_WITH_TIMEOUT(test_air_control_initialization);
    RUN_TEST_WITH_TIMEOUT(test_air_on_off);
    RUN_TEST_WITH_TIMEOUT(test_sonicator_input);
    RUN_TEST_WITH_TIMEOUT(test_timeout_shutoff);
    
    UNITY_END();
}

void loop() {
    // Empty - we only want the tests to run once
}

#else
// Not in emulation mode, stub implementation
void setup() {}
void loop() {}
#endif // EMULATION_MODE 