#!/bin/bash
# Script to update the CI workflow with integration testing steps

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
CI_WORKFLOW_FILE="$PROJECT_ROOT/.github/workflows/ci.yml"

echo -e "${BLUE}Updating CI workflow with integration testing...${NC}"

# Check if CI workflow file exists
if [ ! -f "$CI_WORKFLOW_FILE" ]; then
    echo -e "${RED}CI workflow file not found: $CI_WORKFLOW_FILE${NC}"
    exit 1
fi

# Create backup of the original file
cp "$CI_WORKFLOW_FILE" "$CI_WORKFLOW_FILE.bak"
echo -e "${GREEN}Created backup of CI workflow file: $CI_WORKFLOW_FILE.bak${NC}"

# Read the CI workflow file
ci_workflow=$(cat "$CI_WORKFLOW_FILE")

# Check if integration testing is already included
if [[ "$ci_workflow" == *"Integration Tests"* ]]; then
    echo -e "${YELLOW}Integration testing is already included in the CI workflow.${NC}"
    echo -e "${YELLOW}Skipping update.${NC}"
    exit 0
fi

# Find the position to insert the integration test job
# We'll add it right after the unit-tests job
# Find the line number of the last line of the unit-tests job
unit_tests_end=$(grep -n "unit-tests:" "$CI_WORKFLOW_FILE" | cut -d: -f1)
if [ -z "$unit_tests_end" ]; then
    echo -e "${RED}Could not find unit-tests job in the CI workflow.${NC}"
    exit 1
fi

# Find the next job after unit-tests
next_job_line=$(tail -n +$unit_tests_end "$CI_WORKFLOW_FILE" | grep -n "^  [a-zA-Z0-9_-]\+:" | head -n 1 | cut -d: -f1)
if [ -z "$next_job_line" ]; then
    echo -e "${RED}Could not find next job after unit-tests.${NC}"
    exit 1
fi

# Calculate the position for insertion
insert_position=$((unit_tests_end + next_job_line - 1))

# Create the integration test job content
integration_test_job=$(cat << 'EOF'

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, build]
    if: success() && (github.event_name == 'push' || github.event_name == 'pull_request')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r test/integration/requirements.txt
          pip install platformio
      
      - name: Set up GCP authentication
        uses: google-github-actions/auth@v1
        with:
          credentials_json: '${{ secrets.GCP_SA_KEY }}'
      
      - name: Download firmware artifact
        uses: actions/download-artifact@v3
        with:
          name: firmware
          path: .pio/build/attiny85
      
      - name: Install SimulAVR
        run: |
          sudo apt-get update
          sudo apt-get install -y simulavr
      
      - name: Run firmware integration tests
        id: firmware_tests
        run: |
          chmod +x test/integration/run_integration_tests.sh
          test/integration/run_integration_tests.sh --category firmware --junit-file test/integration/test-results/junit/firmware-tests.xml --verbose
        continue-on-error: true
      
      - name: Run cloud integration tests
        id: cloud_tests
        run: |
          test/integration/run_integration_tests.sh --category cloud --junit-file test/integration/test-results/junit/cloud-tests.xml --verbose
        continue-on-error: true
      
      - name: Run monitoring integration tests
        id: monitoring_tests
        run: |
          test/integration/run_integration_tests.sh --category monitoring --junit-file test/integration/test-results/junit/monitoring-tests.xml --verbose
        continue-on-error: true
      
      - name: Run workflow integration tests
        id: workflow_tests
        run: |
          test/integration/run_integration_tests.sh --category workflow --junit-file test/integration/test-results/junit/workflow-tests.xml --verbose
        continue-on-error: true
      
      - name: Generate integration test reports
        run: |
          python test/integration/utils/generate_summary.py --junit-dir test/integration/test-results/junit --output-file test/integration/test-results/reports/summary.html
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: integration-test-results
          path: test/integration/test-results
      
      - name: Check all tests passed
        run: |
          if [[ "${{ steps.firmware_tests.outcome }}" == "success" && "${{ steps.cloud_tests.outcome }}" == "success" && "${{ steps.monitoring_tests.outcome }}" == "success" && "${{ steps.workflow_tests.outcome }}" == "success" ]]; then
            echo "All integration tests passed!"
            exit 0
          else
            echo "One or more integration tests failed."
            exit 1
          fi
EOF
)

# Insert the integration test job
{
    head -n $insert_position "$CI_WORKFLOW_FILE"
    echo "$integration_test_job"
    tail -n +$insert_position "$CI_WORKFLOW_FILE"
} > "$CI_WORKFLOW_FILE.new"

# Replace the original file
mv "$CI_WORKFLOW_FILE.new" "$CI_WORKFLOW_FILE"

echo -e "${GREEN}Integration testing has been added to the CI workflow.${NC}"
echo -e "${GREEN}Original file was backed up to: $CI_WORKFLOW_FILE.bak${NC}"
echo -e "${BLUE}Please check the workflow file to ensure it was updated correctly.${NC}" 