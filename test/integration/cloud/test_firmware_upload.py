#!/usr/bin/env python3
"""
Firmware Upload Tests for ATTINY Control System

This module contains integration tests that verify the firmware upload
functionality and its integration with Google Cloud Storage.
"""

import os
import sys
import time
import logging
import argparse
import uuid
import unittest
import xml.etree.ElementTree as ET
from pathlib import Path

# Add parent directory to path for importing common modules
sys.path.append(str(Path(__file__).parent.parent))
from utils.test_helpers import setup_logger, generate_html_report

# Import Google Cloud libraries
try:
    from google.cloud import storage
    from google.cloud import firestore
except ImportError:
    logging.warning("Google Cloud libraries not installed. Some tests may fail.")


class FirmwareUploadTests(unittest.TestCase):
    """Test cases for firmware upload functionality."""

    @classmethod
    def setUpClass(cls):
        """Set up the test environment."""
        # Generate a unique test identifier
        cls.test_id = str(uuid.uuid4())[:8]
        cls.test_firmware_version = f"1.0.0-test-{cls.test_id}"
        
        # Get the project root directory
        cls.project_root = Path(__file__).parent.parent.parent.parent
        
        # Find or build the firmware hex file
        cls.firmware_path = cls.project_root / ".pio" / "build" / "attiny85" / "firmware.hex"
        if not cls.firmware_path.exists():
            logging.info("Building firmware...")
            
            # Build the firmware
            import subprocess
            build_process = subprocess.run(
                ["pio", "run", "-e", "attiny85"],
                cwd=cls.project_root,
                capture_output=True,
                text=True
            )
            
            if build_process.returncode != 0:
                logging.error(f"Failed to build firmware: {build_process.stderr}")
                raise FileNotFoundError(f"Firmware not found and failed to build")
        
        # Check for GCP credentials
        if not os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
            logging.warning("GOOGLE_APPLICATION_CREDENTIALS not set. Cloud tests may be skipped.")
        
        # Set up Storage and Firestore clients if possible
        try:
            cls.storage_client = storage.Client()
            cls.firestore_client = firestore.Client()
            cls.cloud_clients_available = True
            
            # Get or create the test bucket
            cls.bucket_name = os.environ.get('TEST_BUCKET_NAME', 'attiny-test-firmware')
            
            try:
                cls.bucket = cls.storage_client.get_bucket(cls.bucket_name)
            except Exception:
                logging.info(f"Creating test bucket: {cls.bucket_name}")
                cls.bucket = cls.storage_client.create_bucket(cls.bucket_name)
        except Exception as e:
            logging.warning(f"Failed to initialize cloud clients: {e}")
            cls.cloud_clients_available = False
        
        logging.info("Test environment setup complete")

    @classmethod
    def tearDownClass(cls):
        """Clean up the test environment."""
        logging.info("Cleaning up test environment...")
        
        # Clean up test data from GCP if available
        if hasattr(cls, 'cloud_clients_available') and cls.cloud_clients_available:
            try:
                # Delete test firmware from Cloud Storage
                blobs = cls.bucket.list_blobs(prefix=f"firmware/test/{cls.test_id}/")
                for blob in blobs:
                    blob.delete()
                
                # Delete test version data from Firestore
                version_ref = cls.firestore_client.collection('firmware_versions').document(cls.test_firmware_version)
                version_ref.delete()
                
                logging.info("Test data cleaned up from GCP")
            except Exception as e:
                logging.warning(f"Failed to clean up test data: {e}")
        
        logging.info("Test environment cleanup complete")

    def test_firmware_upload_to_storage(self):
        """Test that firmware can be uploaded to Cloud Storage."""
        logging.info("Testing firmware upload to Cloud Storage...")
        
        # Skip test if cloud clients are not available
        if not hasattr(self, 'cloud_clients_available') or not self.cloud_clients_available:
            self.skipTest("Cloud clients not available")
        
        # Create blob path
        blob_path = f"firmware/test/{self.test_id}/attiny85.hex"
        
        # Create blob and upload firmware
        blob = self.bucket.blob(blob_path)
        blob.upload_from_filename(self.firmware_path)
        
        # Verify blob exists
        self.assertTrue(blob.exists(), "Uploaded firmware blob not found in Cloud Storage")
        
        # Check metadata
        blob.reload()  # Refresh metadata
        metadata = blob.metadata or {}
        
        # Set firmware version metadata
        metadata['firmware_version'] = self.test_firmware_version
        metadata['device_type'] = 'attiny85'
        metadata['upload_time'] = time.time()
        blob.metadata = metadata
        blob.patch()
        
        logging.info("Firmware upload to Cloud Storage test passed")

    def test_firmware_metadata_retrieval(self):
        """Test that firmware metadata can be retrieved from Cloud Storage."""
        logging.info("Testing firmware metadata retrieval...")
        
        # Skip test if cloud clients are not available
        if not hasattr(self, 'cloud_clients_available') or not self.cloud_clients_available:
            self.skipTest("Cloud clients not available")
        
        # Get blob
        blob_path = f"firmware/test/{self.test_id}/attiny85.hex"
        blob = self.bucket.blob(blob_path)
        
        # Verify blob exists
        self.assertTrue(blob.exists(), "Firmware blob not found in Cloud Storage")
        
        # Get metadata
        blob.reload()  # Refresh metadata
        metadata = blob.metadata or {}
        
        # Verify metadata fields
        self.assertIn('firmware_version', metadata, "Firmware version metadata not found")
        self.assertEqual(metadata['firmware_version'], self.test_firmware_version, "Firmware version metadata incorrect")
        self.assertIn('device_type', metadata, "Device type metadata not found")
        self.assertEqual(metadata['device_type'], 'attiny85', "Device type metadata incorrect")
        
        logging.info("Firmware metadata retrieval test passed")

    def test_firmware_version_tracking(self):
        """Test that firmware versions are tracked in Firestore."""
        logging.info("Testing firmware version tracking...")
        
        # Skip test if cloud clients are not available
        if not hasattr(self, 'cloud_clients_available') or not self.cloud_clients_available:
            self.skipTest("Cloud clients not available")
        
        # Create firmware version document in Firestore
        version_ref = self.firestore_client.collection('firmware_versions').document(self.test_firmware_version)
        version_ref.set({
            'version': self.test_firmware_version,
            'storage_path': f"firmware/test/{self.test_id}/attiny85.hex",
            'device_type': 'attiny85',
            'upload_time': firestore.SERVER_TIMESTAMP,
            'test_build': True
        })
        
        # Verify document was created
        version_doc = version_ref.get()
        self.assertTrue(version_doc.exists, "Firmware version document not found in Firestore")
        
        # Verify document fields
        version_data = version_doc.to_dict()
        self.assertEqual(version_data['version'], self.test_firmware_version, "Firmware version field incorrect")
        self.assertEqual(version_data['storage_path'], f"firmware/test/{self.test_id}/attiny85.hex", "Storage path field incorrect")
        self.assertEqual(version_data['device_type'], 'attiny85', "Device type field incorrect")
        self.assertTrue(version_data['test_build'], "Test build field incorrect")
        
        logging.info("Firmware version tracking test passed")

    def test_firmware_download(self):
        """Test that firmware can be downloaded from Cloud Storage."""
        logging.info("Testing firmware download...")
        
        # Skip test if cloud clients are not available
        if not hasattr(self, 'cloud_clients_available') or not self.cloud_clients_available:
            self.skipTest("Cloud clients not available")
        
        # Get blob
        blob_path = f"firmware/test/{self.test_id}/attiny85.hex"
        blob = self.bucket.blob(blob_path)
        
        # Verify blob exists
        self.assertTrue(blob.exists(), "Firmware blob not found in Cloud Storage")
        
        # Download firmware to a temporary file
        temp_file = Path(self.project_root) / "test" / "integration" / "test-results" / "cloud" / f"downloaded_firmware_{self.test_id}.hex"
        temp_file.parent.mkdir(parents=True, exist_ok=True)
        
        blob.download_to_filename(temp_file)
        
        # Verify downloaded file exists and has content
        self.assertTrue(temp_file.exists(), "Downloaded firmware file not found")
        self.assertGreater(temp_file.stat().st_size, 0, "Downloaded firmware file is empty")
        
        # Compare original and downloaded files
        with open(self.firmware_path, 'rb') as original:
            original_content = original.read()
        
        with open(temp_file, 'rb') as downloaded:
            downloaded_content = downloaded.read()
        
        self.assertEqual(original_content, downloaded_content, "Downloaded firmware does not match original")
        
        # Clean up temporary file
        temp_file.unlink()
        
        logging.info("Firmware download test passed")


def main():
    """Main function to run the tests and generate reports."""
    parser = argparse.ArgumentParser(description='Run firmware upload tests')
    parser.add_argument('--log-file', default=None, help='Log file path')
    parser.add_argument('--report-file', default=None, help='HTML report file path')
    parser.add_argument('--junit-file', default=None, help='JUnit XML report file path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger(log_level, args.log_file)
    
    # Run tests
    logger.info("Starting firmware upload tests...")
    
    # Run tests with unittest
    test_suite = unittest.TestLoader().loadTestsFromTestCase(FirmwareUploadTests)
    test_result = unittest.TextTestRunner(verbosity=2).run(test_suite)
    
    # Generate JUnit XML report if requested
    if args.junit_file:
        junit_path = Path(args.junit_file)
        junit_path.parent.mkdir(parents=True, exist_ok=True)
        
        test_suite = unittest.TestLoader().loadTestsFromTestCase(FirmwareUploadTests)
        
        # Create JUnit XML
        root = ET.Element('testsuite')
        root.set('name', 'FirmwareUploadTests')
        root.set('tests', str(test_result.testsRun))
        root.set('failures', str(len(test_result.failures)))
        root.set('errors', str(len(test_result.errors)))
        root.set('skipped', str(len(test_result.skipped)))
        
        for test_case, error in test_result.failures + test_result.errors:
            case = ET.SubElement(root, 'testcase')
            case.set('name', test_case.id().split('.')[-1])
            case.set('classname', 'FirmwareUploadTests')
            
            if test_case in [f[0] for f in test_result.failures]:
                failure = ET.SubElement(case, 'failure')
                failure.set('type', 'AssertionError')
                failure.text = error
            else:
                error_elem = ET.SubElement(case, 'error')
                error_elem.set('type', 'Exception')
                error_elem.text = error
        
        # Add skipped tests
        for test_case, reason in test_result.skipped:
            case = ET.SubElement(root, 'testcase')
            case.set('name', test_case.id().split('.')[-1])
            case.set('classname', 'FirmwareUploadTests')
            
            skipped = ET.SubElement(case, 'skipped')
            skipped.set('message', reason)
        
        # Write JUnit XML
        tree = ET.ElementTree(root)
        tree.write(args.junit_file, encoding='utf-8', xml_declaration=True)
        logger.info(f"JUnit XML report written to {args.junit_file}")
    
    # Generate HTML report if requested
    if args.report_file:
        report_path = Path(args.report_file)
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        generate_html_report(
            'Firmware Upload Tests',
            test_result,
            args.report_file
        )
        logger.info(f"HTML report written to {args.report_file}")
    
    logger.info("Firmware upload tests completed")
    
    # Return exit code based on test results
    return 0 if test_result.wasSuccessful() else 1


if __name__ == '__main__':
    sys.exit(main()) 