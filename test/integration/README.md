# Final Integration Testing for ATTINY Control System

This directory contains the end-to-end integration tests for the ATTINY Control System. These tests validate the entire system, from firmware functionality to cloud integration and monitoring.

## Test Environment

The integration test environment consists of:

1. **Hardware Emulation**: SimulAVR for emulating the ATTINY85 hardware
2. **Cloud Infrastructure**: Test instances of Google Cloud services
3. **CI/CD Pipeline**: GitHub Actions workflow for automated testing
4. **Monitoring**: Test instance of the monitoring dashboard

## Test Categories

The integration tests are organized into the following categories:

### 1. Firmware Functionality Tests

These tests verify that the firmware operates correctly on the emulated hardware:

- Core functionality tests
- Sensor reading tests
- Control algorithm tests
- Error handling tests

### 2. Cloud Integration Tests

These tests verify the interaction between the firmware and cloud services:

- Firmware upload tests
- Version tracking tests
- Analytics integration tests

### 3. Monitoring and Alerting Tests

These tests verify the monitoring and alerting system:

- Device heartbeat tests
- Alert triggering tests
- Dashboard functionality tests

### 4. End-to-End Workflow Tests

These tests verify complete workflows:

- Development to deployment workflow
- Firmware update workflow
- Monitoring and alerting workflow

## Running the Tests

To run the full integration test suite:

```bash
./run_integration_tests.sh
```

To run a specific test category:

```bash
./run_integration_tests.sh --category [firmware|cloud|monitoring|workflow]
```

## Test Results

The test results are stored in the following formats:

1. JUnit XML reports for CI/CD integration
2. HTML reports for human-readable results
3. Log files for detailed analysis

Results are stored in the `test-results` directory and are also uploaded as build artifacts in GitHub Actions.

## Integration with CI/CD

The integration tests are automatically run as part of the CI/CD pipeline for:

1. Pull requests to the main branch
2. Nightly builds
3. Release builds

## Manual Testing Procedures

For manual testing, refer to the documents in the `procedures` directory:

- `hardware_test_procedure.md`: Testing with actual hardware
- `cloud_test_procedure.md`: Testing cloud integration
- `monitoring_test_procedure.md`: Testing monitoring and alerts 