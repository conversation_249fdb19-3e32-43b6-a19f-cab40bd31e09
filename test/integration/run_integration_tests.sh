#!/bin/bash
# Integration Test Runner for ATTINY Control System

# Exit on error
set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
TEST_RESULTS_DIR="$SCRIPT_DIR/test-results"
LOG_DIR="$TEST_RESULTS_DIR/logs"
REPORT_DIR="$TEST_RESULTS_DIR/reports"
JUNIT_DIR="$TEST_RESULTS_DIR/junit"

# Parse command line arguments
TEST_CATEGORY="all"
VERBOSE=false
DRY_RUN=false

function print_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --category [firmware|cloud|monitoring|workflow|all]   Test category to run (default: all)"
    echo "  --verbose                                             Enable verbose output"
    echo "  --dry-run                                             Show what would be executed without running tests"
    echo "  --help                                                Show this help message"
}

while [[ $# -gt 0 ]]; do
    case "$1" in
        --category)
            TEST_CATEGORY="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Validate test category
if [[ ! "$TEST_CATEGORY" =~ ^(firmware|cloud|monitoring|workflow|all)$ ]]; then
    echo -e "${RED}Error: Invalid test category '$TEST_CATEGORY'${NC}"
    print_usage
    exit 1
fi

# Create test results directories
function create_directories {
    echo -e "${BLUE}Creating test results directories...${NC}"
    
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$REPORT_DIR"
    mkdir -p "$JUNIT_DIR"
    
    # Create category-specific directories
    mkdir -p "$LOG_DIR/firmware"
    mkdir -p "$LOG_DIR/cloud"
    mkdir -p "$LOG_DIR/monitoring"
    mkdir -p "$LOG_DIR/workflow"
    
    mkdir -p "$REPORT_DIR/firmware"
    mkdir -p "$REPORT_DIR/cloud"
    mkdir -p "$REPORT_DIR/monitoring"
    mkdir -p "$REPORT_DIR/workflow"
    
    mkdir -p "$JUNIT_DIR/firmware"
    mkdir -p "$JUNIT_DIR/cloud"
    mkdir -p "$JUNIT_DIR/monitoring"
    mkdir -p "$JUNIT_DIR/workflow"
}

# Setup test environment
function setup_environment {
    echo -e "${BLUE}Setting up test environment...${NC}"
    
    # Load environment variables from .env file if it exists
    if [ -f "$SCRIPT_DIR/.env" ]; then
        echo -e "${BLUE}Loading environment variables from .env file...${NC}"
        source "$SCRIPT_DIR/.env"
    fi
    
    # Check for required tools
    command -v python3 >/dev/null 2>&1 || { echo -e "${RED}Error: python3 is required but not installed.${NC}"; exit 1; }
    command -v simulavr >/dev/null 2>&1 || { echo -e "${RED}Error: simulavr is required but not installed.${NC}"; exit 1; }
    
    # Setup Python virtual environment if needed
    if [ ! -d "$SCRIPT_DIR/venv" ]; then
        echo -e "${BLUE}Creating Python virtual environment...${NC}"
        python3 -m venv "$SCRIPT_DIR/venv"
        source "$SCRIPT_DIR/venv/bin/activate"
        pip install -r "$SCRIPT_DIR/requirements.txt"
    else
        source "$SCRIPT_DIR/venv/bin/activate"
    fi
    
    # Check for GCP credentials
    if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
        echo -e "${YELLOW}Warning: GOOGLE_APPLICATION_CREDENTIALS not set. Cloud tests may fail.${NC}"
    fi
}

# Run firmware tests
function run_firmware_tests {
    echo -e "${BLUE}Running firmware functionality tests...${NC}"
    
    # Core functionality tests
    echo -e "${GREEN}Running core functionality tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/firmware/test_core_functionality.py" \
            --log-file "$LOG_DIR/firmware/core.log" \
            --report-file "$REPORT_DIR/firmware/core.html" \
            --junit-file "$JUNIT_DIR/firmware/core.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/firmware/test_core_functionality.py"
    fi
    
    # Sensor reading tests
    echo -e "${GREEN}Running sensor reading tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/firmware/test_sensor_reading.py" \
            --log-file "$LOG_DIR/firmware/sensor.log" \
            --report-file "$REPORT_DIR/firmware/sensor.html" \
            --junit-file "$JUNIT_DIR/firmware/sensor.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/firmware/test_sensor_reading.py"
    fi
    
    # Control algorithm tests
    echo -e "${GREEN}Running control algorithm tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/firmware/test_control_algorithm.py" \
            --log-file "$LOG_DIR/firmware/control.log" \
            --report-file "$REPORT_DIR/firmware/control.html" \
            --junit-file "$JUNIT_DIR/firmware/control.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/firmware/test_control_algorithm.py"
    fi
    
    # Error handling tests
    echo -e "${GREEN}Running error handling tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/firmware/test_error_handling.py" \
            --log-file "$LOG_DIR/firmware/error.log" \
            --report-file "$REPORT_DIR/firmware/error.html" \
            --junit-file "$JUNIT_DIR/firmware/error.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/firmware/test_error_handling.py"
    fi
}

# Run cloud integration tests
function run_cloud_tests {
    echo -e "${BLUE}Running cloud integration tests...${NC}"
    
    # Firmware upload tests
    echo -e "${GREEN}Running firmware upload tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/cloud/test_firmware_upload.py" \
            --log-file "$LOG_DIR/cloud/upload.log" \
            --report-file "$REPORT_DIR/cloud/upload.html" \
            --junit-file "$JUNIT_DIR/cloud/upload.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/cloud/test_firmware_upload.py"
    fi
    
    # Version tracking tests
    echo -e "${GREEN}Running version tracking tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/cloud/test_version_tracking.py" \
            --log-file "$LOG_DIR/cloud/version.log" \
            --report-file "$REPORT_DIR/cloud/version.html" \
            --junit-file "$JUNIT_DIR/cloud/version.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/cloud/test_version_tracking.py"
    fi
    
    # Analytics integration tests
    echo -e "${GREEN}Running analytics integration tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/cloud/test_analytics.py" \
            --log-file "$LOG_DIR/cloud/analytics.log" \
            --report-file "$REPORT_DIR/cloud/analytics.html" \
            --junit-file "$JUNIT_DIR/cloud/analytics.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/cloud/test_analytics.py"
    fi
}

# Run monitoring and alerting tests
function run_monitoring_tests {
    echo -e "${BLUE}Running monitoring and alerting tests...${NC}"
    
    # Device heartbeat tests
    echo -e "${GREEN}Running device heartbeat tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/monitoring/test_device_heartbeat.py" \
            --log-file "$LOG_DIR/monitoring/heartbeat.log" \
            --report-file "$REPORT_DIR/monitoring/heartbeat.html" \
            --junit-file "$JUNIT_DIR/monitoring/heartbeat.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/monitoring/test_device_heartbeat.py"
    fi
    
    # Alert triggering tests
    echo -e "${GREEN}Running alert triggering tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/monitoring/test_alert_triggering.py" \
            --log-file "$LOG_DIR/monitoring/alert.log" \
            --report-file "$REPORT_DIR/monitoring/alert.html" \
            --junit-file "$JUNIT_DIR/monitoring/alert.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/monitoring/test_alert_triggering.py"
    fi
    
    # Dashboard functionality tests
    echo -e "${GREEN}Running dashboard functionality tests...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/monitoring/test_dashboard.py" \
            --log-file "$LOG_DIR/monitoring/dashboard.log" \
            --report-file "$REPORT_DIR/monitoring/dashboard.html" \
            --junit-file "$JUNIT_DIR/monitoring/dashboard.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/monitoring/test_dashboard.py"
    fi
}

# Run end-to-end workflow tests
function run_workflow_tests {
    echo -e "${BLUE}Running end-to-end workflow tests...${NC}"
    
    # Development to deployment workflow
    echo -e "${GREEN}Running development to deployment workflow test...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/workflow/test_dev_to_deploy.py" \
            --log-file "$LOG_DIR/workflow/dev_to_deploy.log" \
            --report-file "$REPORT_DIR/workflow/dev_to_deploy.html" \
            --junit-file "$JUNIT_DIR/workflow/dev_to_deploy.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/workflow/test_dev_to_deploy.py"
    fi
    
    # Firmware update workflow
    echo -e "${GREEN}Running firmware update workflow test...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/workflow/test_firmware_update.py" \
            --log-file "$LOG_DIR/workflow/firmware_update.log" \
            --report-file "$REPORT_DIR/workflow/firmware_update.html" \
            --junit-file "$JUNIT_DIR/workflow/firmware_update.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/workflow/test_firmware_update.py"
    fi
    
    # Monitoring and alerting workflow
    echo -e "${GREEN}Running monitoring and alerting workflow test...${NC}"
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/workflow/test_monitoring_workflow.py" \
            --log-file "$LOG_DIR/workflow/monitoring.log" \
            --report-file "$REPORT_DIR/workflow/monitoring.html" \
            --junit-file "$JUNIT_DIR/workflow/monitoring.xml" \
            --verbose $VERBOSE
    else
        echo "Would run: python3 $SCRIPT_DIR/workflow/test_monitoring_workflow.py"
    fi
}

# Generate summary report
function generate_summary {
    echo -e "${BLUE}Generating test summary report...${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        python3 "$SCRIPT_DIR/utils/generate_summary.py" \
            --junit-dir "$JUNIT_DIR" \
            --output-file "$REPORT_DIR/summary.html"
    else
        echo "Would run: python3 $SCRIPT_DIR/utils/generate_summary.py"
    fi
}

# Cleanup test environment
function cleanup_environment {
    echo -e "${BLUE}Cleaning up test environment...${NC}"
    
    # Deactivate Python virtual environment
    deactivate 2>/dev/null || true
    
    # Remove temporary files
    find "$SCRIPT_DIR" -name "*.pyc" -delete
    find "$SCRIPT_DIR" -name "__pycache__" -delete
}

# Main execution
echo -e "${YELLOW}=============================================${NC}"
echo -e "${YELLOW}  ATTINY Control System Integration Tests   ${NC}"
echo -e "${YELLOW}=============================================${NC}"
echo ""
echo -e "${BLUE}Configuration:${NC}"
echo -e "  Test Category: ${GREEN}$TEST_CATEGORY${NC}"
echo -e "  Verbose Mode: ${GREEN}$VERBOSE${NC}"
echo -e "  Dry Run: ${GREEN}$DRY_RUN${NC}"
echo ""

# Create directories
create_directories

# Setup environment
setup_environment

# Run tests based on category
case "$TEST_CATEGORY" in
    firmware)
        run_firmware_tests
        ;;
    cloud)
        run_cloud_tests
        ;;
    monitoring)
        run_monitoring_tests
        ;;
    workflow)
        run_workflow_tests
        ;;
    all)
        run_firmware_tests
        run_cloud_tests
        run_monitoring_tests
        run_workflow_tests
        ;;
esac

# Generate summary report
if [ "$TEST_CATEGORY" = "all" ]; then
    generate_summary
fi

# Cleanup
cleanup_environment

echo ""
echo -e "${YELLOW}=============================================${NC}"
echo -e "${GREEN}  Integration tests completed successfully!  ${NC}"
echo -e "${YELLOW}=============================================${NC}"
echo ""
echo -e "${BLUE}Results available at:${NC}"
echo -e "  Logs: ${GREEN}$LOG_DIR${NC}"
echo -e "  Reports: ${GREEN}$REPORT_DIR${NC}"
echo -e "  JUnit XML: ${GREEN}$JUNIT_DIR${NC}"
echo "" 