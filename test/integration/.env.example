# Google Cloud Platform
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json
GCP_PROJECT_ID=cannasol-automation-suite
GCP_REGION=us-central1

# SimulAVR Configuration
SIMULAVR_PATH=/usr/local/bin/simulavr
SIMULAVR_MODEL=attiny85

# Test Environment
TEST_ENVIRONMENT=integration
CI_ENABLED=false

# Test Cloud Storage
TEST_BUCKET_NAME=attiny-test-firmware
TEST_FIRMWARE_PATH=test/firmware/attiny85.hex

# Test Device Configuration
TEST_DEVICE_ID=test-device-001
TEST_FIRMWARE_VERSION=1.0.0-test

# Test Monitoring
TEST_ALERT_POLICY=build-failure
TEST_NOTIFICATION_CHANNEL=email

# Authentication for APIs (if required)
API_KEY=your-api-key-here

# Timeouts and Retries
TEST_TIMEOUT_SECONDS=300
TEST_MAX_RETRIES=3

# Logging Level
LOG_LEVEL=DEBUG 