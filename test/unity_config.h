/**
 * @file unity_config.h
 * @brief Custom Unity framework configuration
 * 
 * This file provides configuration settings for the Unity test framework
 * to work properly with our ATTINY85 Control System, in both native
 * and emulated environments.
 */

#ifndef UNITY_CONFIG_H
#define UNITY_CONFIG_H

/* Include standard headers */
#include <stdio.h>
#include <stddef.h>

/* Define Unity configuration options */
#define UNITY_INCLUDE_DOUBLE
#define UNITY_DOUBLE_PRECISION 0.0001
#define UNITY_SUPPORT_TEST_CASES

/* Define output functions */
#define UNITY_OUTPUT_CHAR(c) putchar(c)
#define UNITY_OUTPUT_FLUSH() fflush(stdout)
#define UNITY_OUTPUT_START() 
#define UNITY_OUTPUT_COMPLETE() 

/* We want colors in our test output */
#define UNITY_OUTPUT_COLOR

#ifdef EMULATION_MODE
    /* When running in emulation mode with simulavr */
    void unity_emulator_init(void);
    void unity_emulator_cleanup(void);
    #define UNITY_SETUP_HOOK unity_emulator_init()
    #define UNITY_TEARDOWN_HOOK unity_emulator_cleanup()
#elif defined(NATIVE_TEST)
    /* When running in native test mode */
    /* Using standard Unity behavior for native tests */
#elif defined(ATTINY85) && !defined(EMULATION_MODE)
    /* When running on actual hardware */
    void serial_putchar(char c);
    #define UNITY_OUTPUT_CHAR(c) serial_putchar(c)
    
    /* Define hardware init and cleanup */
    void unity_hw_init(void);
    void unity_hw_cleanup(void);
    #define UNITY_SETUP_HOOK unity_hw_init()
    #define UNITY_TEARDOWN_HOOK unity_hw_cleanup()
#endif

/* Custom test state management */
#ifdef EMULATION_MODE
    /* For SimulAVR emulator */
    #include "mocks/ATTINY85_emulator.h"
    
    /* Implementation of Unity callbacks for emulator mode */
    void unity_output_char(char c) {
        /* Output to SimulAVR's terminal interface */
        putchar(c);
    }
    
    void unity_emulator_init(void) {
        /* Initialize the emulator */
        EmulatorUtils::initEmulator();
    }
    
    void unity_emulator_cleanup(void) {
        /* Clean up emulator state */
        /* No action needed as SimulAVR handles this */
    }
#elif defined(ATTINY85) && !defined(EMULATION_MODE)
    /* Implementation for hardware testing */
    #include <Arduino.h>
    
    void serial_putchar(char c) {
        /* Software serial implementation would go here */
        /* This is a placeholder as ATtiny85 doesn't have hardware UART */
    }
    
    void unity_hw_init(void) {
        /* Initialize hardware for testing */
        /* This would set up pin modes, disable interrupts, etc. */
    }
    
    void unity_hw_cleanup(void) {
        /* Clean up hardware state after testing */
        /* This would restore default pin states, re-enable interrupts, etc. */
    }
#endif

#endif /* UNITY_CONFIG_H */ 