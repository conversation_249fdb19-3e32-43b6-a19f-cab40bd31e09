/**
 * @file test_basic.cpp
 * @brief Basic tests to verify the test framework
 */

#include <unity.h>

void setUp(void) {
    // Set up code
}

void tearDown(void) {
    // Clean up code
}

void test_basic_addition(void) {
    TEST_ASSERT_EQUAL(4, 2 + 2);
}

void test_basic_subtraction(void) {
    TEST_ASSERT_EQUAL(2, 4 - 2);
}

int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_basic_addition);
    RUN_TEST(test_basic_subtraction);
    
    return UNITY_END();
} 