/**
 * @file air_control_mock.cpp
 * @brief Mock implementation of air control functions for testing
 */

#include "modules/air_control.h"
#include <stdio.h>

// Global state to track if air is on
static bool air_is_on = false;

/**
 * @brief Turn on the air
 */
void air_turnOn() {
    air_is_on = true;
}

/**
 * @brief Turn off the air
 */
void air_turnOff() {
    air_is_on = false;
}

/**
 * @brief Check if air is running
 * @return True if air is on, false otherwise
 */
bool isAirRunning() {
    return air_is_on;
}

/**
 * @brief Turn off the air from an interrupt service routine
 * This is a critical function that is called from timer ISR
 */
void air_turnOff_from_ISR() {
    air_is_on = false;
    // Additional debug output if needed
    #ifdef DEBUG_MODE
    printf("Air turned off from ISR\n");
    #endif
}

/**
 * @brief Reset the air control state for testing
 */
void air_resetState() {
    air_is_on = false;
} 