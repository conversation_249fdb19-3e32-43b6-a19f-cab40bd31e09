/**
 * @file EEPROM.h
 * @brief Mock EEPROM interface for testing
 */

#ifndef EEPROM_MOCK_H
#define EEPROM_MOCK_H

#include <stdint.h>
#include <string.h>

/**
 * @class EEPROMClass
 * @brief Mock implementation of the Arduino EEPROM library for unit testing
 */
class EEPROMClass {
public:
    /**
     * @brief Read a byte from the EEPROM
     * @param address The address to read from
     * @return The byte value at the given address
     */
    uint8_t read(int address) const {
        if (address >= 0 && address < 512) {
            return eepromData[address];
        }
        return 0;
    }
    
    /**
     * @brief Write a byte to the EEPROM
     * @param address The address to write to
     * @param value The value to write
     */
    void write(int address, uint8_t value) {
        if (address >= 0 && address < 512) {
            eepromData[address] = value;
        }
    }
    
    /**
     * @brief Reset the EEPROM to zeros
     */
    void reset() {
        memset(eepromData, 0, sizeof(eepromData));
    }
    
private:
    uint8_t eepromData[512] = {0};  // 512 bytes of mock EEPROM
};

// Single instance of the EEPROM class
extern EEPROMClass EEPROM;

#endif // EEPROM_MOCK_H 