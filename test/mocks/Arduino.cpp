/**
 * @file Arduino.cpp
 * @brief Implementation of mock Arduino functions for unit testing
 */

#include "Arduino.h"
#include <stdint.h>
#include <string.h>

// Register variables
volatile uint8_t DDRB = 0;
volatile uint8_t PORTB = 0;
volatile uint8_t PINB = 0;

// Initialize static members of ArduinoMock
uint8_t ArduinoMock::TCCR0A_value = 0;
uint8_t ArduinoMock::TCCR0B_value = 0;
uint8_t ArduinoMock::TCCR1_value = 0;
uint8_t ArduinoMock::TIMSK_value = 0;
uint8_t ArduinoMock::GTCCR_value = 0;
uint8_t ArduinoMock::OCR0A_value = 0;
uint8_t ArduinoMock::OCR0B_value = 0;
uint8_t ArduinoMock::TCNT0_value = 0;
uint8_t ArduinoMock::TCNT1_value = 0;

uint8_t ArduinoMock::pinModes[20] = {0};
uint8_t ArduinoMock::pinValues[20] = {0};
uint8_t ArduinoMock::pwmValues[20] = {0};
unsigned long ArduinoMock::pulseHighTime[20] = {0};
unsigned long ArduinoMock::pulseLowTime[20] = {0};
unsigned long ArduinoMock::currentTime = 0;

bool ArduinoMock::digitalWriteCalled = false;
bool ArduinoMock::digitalReadCalled = false;
bool ArduinoMock::pinModeCalled = false;
bool ArduinoMock::analogWriteCalled = false;

// Module initialization tracking flags
bool pinConfigInitCalled = false;
bool pwmControlInitCalled = false;
bool timerManagerInitCalled = false;
bool airControlInitCalled = false;
bool pumpControlInitCalled = false;

// Arduino API implementation

void pinMode(uint8_t pin, uint8_t mode) {
    if (pin < 20) {
        ArduinoMock::pinModes[pin] = mode;
        ArduinoMock::pinModeCalled = true;
    }
}

void digitalWrite(uint8_t pin, uint8_t val) {
    if (pin < 20) {
        ArduinoMock::pinValues[pin] = val;
        ArduinoMock::digitalWriteCalled = true;
    }
}

int digitalRead(uint8_t pin) {
    ArduinoMock::digitalReadCalled = true;
    if (pin < 20) {
        return ArduinoMock::pinValues[pin];
    }
    return LOW;
}

void analogWrite(uint8_t pin, uint8_t val) {
    if (pin < 20) {
        ArduinoMock::pwmValues[pin] = val;
        ArduinoMock::analogWriteCalled = true;
    }
}

unsigned long millis(void) {
    return ArduinoMock::currentTime;
}

void delay(unsigned long ms) {
    ArduinoMock::currentTime += ms;
}

unsigned long pulseIn(uint8_t pin, uint8_t value, unsigned long timeout) {
    if (pin < 20) {
        return (value == HIGH) ? ArduinoMock::pulseHighTime[pin] : ArduinoMock::pulseLowTime[pin];
    }
    return 0;
}

void cli(void) {
    // Just stub for testing
}

void sei(void) {
    // Just stub for testing
} 