/**
 * @file EEPROM.cpp
 * @brief Implementation of mock EEPROM for unit testing
 */

#include "EEPROM.h"
#include <string.h>

// Create single instance of the EEPROM class
EEPROMClass EEPROM;

uint8_t EEPROMClass::read(int address) {
    if (address < 512) {
        return defaultEepromData[address];
    }
    return 0;
}

void EEPROMClass::write(int address, uint8_t val) {
    if (address < 512) {
        defaultEepromData[address] = val;
    }
}

void EEPROMClass::update(int address, uint8_t val) {
    if (address < 512 && defaultEepromData[address] != val) {
        defaultEepromData[address] = val;
    }
} 