/**
 * @file ATTINY85_emulator.h
 * @brief Emulator configuration for ATTINY85 in SimulAVR environment
 */

#ifndef ATTINY85_EMULATOR_H
#define ATTINY85_EMULATOR_H

#include <Arduino.h>

// Define specific ATTINY85 registers for emulation
#ifndef EMULATION_MODE
#define EMULATION_MODE 1
#endif

// IO pins mapping for emulator
#define EMULATOR_PIN_PB0 10 // Emulator mapping for physical pin PB0
#define EMULATOR_PIN_PB1 11 // Emulator mapping for physical pin PB1
#define EMULATOR_PIN_PB2 12 // Emulator mapping for physical pin PB2
#define EMULATOR_PIN_PB3 13 // Emulator mapping for physical pin PB3
#define EMULATOR_PIN_PB4 14 // Emulator mapping for physical pin PB4
#define EMULATOR_PIN_PB5 15 // Emulator mapping for physical pin PB5

// Define functions to simulate hardware behavior
namespace EmulatorUtils {
    /**
     * @brief Initialize the emulator environment
     */
    void initEmulator();
    
    /**
     * @brief Generate a PWM signal for testing
     * @param pin The pin to generate the signal on
     * @param dutyCycle The duty cycle (0-100)
     */
    void generatePwmSignal(uint8_t pin, uint8_t dutyCycle);
    
    /**
     * @brief Simulate a pin change
     * @param pin The pin to change
     * @param value The value to set (HIGH or LOW)
     */
    void setPinValue(uint8_t pin, uint8_t value);
    
    /**
     * @brief Fast-forward the emulator clock
     * @param milliseconds The number of milliseconds to advance
     */
    void advanceTime(unsigned long milliseconds);
    
    /**
     * @brief Trigger an interrupt
     * @param interruptNum The interrupt number to trigger
     */
    void triggerInterrupt(uint8_t interruptNum);
    
    /**
     * @brief Read a pin value directly from emulator
     * @param pin The pin to read
     * @return The pin value (HIGH or LOW)
     */
    uint8_t readPinValue(uint8_t pin);
}

#endif // ATTINY85_EMULATOR_H 