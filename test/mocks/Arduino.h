/**
 * @file Arduino.h
 * @brief Mock Arduino header for unit testing
 */

#ifndef ARDUINO_MOCK_H
#define ARDUINO_MOCK_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>

// Arduino pin modes
#define INPUT 0x0
#define OUTPUT 0x1
#define INPUT_PULLUP 0x2

// Arduino pin values
#define LOW 0x0
#define HIGH 0x1

// Forward declare register variables - actual values are defined in Arduino.cpp
extern volatile uint8_t DDRB;
extern volatile uint8_t PORTB;
extern volatile uint8_t PINB;

// C++ Mock implementation
#ifdef __cplusplus
class ArduinoMock {
public:
    // Register values
    static uint8_t TCCR0A_value;
    static uint8_t TCCR0B_value;
    static uint8_t TCCR1_value;
    static uint8_t TIMSK_value;
    static uint8_t GTCCR_value;
    static uint8_t OCR0A_value;
    static uint8_t OCR0B_value;
    static uint8_t TCNT0_value;
    static uint8_t TCNT1_value;
    
    // State tracking
    static uint8_t pinModes[20];
    static uint8_t pinValues[20];
    static uint8_t pwmValues[20];
    static unsigned long pulseHighTime[20];
    static unsigned long pulseLowTime[20];
    static unsigned long currentTime;
    
    // Function call tracking
    static bool digitalWriteCalled;
    static bool digitalReadCalled;
    static bool pinModeCalled;
    static bool analogWriteCalled;
    
    // Methods to set up test conditions
    static void reset() {
        // Reset all state 
        memset(pinModes, 0, sizeof(pinModes));
        memset(pinValues, 0, sizeof(pinValues));
        memset(pwmValues, 0, sizeof(pwmValues));
        memset(pulseHighTime, 0, sizeof(pulseHighTime));
        memset(pulseLowTime, 0, sizeof(pulseLowTime));
        currentTime = 0;
        
        // Reset call tracking
        digitalWriteCalled = false;
        digitalReadCalled = false;
        pinModeCalled = false;
        analogWriteCalled = false;
        
        // Reset registers
        DDRB = 0;
        PORTB = 0;
        PINB = 0;
        TCCR0A_value = 0;
        TCCR0B_value = 0;
        TCCR1_value = 0;
        TIMSK_value = 0;
        GTCCR_value = 0;
        OCR0A_value = 0;
        OCR0B_value = 0;
        TCNT0_value = 0;
        TCNT1_value = 0;
    }
    
    static void setPinMode(uint8_t pin, uint8_t mode) {
        if (pin < 20) pinModes[pin] = mode;
    }
    
    static void setDigitalPinValue(uint8_t pin, uint8_t value) {
        if (pin < 20) pinValues[pin] = value;
    }
    
    static void setPWMValue(uint8_t pin, uint8_t value) {
        if (pin < 20) pwmValues[pin] = value;
    }
    
    static void setPulseValues(uint8_t pin, unsigned long highTime, unsigned long lowTime) {
        if (pin < 20) {
            pulseHighTime[pin] = highTime;
            pulseLowTime[pin] = lowTime;
        }
    }
    
    static void setTime(unsigned long time) {
        currentTime = time;
    }
    
    static void advanceTime(unsigned long ms) {
        currentTime += ms;
    }
};

// Module initialization tracking flags
extern bool pinConfigInitCalled;
extern bool pwmControlInitCalled;
extern bool timerManagerInitCalled;
extern bool airControlInitCalled;
extern bool pumpControlInitCalled;

#endif // __cplusplus

// Arduino API function declarations
#ifdef __cplusplus
extern "C" {
#endif

void pinMode(uint8_t pin, uint8_t mode);
void digitalWrite(uint8_t pin, uint8_t val);
int digitalRead(uint8_t pin);
void analogWrite(uint8_t pin, uint8_t val);
unsigned long millis(void);
void delay(unsigned long ms);
unsigned long pulseIn(uint8_t pin, uint8_t value, unsigned long timeout);
void cli(void);
void sei(void);

#ifdef __cplusplus
}
#endif

#endif // ARDUINO_MOCK_H 