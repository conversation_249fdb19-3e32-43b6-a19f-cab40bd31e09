#!/bin/bash

# Run Unit Tests
# This script runs all unit tests for the ATTINY85 control project

echo "========================================"
echo "Running ATTINY85 Control System Unit Tests"
echo "========================================"

# Navigate to the project root directory
PROJECT_ROOT=$(realpath $(dirname $(dirname $0)))
cd $PROJECT_ROOT

# Ensure the test build directory exists
mkdir -p test/build

# Compile and run each test
compile_and_run_test() {
    test_file=$1
    test_name=$(basename $test_file .cpp)
    echo "Running test: $test_name"
    
    g++ -std=c++11 -o test/build/$test_name \
        -I. \
        -Isrc \
        -Itest \
        -Itest/unity \
        -DNATIVE_TEST \
        -DUNITY_OUTPUT_COLOR \
        $test_file \
        test/mocks/Arduino.cpp \
        test/unity/unity.c
    
    if [ $? -eq 0 ]; then
        echo "Compilation successful, running test..."
        test/build/$test_name
        if [ $? -eq 0 ]; then
            echo "✅ Test passed: $test_name"
            return 0
        else
            echo "❌ Test failed: $test_name"
            return 1
        fi
    else
        echo "❌ Compilation failed: $test_name"
        return 1
    fi
}

# Run a specific test if specified
if [ "$1" != "" ]; then
    test_file="test/unit/$1.cpp"
    if [ -f "$test_file" ]; then
        compile_and_run_test $test_file
        if [ $? -eq 0 ]; then
            echo "✅ Test passed: $1"
            exit 0
        else
            echo "❌ Test failed: $1"
            exit 1
        fi
    else
        echo "❌ Test file not found: $test_file"
        exit 1
    fi
    exit
fi

# Run all tests in the unit directory
failed_tests=0
total_tests=0

for test_file in test/unit/test_*.cpp; do
    total_tests=$((total_tests + 1))
    compile_and_run_test $test_file
    if [ $? -ne 0 ]; then
        failed_tests=$((failed_tests + 1))
    fi
    echo "----------------------------------------"
done

# Display test results summary
echo "========================================"
echo "Test Results: $((total_tests - failed_tests))/$total_tests tests passed"

if [ $failed_tests -eq 0 ]; then
    echo "✅ All tests passed!"
    exit 0
else
    echo "❌ $failed_tests tests failed."
    exit 1
fi 