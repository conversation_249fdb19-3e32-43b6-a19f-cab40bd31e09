/*
 * ATtiny85 Connection Test
 * 
 * This sketch attempts to read the device signature of an ATtiny85
 * connected to an Arduino running as ISP. It can help diagnose
 * connection issues.
 * 
 * Connections:
 * - Arduino D13 -> ATtiny pin 7 (SCK)
 * - Arduino D12 -> ATtiny pin 6 (MISO)
 * - Arduino D11 -> ATtiny pin 5 (MOSI)
 * - Arduino D10 -> ATtiny pin 1 (RESET)
 * - Arduino 5V  -> ATtiny pin 8 (VCC)
 * - Ard<PERSON>o GND -> ATtiny pin 4 (GND)
 * 
 * Optional: 10μF capacitor between RESET and GND on Arduino (+ to RESET)
 */

#include <SPI.h>

// Pins definition
#define RESET 10  // Reset pin connected to ATtiny85 reset

// Commands for programming
#define CMD_PROGRAM_ENABLE     0xAC
#define CMD_READ_SIGNATURE     0x30
#define CMD_READ_FUSE          0x50

// Expected signature for ATtiny85
const byte ATTINY85_SIGNATURE[3] = {0x1E, 0x93, 0x0B};

void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  while (!Serial) {
    ; // wait for serial port to connect
  }
  
  Serial.println("\nATtiny85 Connection Test");
  Serial.println("========================");
  
  // Setup SPI pins
  pinMode(RESET, OUTPUT);
  digitalWrite(RESET, HIGH);  // Not in reset state
  
  // Initialize SPI
  SPI.begin();
  SPI.setClockDivider(SPI_CLOCK_DIV128);  // Slow down SPI clock for reliable communication
  
  delay(50);  // Give time to settle
  
  // Try to read the ATtiny85 signature
  Serial.println("Attempting to read ATtiny85 device signature...");
  
  byte sig[3];
  if (readSignature(sig)) {
    Serial.println("Successfully communicated with the ATtiny85!");
    Serial.print("Device signature: ");
    for (int i = 0; i < 3; i++) {
      Serial.print("0x");
      if (sig[i] < 0x10) Serial.print("0");
      Serial.print(sig[i], HEX);
      Serial.print(" ");
    }
    Serial.println();
    
    // Check if signature matches expected ATtiny85 signature
    if (sig[0] == ATTINY85_SIGNATURE[0] && 
        sig[1] == ATTINY85_SIGNATURE[1] && 
        sig[2] == ATTINY85_SIGNATURE[2]) {
      Serial.println("Signature matches ATtiny85!");
    } else {
      Serial.println("Signature does not match ATtiny85!");
      Serial.println("Expected: 0x1E 0x93 0x0B");
    }
  } else {
    Serial.println("Failed to communicate with ATtiny85!");
    Serial.println("Check your connections and try again.");
    Serial.println("Suggestions:");
    Serial.println("1. Ensure all wiring is correct and secure");
    Serial.println("2. Make sure ATtiny85 is properly powered");
    Serial.println("3. Try adding a 10μF capacitor between RESET and GND on Arduino");
    Serial.println("4. Try pressing the reset button on Arduino just before running this test");
  }
}

void loop() {
  // Nothing to do here
}

// Function to enter programming mode
bool enterProgrammingMode() {
  // Enter programming mode
  digitalWrite(RESET, LOW);  // Put ATtiny in reset state
  delay(20);  // Wait for stable reset
  
  // Send programming enable command
  SPI.transfer(CMD_PROGRAM_ENABLE);
  SPI.transfer(0x53);  // Second byte is always 0x53
  byte response = SPI.transfer(0x00);  // Third byte is don't care
  SPI.transfer(0x00);  // Fourth byte is don't care
  
  return (response == 0x53);  // Check if we got the expected response
}

// Function to read device signature
bool readSignature(byte *sig) {
  if (!enterProgrammingMode()) {
    return false;  // Failed to enter programming mode
  }
  
  // Read the three signature bytes
  for (int i = 0; i < 3; i++) {
    SPI.transfer(CMD_READ_SIGNATURE);
    SPI.transfer(0x00);  // Don't care
    SPI.transfer(i);     // Signature byte address
    sig[i] = SPI.transfer(0x00);  // Read signature byte
  }
  
  // Exit programming mode
  digitalWrite(RESET, HIGH);
  
  return true;
} 