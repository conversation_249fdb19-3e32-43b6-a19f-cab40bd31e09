# Google Cloud Integration for ATTINY Control System

This directory contains the necessary code and configuration for integrating the ATTINY Control System with Google Cloud Platform.

## Components

- **Firmware Storage**: Cloud Storage for firmware versions
- **Version Tracking**: Cloud Function to track firmware uploads
- **Firmware Management**: Web application for managing firmware versions
- **Firmware Analytics**: Analytics for firmware downloads and usage

## Setup

1. **Prerequisites**:
   - Google Cloud SDK installed locally
   - Access to Google Cloud project `cannasol-automation-suite`
   - `gcloud` authenticated with appropriate permissions

2. **Environment Setup**:
   ```bash
   # Run the setup script to configure Google Cloud resources
   ./setup.sh
   ```

3. **Deploy Cloud Functions**:
   ```bash
   # Deploy firmware tracker function
   cd firmware-tracker
   gcloud functions deploy firmware-tracker \
     --runtime nodejs14 \
     --trigger-resource cannasol-firmware \
     --trigger-event google.storage.object.finalize \
     --entry-point trackFirmwareUpload

   # Deploy firmware analytics function
   cd ../firmware-analytics
   gcloud functions deploy track-firmware-download \
     --runtime nodejs14 \
     --trigger-http \
     --allow-unauthenticated \
     --entry-point trackFirmwareDownload
   ```

4. **Deploy Firmware Management Web App**:
   ```bash
   cd ../firmware-manager
   npm install
   gcloud app deploy
   ```

## Access Control

- **Storage Admin**: Required for firmware uploads and management
- **Firestore Access**: Required for tracking firmware metadata
- **BigQuery Access**: Required for firmware analytics
- **Cloud Functions Deployer**: Required for deploying Cloud Functions
- **App Engine Deployer**: Required for deploying web application

## Firmware Lifecycle

1. **Build Process**: 
   - CI/CD pipeline builds firmware on push or tag
   - Compiled firmware is uploaded to Cloud Storage

2. **Version Tracking**:
   - Cloud Function detects new firmware in Cloud Storage
   - Metadata is stored in Firestore

3. **Deployment**:
   - Web interface allows promoting a version to production
   - Firmware is copied to a "production" path in Cloud Storage

4. **Monitoring**:
   - Download tracking captures usage statistics
   - BigQuery enables analytics on firmware usage

## Integration with CI/CD

- **GitHub Actions**: Integrated with Google Cloud Build
- **Cloud Build**: Automated build and deploy process
- **Service Account**: `<EMAIL>` 