<!-- cloud/firmware-manager/views/index.ejs -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ATTINY Control System - Firmware Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      padding-top: 20px;
      padding-bottom: 20px;
    }
    .firmware-card {
      margin-bottom: 20px;
    }
    .production-label {
      background-color: #198754;
      color: white;
      padding: 2px 8px;
      border-radius: 4px;
      margin-left: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="mb-4">
      <h1>ATTINY Control System</h1>
      <h2>Firmware Management</h2>
    </header>

    <% if (locals.success) { %>
      <div class="alert alert-success alert-dismissible fade show" role="alert">
        <%= success %>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    <% } %>

    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h3>Firmware Versions</h3>
          </div>
          <div class="card-body">
            <% if (versions && versions.length > 0) { %>
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Version</th>
                    <th>Upload Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <% versions.forEach(firmware => { %>
                    <tr>
                      <td>
                        <%= firmware.version %>
                        <% if (firmware.isProduction) { %>
                          <span class="production-label">Production</span>
                        <% } %>
                      </td>
                      <td><%= new Date(firmware.uploadedAt.toDate()).toLocaleString() %></td>
                      <td>
                        <a href="/download/<%= firmware.version %>" class="btn btn-primary btn-sm">Download</a>
                        <form method="post" action="/set-production" class="d-inline">
                          <input type="hidden" name="version" value="<%= firmware.version %>">
                          <button type="submit" class="btn btn-success btn-sm">Set as Production</button>
                        </form>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            <% } else { %>
              <p>No firmware versions found.</p>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 