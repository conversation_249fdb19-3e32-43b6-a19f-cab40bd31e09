// cloud/firmware-manager/app.js
const express = require('express');
const {Firestore} = require('@google-cloud/firestore');
const {Storage} = require('@google-cloud/storage');

const app = express();
const firestore = new Firestore();
const storage = new Storage();
const firmwareBucket = storage.bucket('cannasol-firmware');

app.set('view engine', 'ejs');
app.use(express.static('public'));
app.use(express.urlencoded({extended: true}));

// List all firmware versions
app.get('/', async (req, res) => {
  try {
    const snapshot = await firestore.collection('firmware')
      .where('filePath', '>=', 'attiny-control/')
      .orderBy('filePath', 'desc')
      .get();
    
    const versions = [];
    snapshot.forEach(doc => {
      versions.push(doc.data());
    });
    
    res.render('index', {versions});
  } catch (error) {
    res.status(500).send(`Error: ${error.message}`);
  }
});

// Set a version as current production version
app.post('/set-production', async (req, res) => {
  const {version} = req.body;
  if (!version) {
    return res.status(400).send('Version is required');
  }
  
  try {
    // Copy the specified version to production
    const sourcePath = `attiny-control/firmware-${version}.hex`;
    const destPath = 'attiny-control/firmware-production.hex';
    
    await firmwareBucket.file(sourcePath).copy(firmwareBucket.file(destPath));
    
    // Update metadata in Firestore
    await firestore.collection('firmware-deployment').doc('production').set({
      version,
      deployedAt: new Date(),
      deployedBy: req.user?.email || 'unknown',
      sourcePath
    });
    
    res.redirect('/?success=Version set as production');
  } catch (error) {
    res.status(500).send(`Error: ${error.message}`);
  }
});

// Download a specific firmware version
app.get('/download/:version', async (req, res) => {
  const {version} = req.params;
  
  try {
    const filePath = `attiny-control/firmware-${version}.hex`;
    const file = firmwareBucket.file(filePath);
    
    // Check if file exists
    const [exists] = await file.exists();
    if (!exists) {
      return res.status(404).send('Firmware version not found');
    }
    
    // Set response headers
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="attiny-firmware-${version}.hex"`);
    
    // Stream the file
    file.createReadStream()
      .on('error', (error) => {
        console.error('Error streaming file:', error);
        res.status(500).send('Error downloading firmware');
      })
      .pipe(res);
    
  } catch (error) {
    res.status(500).send(`Error: ${error.message}`);
  }
});

const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`Firmware management app listening on port ${PORT}`);
}); 