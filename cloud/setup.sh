#!/bin/bash
# cloud/setup.sh
# Script to configure Google Cloud environment for ATTINY control system

# Exit on error
set -e

# Configuration
PROJECT_ID="cannasol-automation-suite"
SERVICE_ACCOUNT="attiny-automation"
BUCKET_NAME="cannasol-firmware"
REGION="us-central1"

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up Google Cloud environment for ATTINY Control System...${NC}"

# Ensure gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" > /dev/null 2>&1; then
  echo -e "${RED}Error: No active gcloud account found. Please run 'gcloud auth login' first.${NC}"
  exit 1
fi

# Set default project
echo -e "${GREEN}Setting default project to ${PROJECT_ID}...${NC}"
gcloud config set project ${PROJECT_ID}

# Create service account for automation
echo -e "${GREEN}Creating service account for automation...${NC}"
if ! gcloud iam service-accounts describe ${SERVICE_ACCOUNT}@${PROJECT_ID}.iam.gserviceaccount.com > /dev/null 2>&1; then
  gcloud iam service-accounts create ${SERVICE_ACCOUNT} \
    --display-name="ATTINY Automation Service Account"
else
  echo -e "${YELLOW}Service account already exists, skipping creation.${NC}"
fi

# Assign required roles
echo -e "${GREEN}Assigning required roles to service account...${NC}"
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SERVICE_ACCOUNT}@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/storage.admin"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SERVICE_ACCOUNT}@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/cloudbuild.builds.editor"

# Create firmware storage bucket
echo -e "${GREEN}Creating firmware storage bucket...${NC}"
if ! gsutil ls -p ${PROJECT_ID} gs://${BUCKET_NAME} > /dev/null 2>&1; then
  gsutil mb -p ${PROJECT_ID} -l ${REGION} gs://${BUCKET_NAME}
else
  echo -e "${YELLOW}Bucket already exists, skipping creation.${NC}"
fi

# Set lifecycle policy for version retention
echo -e "${GREEN}Setting bucket lifecycle policy...${NC}"
cat > /tmp/lifecycle.json << EOF
{
  "rule": [
    {
      "action": {"type": "Delete"},
      "condition": {
        "age": 90,
        "isLive": true,
        "matchesPrefix": ["dev/"]
      }
    }
  ]
}
EOF

gsutil lifecycle set /tmp/lifecycle.json gs://${BUCKET_NAME}

# Set object versioning
echo -e "${GREEN}Enabling object versioning for bucket...${NC}"
gsutil versioning set on gs://${BUCKET_NAME}

# Enable required APIs
echo -e "${GREEN}Enabling required Google Cloud APIs...${NC}"
gcloud services enable \
  cloudbuild.googleapis.com \
  cloudfunctions.googleapis.com \
  firestore.googleapis.com \
  storage.googleapis.com \
  appengine.googleapis.com \
  bigquery.googleapis.com

# Create a BigQuery dataset for firmware analytics
echo -e "${GREEN}Creating BigQuery dataset for firmware analytics...${NC}"
if ! bq ls --project_id=${PROJECT_ID} | grep -q firmware_analytics; then
  bq --location=${REGION} mk \
    --dataset \
    --description="ATTINY Firmware Analytics" \
    ${PROJECT_ID}:firmware_analytics
else
  echo -e "${YELLOW}BigQuery dataset already exists, skipping creation.${NC}"
fi

# Create a table for firmware downloads
echo -e "${GREEN}Creating BigQuery table for firmware downloads...${NC}"
if ! bq ls --project_id=${PROJECT_ID} firmware_analytics | grep -q downloads; then
  bq mk \
    --table \
    --description="Firmware Download Analytics" \
    ${PROJECT_ID}:firmware_analytics.downloads \
    version:STRING,deviceId:STRING,timestamp:TIMESTAMP,userAgent:STRING,ipAddress:STRING
else
  echo -e "${YELLOW}BigQuery table already exists, skipping creation.${NC}"
fi

echo -e "${GREEN}Google Cloud environment setup complete.${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo -e "1. Deploy the Cloud Functions: cd cloud/firmware-tracker && gcloud functions deploy firmware-tracker"
echo -e "2. Deploy the firmware management app: cd cloud/firmware-manager && gcloud app deploy"
echo -e "3. Configure GitHub Actions integration with Google Cloud" 