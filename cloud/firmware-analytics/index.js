// cloud/firmware-analytics/index.js
const {BigQuery} = require(\"@google-cloud/bigquery\");
const bigquery = new BigQuery();

exports.trackFirmwareDownload = async (req, res) => {
  // Enable CORS
  res.set(\"Access-Control-Allow-Origin\", \"*\");
  
  if (req.method === \"OPTIONS\") {
    res.set(\"Access-Control-Allow-Methods\", \"GET\");
    res.set(\"Access-Control-Allow-Headers\", \"Content-Type\");
    res.status(204).send(\"\");
    return;
  }
  
  const {version, deviceId} = req.query;
  if (!version || !deviceId) {
    res.status(400).send(\"Missing required parameters\");
    return;
  }
  
  // Track download in BigQuery
  try {
    await bigquery.dataset(\"firmware_analytics\").table(\"downloads\").insert([
      {
        version,
        deviceId,
        timestamp: new Date().toISOString(),
        userAgent: req.headers[\"user-agent\"],
        ipAddress: req.ip
      }
    ]);
    
    res.status(200).send(\"Download tracked\");
  } catch (error) {
    console.error(\"Error tracking download:\", error);
    res.status(500).send(\"Error tracking download\");
  }
};
