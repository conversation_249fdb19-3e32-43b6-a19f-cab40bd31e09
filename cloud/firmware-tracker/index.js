const {Firestore} = require('@google-cloud/firestore');
const firestore = new Firestore();

exports.trackFirmwareUpload = async (data, context) => {
  const fileBucket = data.bucket;
  const filePath = data.name;
  
  // Only process firmware files
  if (!filePath.includes('attiny-control') || !filePath.endsWith('.hex')) {
    console.log(`Ignoring non-firmware file: ${filePath}`);
    return;
  }
  
  // Extract version from filename
  const versionMatch = filePath.match(/firmware-v?(\d+\.\d+\.\d+)\.hex$/);
  if (!versionMatch) {
    if (filePath.includes('firmware-latest.hex')) {
      console.log('Processing latest firmware file');
    } else {
      console.log('No version found in filename:', filePath);
      return;
    }
  }
  
  const version = versionMatch ? versionMatch[1] : 'latest';
  const timestamp = new Date();
  
  // Store firmware metadata in Firestore
  try {
    await firestore.collection('firmware').doc(`attiny-${version}`).set({
      version,
      filePath,
      bucket: fileBucket,
      uploadedAt: timestamp,
      fullPath: `gs://${fileBucket}/${filePath}`
    });
    
    console.log(`Tracked firmware version ${version} at ${timestamp}`);
  } catch (error) {
    console.error('Error tracking firmware:', error);
  }
}; 