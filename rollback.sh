#!/bin/bash
# ATTINY Control System Rollback Script
# This script handles the rollback of firmware deployments

# Exit on error
set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
VERSION=""
TARGET=""
ENVIRONMENT="production"
REGION=""
DEVICE_TYPE=""
DRY_RUN=false
FORCE=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ID="cannasol-automation-suite"
GCP_REGION="us-central1"

# Load environment variables if .env file exists
if [ -f "$SCRIPT_DIR/.env" ]; then
    source "$SCRIPT_DIR/.env"
fi

function print_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --version <version>     Current version to roll back (required, format: v1.x.x)"
    echo "  --target <version>      Target version to roll back to (required, format: v1.x.x)"
    echo "  --environment <env>     Deployment environment: development, staging, production (default: production)"
    echo "  --region <region>       Limit rollback to specific region (optional)"
    echo "  --device-type <type>    Limit rollback to specific device type (optional)"
    echo "  --dry-run               Show what would be done without actually rolling back"
    echo "  --force                 Force rollback, bypassing confirmation prompts"
    echo "  --help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --version=v1.2.0 --target=v1.1.0"
    echo "  $0 --version=v1.2.0 --target=v1.1.0 --environment=staging"
    echo "  $0 --version=v1.2.0 --target=v1.1.0 --region=us-west --device-type=model-b"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    arg="$1"
    case $arg in
        --version=*)
            VERSION="${arg#*=}"
            shift
            ;;
        --version)
            VERSION="$2"
            shift 2
            ;;
        --target=*)
            TARGET="${arg#*=}"
            shift
            ;;
        --target)
            TARGET="$2"
            shift 2
            ;;
        --environment=*)
            ENVIRONMENT="${arg#*=}"
            shift
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --region=*)
            REGION="${arg#*=}"
            shift
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --device-type=*)
            DEVICE_TYPE="${arg#*=}"
            shift
            ;;
        --device-type)
            DEVICE_TYPE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Error: Unknown option: $arg${NC}"
            print_usage
            exit 1
            ;;
    esac
done

# Validate arguments
if [ -z "$VERSION" ] || [ -z "$TARGET" ]; then
    echo -e "${RED}Error: Both current version and target version are required${NC}"
    print_usage
    exit 1
fi

# Validate version formats
if ! [[ $VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "${RED}Error: Current version must be in format v1.x.x${NC}"
    exit 1
fi

if ! [[ $TARGET =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "${RED}Error: Target version must be in format v1.x.x${NC}"
    exit 1
fi

# Validate environment
if ! [[ "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    echo -e "${RED}Error: Environment must be one of: development, staging, production${NC}"
    exit 1
fi

# Define environment-specific settings
case "$ENVIRONMENT" in
    development)
        BUCKET_NAME="attiny-dev-firmware"
        ;;
    staging)
        BUCKET_NAME="attiny-staging-firmware"
        ;;
    production)
        BUCKET_NAME="attiny-prod-firmware"
        ;;
esac

# Display rollback configuration
echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}  ATTINY Control System Rollback${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""
echo -e "Current Version: ${RED}$VERSION${NC}"
echo -e "Target Version:  ${GREEN}$TARGET${NC}"
echo -e "Environment:     ${GREEN}$ENVIRONMENT${NC}"
echo -e "Bucket:          ${GREEN}$BUCKET_NAME${NC}"

if [ -n "$REGION" ]; then
    echo -e "Region:          ${GREEN}$REGION${NC}"
fi

if [ -n "$DEVICE_TYPE" ]; then
    echo -e "Device Type:     ${GREEN}$DEVICE_TYPE${NC}"
fi

if [ "$DRY_RUN" = true ]; then
    echo -e "${YELLOW}DRY RUN: No actual changes will be made${NC}"
fi

echo ""

# Check for Google Cloud SDK
if ! command -v gcloud >/dev/null 2>&1; then
    echo -e "${RED}Error: Google Cloud SDK (gcloud) is required but not installed.${NC}"
    echo "Please install from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check for authentication
if ! gcloud auth print-identity-token >/dev/null 2>&1; then
    echo -e "${RED}Error: Not authenticated with Google Cloud.${NC}"
    echo "Please run: gcloud auth login"
    exit 1
fi

# Check project access
if ! gcloud projects describe "$PROJECT_ID" >/dev/null 2>&1; then
    echo -e "${RED}Error: Cannot access project $PROJECT_ID.${NC}"
    echo "Please make sure you have the correct permissions."
    exit 1
fi

# Set default project
gcloud config set project "$PROJECT_ID"

# Verify that the target version exists
echo -e "${BLUE}Verifying target version...${NC}"

if [ "$DRY_RUN" = false ]; then
    # Check if target version exists in Cloud Storage
    if ! gsutil ls "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/$TARGET/attiny85.hex" &> /dev/null; then
        echo -e "${RED}Error: Target version $TARGET does not exist in $ENVIRONMENT.${NC}"
        echo "Please specify a valid target version that exists in Cloud Storage."
        exit 1
    fi
else
    echo -e "${YELLOW}(DRY RUN) Would verify target version exists in Cloud Storage${NC}"
fi

# Confirm rollback for production
if [ "$ENVIRONMENT" = "production" ] && [ "$FORCE" = false ]; then
    echo -e "${YELLOW}WARNING: You are about to roll back a PRODUCTION deployment!${NC}"
    echo "This will affect real devices in the field."
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        echo -e "${RED}Rollback aborted.${NC}"
        exit 1
    fi
    
    # Additional approval check for production
    echo -e "${BLUE}Checking rollback approvals...${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        # In a real implementation, this would check a database or API for approvals
        # For this example, we'll simulate approval by prompting for confirmation
        read -p "Has this rollback been approved by the Engineering Lead? (yes/no): " approved
        
        if [ "$approved" != "yes" ]; then
            echo -e "${RED}Rollback aborted. Engineering Lead approval required.${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}(DRY RUN) Would check for rollback approvals${NC}"
    fi
fi

# Create a rollback record
echo -e "${BLUE}Recording rollback event...${NC}"

if [ "$DRY_RUN" = false ]; then
    # Generate rollback metadata
    ROLLBACK_DATA="{
        \"from\": \"$VERSION\",
        \"to\": \"$TARGET\",
        \"environment\": \"$ENVIRONMENT\",
        \"timestamp\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",
        \"initiatedBy\": \"$(whoami)\""
    
    if [ -n "$REGION" ]; then
        ROLLBACK_DATA="$ROLLBACK_DATA, \"region\": \"$REGION\""
    fi
    
    if [ -n "$DEVICE_TYPE" ]; then
        ROLLBACK_DATA="$ROLLBACK_DATA, \"deviceType\": \"$DEVICE_TYPE\""
    fi
    
    ROLLBACK_DATA="$ROLLBACK_DATA, \"reason\": \"Manual rollback\""
    ROLLBACK_DATA="$ROLLBACK_DATA }"
    
    # Save rollback metadata to Cloud Storage
    echo "$ROLLBACK_DATA" > /tmp/rollback-record.json
    
    ROLLBACK_ID=$(date +%Y%m%d%H%M%S)
    
    if ! gsutil cp /tmp/rollback-record.json "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/rollbacks/rollback-$ROLLBACK_ID.json"; then
        echo -e "${YELLOW}Warning: Failed to record rollback metadata.${NC}"
        echo "Rollback will continue, but tracking information may be incomplete."
    fi
    
    rm /tmp/rollback-record.json
    
    # Update deployment record in Firestore
    echo -e "${BLUE}Updating deployment records in Firestore...${NC}"
    
    # Run Node.js script to record rollback
    DEPLOY_RECORD_SCRIPT="$SCRIPT_DIR/monitoring/tools/deployment-tracker.js"
    
    if [ -f "$DEPLOY_RECORD_SCRIPT" ]; then
        if ! node "$DEPLOY_RECORD_SCRIPT" \
            --version="$VERSION" \
            --environment="$ENVIRONMENT" \
            --status="rolled-back" \
            --replaced-by="$TARGET"; then
                
            echo -e "${YELLOW}Warning: Failed to update deployment record in Firestore.${NC}"
            echo "Rollback will continue, but tracking information may be incomplete."
        fi
    else
        echo -e "${YELLOW}Warning: Deployment tracking script not found at $DEPLOY_RECORD_SCRIPT${NC}"
    fi
else
    echo -e "${YELLOW}(DRY RUN) Would record rollback event in Cloud Storage${NC}"
    echo -e "${YELLOW}(DRY RUN) Would update deployment records in Firestore${NC}"
fi

# Set the target version as current
echo -e "${BLUE}Setting $TARGET as current for $ENVIRONMENT...${NC}"

if [ "$DRY_RUN" = false ]; then
    # Create current version file
    echo "$TARGET" > /tmp/current-version
    
    if ! gsutil cp /tmp/current-version "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/current-version"; then
        echo -e "${RED}Error: Failed to update current version marker.${NC}"
        echo "Rollback cannot continue without updating the current version."
        exit 1
    fi
    
    rm /tmp/current-version
else
    echo -e "${YELLOW}(DRY RUN) Would set $TARGET as current for $ENVIRONMENT${NC}"
fi

# Create or update rollout configuration for target version
echo -e "${BLUE}Configuring rollout settings for target version...${NC}"

if [ "$DRY_RUN" = false ]; then
    # Create rollout metadata file
    ROLLOUT_CONFIG="{
        \"version\": \"$TARGET\",
        \"environment\": \"$ENVIRONMENT\",
        \"canaryPercentage\": 0,
        \"rolloutPercentage\": 100,
        \"deployTime\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",
        \"deployedBy\": \"$(whoami)\",
        \"isRollback\": true,
        \"rolledBackFrom\": \"$VERSION\"
    }"
    
    # Save rollout configuration to Cloud Storage
    echo "$ROLLOUT_CONFIG" > /tmp/rollout-config.json
    
    if ! gsutil cp /tmp/rollout-config.json "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/$TARGET/rollout-config.json"; then
        echo -e "${YELLOW}Warning: Failed to update rollout configuration.${NC}"
        echo "Rollback will continue, but rollout settings may not be applied."
    fi
    
    rm /tmp/rollout-config.json
else
    echo -e "${YELLOW}(DRY RUN) Would configure immediate rollout (100%)${NC}"
fi

# Create device selection configuration if region or device type specified
if [ -n "$REGION" ] || [ -n "$DEVICE_TYPE" ]; then
    echo -e "${BLUE}Creating device selection filter...${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        # Create selection filter
        SELECTION_FILTER="{
            \"version\": \"$TARGET\",
            \"environment\": \"$ENVIRONMENT\",
            \"isRollback\": true"
        
        if [ -n "$REGION" ]; then
            SELECTION_FILTER="$SELECTION_FILTER, \"region\": \"$REGION\""
        fi
        
        if [ -n "$DEVICE_TYPE" ]; then
            SELECTION_FILTER="$SELECTION_FILTER, \"deviceType\": \"$DEVICE_TYPE\""
        fi
        
        SELECTION_FILTER="$SELECTION_FILTER }"
        
        # Save selection filter to Cloud Storage
        echo "$SELECTION_FILTER" > /tmp/device-selection.json
        
        if ! gsutil cp /tmp/device-selection.json "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/$TARGET/device-selection.json"; then
            echo -e "${YELLOW}Warning: Failed to create device selection filter.${NC}"
            echo "Rollback will continue, but device targeting may not be applied."
        fi
        
        rm /tmp/device-selection.json
    else
        echo -e "${YELLOW}(DRY RUN) Would create device selection filter${NC}"
    fi
fi

# Notify monitoring system of rollback
echo -e "${BLUE}Notifying monitoring system...${NC}"

if [ "$DRY_RUN" = false ]; then
    # In a real implementation, this would call an API or update a database
    # For this example, we'll simply create a notification record
    
    NOTIFICATION="{
        \"type\": \"rollback\",
        \"from\": \"$VERSION\",
        \"to\": \"$TARGET\",
        \"environment\": \"$ENVIRONMENT\",
        \"timestamp\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",
        \"initiatedBy\": \"$(whoami)\"
    }"
    
    echo "$NOTIFICATION" > /tmp/rollback-notification.json
    
    # In a real system, this would send to a monitoring endpoint
    # For now, we'll just store it in Cloud Storage
    if ! gsutil cp /tmp/rollback-notification.json "gs://$BUCKET_NAME/firmware/$ENVIRONMENT/notifications/rollback-$ROLLBACK_ID.json"; then
        echo -e "${YELLOW}Warning: Failed to send notification.${NC}"
    fi
    
    rm /tmp/rollback-notification.json
else
    echo -e "${YELLOW}(DRY RUN) Would notify monitoring system${NC}"
fi

# Rollback complete
echo ""
echo -e "${GREEN}=======================================${NC}"
echo -e "${GREEN}  Rollback completed successfully!  ${NC}"
echo -e "${GREEN}=======================================${NC}"
echo ""
echo -e "Rolled back from ${RED}$VERSION${NC} to ${GREEN}$TARGET${NC} in ${GREEN}$ENVIRONMENT${NC}"
echo ""

echo -e "${BLUE}Post-rollback steps:${NC}"
echo "1. Monitor the rollback in the dashboard"
echo "2. Verify devices are downgrading correctly"
echo "3. Document the rollback and root cause in the issue tracking system"
echo "4. Schedule a follow-up review to prevent similar issues"
echo ""

exit 0 