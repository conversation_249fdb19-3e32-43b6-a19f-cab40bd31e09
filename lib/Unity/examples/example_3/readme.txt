Example 3
=========

This example project gives an example of some passing, ignored, and failing tests.
It's simple and meant for you to look over and get an idea for what all of this stuff does.

You can build and test using rake. The rake version will let you test with gcc or a couple
versions of IAR.  You can tweak the yaml files to get those versions running.

Ruby is required if you're using the rake version (obviously).  This version shows off most of
Unity's advanced features (automatically creating test runners, fancy summaries, etc.)
Without ruby, you have to maintain your own test runners.  Do that for a while and you'll learn
why you really want to start using the Ruby tools.
