/* =========================================================================
    Unity - A Test Framework for C
    ThrowTheSwitch.org
    Copyright (c) 2007-25 <PERSON>, <PERSON>, & <PERSON>X-License-Identifier: MIT
========================================================================= */

#pragma once

typedef enum {
    ENUM_A,
    ENUM_2,
    ENUM_C,
    ENUM_4,
} test_enum_t;

static const float test_arr[] = {
    1.2f,
    2.3f,
    3.4f,
};
