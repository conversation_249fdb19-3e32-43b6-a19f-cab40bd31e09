# =========================================================================
#   Unity - A Test Framework for C
#   ThrowTheSwitch.org
#   Copyright (c) 2007-25 <PERSON>, <PERSON>, & <PERSON>
#   SPDX-License-Identifier: MIT
# =========================================================================

---
colour: true
:unity:
  :plugins: []
:tools:
  :test_compiler:
    :name: compiler
    :executable: clang
    :arguments:
    - "-c"
    - "-Wall"
    - "-Wextra"
    - "-Werror"
    - "-Wcast-qual"
    - "-Wconversion"
    - "-Wdisabled-optimization"
    - "-Wformat=2"
    - "-Winit-self"
    - "-Winline"
    - "-Winvalid-pch"
    - "-Wmissing-include-dirs"
    - "-Wnonnull"
    - "-Wpacked"
    - "-Wpointer-arith"
    - "-Wswitch-default"
    - "-Wstric<PERSON>-aliasing"
    - "-Wstrict-overflow=5"
    - "-Wuninitialized"
    - "-Wunused"
    - "-Wreturn-type"
    - "-Wshadow"
    - "-Wundef"
    - "-Wwrite-strings"
    - "-Wno-nested-externs"
    - "-Wno-unused-parameter"
    - "-Wno-variadic-macros"
    - "-Wbad-function-cast"
    - "-fms-extensions"
    - "-fno-omit-frame-pointer"
    - "-fno-common"
    - "-fstrict-aliasing"
    - "-std=gnu99"
    - "-pedantic"
    - "-O0"
    - '-I"$": COLLECTION_PATHS_TEST_TOOLCHAIN_INCLUDE'
    - '-I"$": COLLECTION_PATHS_TEST_SUPPORT_SOURCE_INCLUDE_VENDOR'
    - "-D$: COLLECTION_DEFINES_TEST_AND_VENDOR"
    - "${1}"
    - "-o ${2}"
  :test_linker:
    :name: linker
    :executable: clang
    :arguments:
    - "${1}"
    - "-lm"
    - "-m64"
    - "-o ${2}"
:extension:
  :object: ".o"
  :executable: ".exe"
:paths:
  :test:
    - src/
    - "../src/"
    - testdata/
    - tests/
:defines:
  :test:
  - UNITY_INCLUDE_DOUBLE
  - UNITY_SUPPORT_TEST_CASES
  - UNITY_SUPPORT_64
  - UNITY_COMPARE_PTRS_ON_ZERO_ARRAY
