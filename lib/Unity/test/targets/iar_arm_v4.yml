# =========================================================================
#   Unity - A Test Framework for C
#   ThrowTheSwitch.org
#   Copyright (c) 2007-25 <PERSON>, <PERSON>, & <PERSON>
#   SPDX-License-Identifier: MIT
# =========================================================================

---
tools_root: C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
colour: true
:unity:
  :plugins: []
:tools:
  :test_compiler:
    :name: compiler
    :executable:
    - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
    - arm\bin\iccarm.exe
    :arguments:
    - "--dlib_config"
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - arm\lib\dl4tptinl8n.h
    - "-z3"
    - "--no_cse"
    - "--no_unroll"
    - "--no_inline"
    - "--no_code_motion"
    - "--no_tbaa"
    - "--no_clustering"
    - "--no_scheduling"
    - "--debug"
    - "--cpu_mode thumb"
    - "--endian little"
    - "--cpu ARM7TDMI"
    - "--stack_align 4"
    - "--interwork"
    - "-e"
    - "--silent"
    - "--warnings_are_errors"
    - "--fpu None"
    - "--diag_suppress Pa050"
    - '-I"$": COLLECTION_PATHS_TEST_TOOLCHAIN_INCLUDE'
    - '-I"$": COLLECTION_PATHS_TEST_SUPPORT_SOURCE_INCLUDE_VENDOR'
    - "-D$: COLLECTION_DEFINES_TEST_AND_VENDOR"
    - "${1}"
    - "-o ${2}"
  :test_linker:
    :name: linker
    :executable:
    - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
    - common\bin\xlink.exe
    :arguments:
    - "${1}"
    - "-rt"
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - arm\lib\dl4tptinl8n.r79
    - "-D_L_EXTMEM_START=0"
    - "-D_L_EXTMEM_SIZE=0"
    - "-D_L_HEAP_SIZE=120"
    - "-D_L_STACK_SIZE=32"
    - "-e_small_write=_formatted_write"
    - "-s"
    - __program_start
    - "-f"
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - "\\arm\\config\\lnkarm.xcl"
    - "-o ${2}"
  :test_fixture:
    :name: simulator
    :executable:
    - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
    - common\bin\CSpyBat.exe
    :arguments:
    - "--silent"
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - arm\bin\armproc.dll
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - arm\bin\armsim.dll
    - "${1}"
    - "--plugin"
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - arm\bin\armbat.dll
    - "--backend"
    - "-B"
    - "-p"
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - arm\config\ioat91sam7X256.ddf
    - "-d"
    - sim
:extension:
  :object: ".r79"
  :executable: ".d79"
:paths:
  :test:
    - - C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\
      - arm\inc\
    - src\
    - "..\\src\\"
    - testdata/
    - tests\
    - vendor\unity\src\
:defines:
  :test:
  - UNITY_SUPPORT_64
  - UNITY_SUPPORT_TEST_CASES
