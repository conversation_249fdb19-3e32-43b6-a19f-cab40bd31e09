#!/usr/bin/env node

/**
 * ATTINY Control System - Version Manager
 * 
 * This script provides utilities for managing firmware versions across environments.
 * It allows listing, marking, and validating firmware versions in Cloud Storage.
 */

const { Storage } = require('@google-cloud/storage');
const { Firestore } = require('@google-cloud/firestore');
const fs = require('fs');
const path = require('path');
const semver = require('semver');

// Default values
const DEFAULT_PROJECT_ID = 'cannasol-automation-suite';

// Parse command line arguments
const argv = require('minimist')(process.argv.slice(2), {
  string: ['action', 'version', 'environment', 'target-version', 'project'],
  boolean: ['dry-run', 'all'],
  default: {
    'dry-run': false,
    'project': DEFAULT_PROJECT_ID,
    'environment': 'development',
    'all': false,
  },
  alias: {
    a: 'action',
    v: 'version',
    e: 'environment',
    t: 'target-version',
    p: 'project',
    d: 'dry-run',
  }
});

// Print usage information
function printUsage() {
  console.log(`
Version Manager for ATTINY Control System
========================================

Usage: version-manager.js --action <action> [options]

Actions:
  list                List available firmware versions
  current             Show current version for an environment
  mark                Mark a version as current
  promote             Promote a version from one environment to another
  validate            Validate a firmware version exists
  info                Show detailed information about a specific version
  
Common Options:
  -a, --action <action>           Action to perform (required)
  -e, --environment <env>         Environment: development, staging, production (default: development)
  -p, --project <project-id>      Google Cloud project ID (default: ${DEFAULT_PROJECT_ID})
  -d, --dry-run                   Show what would be done without making changes
  -h, --help                      Show this help message
  
Action-specific Options:
  -v, --version <version>         Version to operate on (required for mark, validate, info)
  -t, --target-version <version>  Target version for promote action
  --all                           List all versions, including archived (for list action)
  
Examples:
  # List all available versions in staging
  version-manager.js --action=list --environment=staging
  
  # Show current version in production
  version-manager.js --action=current --environment=production
  
  # Mark a specific version as current in development
  version-manager.js --action=mark --version=v1.2.0 --environment=development
  
  # Promote a version from staging to production
  version-manager.js --action=promote --environment=staging --target-version=production
  
  # Show detailed information about a specific version
  version-manager.js --action=info --version=v1.2.0 --environment=production
  `);
}

// Show help if requested
if (argv.h || argv.help) {
  printUsage();
  process.exit(0);
}

// Check if action is provided
if (!argv.action) {
  console.error('Error: --action is required');
  printUsage();
  process.exit(1);
}

// Validate environment
function validateEnvironment(env) {
  const validEnvironments = ['development', 'staging', 'production'];
  if (!validEnvironments.includes(env)) {
    console.error(`Error: Environment must be one of: ${validEnvironments.join(', ')}`);
    process.exit(1);
  }
}

// Get bucket name for environment
function getBucketName(environment) {
  switch (environment) {
    case 'development':
      return 'attiny-dev-firmware';
    case 'staging':
      return 'attiny-staging-firmware';
    case 'production':
      return 'attiny-prod-firmware';
    default:
      console.error(`Error: Unknown environment: ${environment}`);
      process.exit(1);
  }
}

// Execute the requested action
async function executeAction() {
  try {
    // Validate environment
    validateEnvironment(argv.environment);
    
    // Initialize clients
    const storage = new Storage({ projectId: argv.project });
    const firestore = new Firestore({ projectId: argv.project });
    
    const bucketName = getBucketName(argv.environment);
    const bucket = storage.bucket(bucketName);
    
    // Process the requested action
    switch (argv.action.toLowerCase()) {
      case 'list':
        await listVersions(bucket, argv.environment, argv.all);
        break;
      case 'current':
        await getCurrentVersion(bucket, firestore, argv.environment);
        break;
      case 'mark':
        if (!argv.version) {
          console.error('Error: --version is required for mark action');
          process.exit(1);
        }
        await markVersion(bucket, firestore, argv.environment, argv.version);
        break;
      case 'promote':
        if (!argv['target-version']) {
          console.error('Error: --target-version is required for promote action');
          process.exit(1);
        }
        await promoteVersion(storage, firestore, argv.environment, argv['target-version']);
        break;
      case 'validate':
        if (!argv.version) {
          console.error('Error: --version is required for validate action');
          process.exit(1);
        }
        await validateVersion(bucket, argv.environment, argv.version);
        break;
      case 'info':
        if (!argv.version) {
          console.error('Error: --version is required for info action');
          process.exit(1);
        }
        await versionInfo(bucket, firestore, argv.environment, argv.version);
        break;
      default:
        console.error(`Error: Unknown action: ${argv.action}`);
        printUsage();
        process.exit(1);
    }
    
  } catch (error) {
    console.error('Error executing action:', error);
    process.exit(1);
  }
}

// List available versions in an environment
async function listVersions(bucket, environment, showAll = false) {
  console.log(`Listing firmware versions in ${environment} environment...`);
  
  // Get the current version
  const currentVersionFile = 'firmware/' + environment + '/current-version';
  let currentVersion = null;
  
  try {
    const [currentVersionContent] = await bucket.file(currentVersionFile).download();
    currentVersion = currentVersionContent.toString().trim();
    console.log(`Current version: ${currentVersion}`);
  } catch (error) {
    console.log('No current version set');
  }
  
  // List versions in Cloud Storage
  const prefix = 'firmware/' + environment + '/';
  const options = {
    prefix: prefix,
    delimiter: '/'
  };
  
  try {
    const [files, _, apiResponse] = await bucket.getFiles(options);
    
    // Extract version folders (they have the path firmware/environment/vX.Y.Z/)
    const versionFolders = apiResponse.prefixes || [];
    
    if (versionFolders.length === 0) {
      console.log('No versions found');
      return;
    }
    
    // Extract and sort versions
    const versions = versionFolders.map(folder => {
      const parts = folder.split('/');
      return parts[parts.length - 2]; // Get the second-to-last path component
    }).filter(version => {
      if (!showAll && version.startsWith('archived-')) {
        return false;
      }
      return true;
    });
    
    // Sort by semver (removing the 'v' prefix)
    versions.sort((a, b) => {
      const aVersion = a.startsWith('v') ? a.substring(1) : a;
      const bVersion = b.startsWith('v') ? b.substring(1) : b;
      return semver.compare(bVersion, aVersion); // Newest first
    });
    
    // Display the versions
    console.log('\nAvailable versions:');
    for (const version of versions) {
      let marker = ' ';
      if (version === currentVersion) {
        marker = '*';
      }
      
      // Check for active rollout
      const rolloutConfigPath = `firmware/${environment}/${version}/rollout-config.json`;
      let rolloutInfo = '';
      
      try {
        const [rolloutConfig] = await bucket.file(rolloutConfigPath).download();
        const config = JSON.parse(rolloutConfig.toString());
        
        if (config.rolloutPercentage < 100) {
          rolloutInfo = ` [rollout: ${config.rolloutPercentage}%]`;
        }
      } catch (error) {
        // No rollout config found, that's OK
      }
      
      console.log(`${marker} ${version}${rolloutInfo}`);
    }
    
    console.log('\n* = current version');
    
  } catch (error) {
    console.error('Error listing versions:', error);
  }
}

// Get current version for an environment
async function getCurrentVersion(bucket, firestore, environment) {
  console.log(`Getting current version for ${environment} environment...`);
  
  try {
    // Check Cloud Storage for current version
    const currentVersionFile = 'firmware/' + environment + '/current-version';
    
    try {
      const [currentVersionContent] = await bucket.file(currentVersionFile).download();
      const version = currentVersionContent.toString().trim();
      console.log(`Current version (from Cloud Storage): ${version}`);
      
      // Also check if there's a rollout in progress
      const rolloutConfigPath = `firmware/${environment}/${version}/rollout-config.json`;
      
      try {
        const [rolloutConfig] = await bucket.file(rolloutConfigPath).download();
        const config = JSON.parse(rolloutConfig.toString());
        
        console.log('Rollout configuration:');
        console.log(`- Deployed on: ${new Date(config.deployTime).toLocaleString()}`);
        console.log(`- Deployed by: ${config.deployedBy}`);
        
        if (config.canaryPercentage > 0) {
          console.log(`- Canary percentage: ${config.canaryPercentage}%`);
        }
        
        console.log(`- Rollout percentage: ${config.rolloutPercentage}%`);
        
        if (config.isRollback) {
          console.log(`- Rollback from: ${config.rolledBackFrom}`);
        }
      } catch (error) {
        // No rollout config, which is fine
        console.log('No active rollout configuration found');
      }
      
    } catch (error) {
      console.log('No current version found in Cloud Storage');
    }
    
    // Also check Firestore for current version
    const envDoc = await firestore.collection('environments').doc(environment).get();
    
    if (envDoc.exists) {
      const data = envDoc.data();
      if (data.currentVersion) {
        console.log(`Current version (from Firestore): ${data.currentVersion}`);
        
        if (data.lastUpdated) {
          console.log(`Last updated: ${data.lastUpdated.toDate().toLocaleString()}`);
        }
        
        if (data.lastDeployment) {
          console.log('Last deployment:');
          console.log(`- Version: ${data.lastDeployment.version}`);
          console.log(`- Time: ${data.lastDeployment.timestamp.toDate().toLocaleString()}`);
          console.log(`- By: ${data.lastDeployment.updatedBy}`);
        }
      } else {
        console.log('No current version found in Firestore');
      }
    } else {
      console.log('No environment record found in Firestore');
    }
    
  } catch (error) {
    console.error('Error getting current version:', error);
  }
}

// Mark a version as current
async function markVersion(bucket, firestore, environment, version) {
  console.log(`Marking version ${version} as current for ${environment} environment...`);
  
  // Validate the version exists
  const versionExists = await validateVersion(bucket, environment, version, false);
  
  if (!versionExists) {
    console.error(`Error: Version ${version} does not exist in ${environment} environment`);
    process.exit(1);
  }
  
  // If this is a dry run, exit now
  if (argv['dry-run']) {
    console.log(`DRY RUN: Would mark ${version} as current for ${environment}`);
    return;
  }
  
  try {
    // Update the current version in Cloud Storage
    const currentVersionFile = 'firmware/' + environment + '/current-version';
    await bucket.file(currentVersionFile).save(version);
    
    console.log(`Updated current version in Cloud Storage to ${version}`);
    
    // Update the current version in Firestore
    const envRef = firestore.collection('environments').doc(environment);
    
    await envRef.set({
      currentVersion: version,
      lastUpdated: new Date(),
      lastManualUpdate: {
        version: version,
        timestamp: new Date(),
        updatedBy: process.env.USER || 'unknown'
      }
    }, { merge: true });
    
    console.log(`Updated current version in Firestore to ${version}`);
    
    // Check if there's a rollout config, and if not, create a default one
    const rolloutConfigPath = `firmware/${environment}/${version}/rollout-config.json`;
    
    try {
      await bucket.file(rolloutConfigPath).download();
      console.log('Rollout configuration already exists, not modifying');
    } catch (error) {
      // No rollout config found, create a default one
      const rolloutConfig = {
        version: version,
        environment: environment,
        canaryPercentage: 0,
        rolloutPercentage: 100, // Full rollout by default
        deployTime: new Date().toISOString(),
        deployedBy: process.env.USER || 'unknown',
        manuallyMarkedAsCurrent: true
      };
      
      // Save the rollout config
      const tmpFile = '/tmp/rollout-config.json';
      fs.writeFileSync(tmpFile, JSON.stringify(rolloutConfig, null, 2));
      
      await bucket.upload(tmpFile, {
        destination: rolloutConfigPath,
        metadata: {
          contentType: 'application/json'
        }
      });
      
      fs.unlinkSync(tmpFile);
      
      console.log('Created default rollout configuration (100% rollout)');
    }
    
  } catch (error) {
    console.error('Error marking version as current:', error);
    process.exit(1);
  }
}

// Promote a version from one environment to another
async function promoteVersion(storage, firestore, sourceEnv, targetEnv) {
  validateEnvironment(sourceEnv);
  validateEnvironment(targetEnv);
  
  console.log(`Promoting current version from ${sourceEnv} to ${targetEnv}...`);
  
  // Define buckets
  const sourceBucketName = getBucketName(sourceEnv);
  const targetBucketName = getBucketName(targetEnv);
  
  const sourceBucket = storage.bucket(sourceBucketName);
  const targetBucket = storage.bucket(targetBucketName);
  
  try {
    // Get current version from source environment
    const sourceVersionFile = 'firmware/' + sourceEnv + '/current-version';
    
    let sourceVersion;
    try {
      const [sourceVersionContent] = await sourceBucket.file(sourceVersionFile).download();
      sourceVersion = sourceVersionContent.toString().trim();
      console.log(`Current version in ${sourceEnv}: ${sourceVersion}`);
    } catch (error) {
      console.error(`Error: No current version found in ${sourceEnv} environment`);
      process.exit(1);
    }
    
    // If this is a dry run, exit now
    if (argv['dry-run']) {
      console.log(`DRY RUN: Would promote ${sourceVersion} from ${sourceEnv} to ${targetEnv}`);
      return;
    }
    
    // Check if the version already exists in the target environment
    const targetVersionPath = `firmware/${targetEnv}/${sourceVersion}/`;
    
    try {
      const [files] = await targetBucket.getFiles({ prefix: targetVersionPath, maxResults: 1 });
      
      if (files.length > 0) {
        console.log(`Version ${sourceVersion} already exists in ${targetEnv} environment`);
      } else {
        // Copy the firmware files from source to target
        console.log(`Copying firmware files for ${sourceVersion} to ${targetEnv} environment...`);
        
        const [sourceFiles] = await sourceBucket.getFiles({ prefix: `firmware/${sourceEnv}/${sourceVersion}/` });
        
        for (const file of sourceFiles) {
          const targetPath = file.name.replace(`firmware/${sourceEnv}/`, `firmware/${targetEnv}/`);
          
          await file.copy(storage.bucket(targetBucketName).file(targetPath));
          console.log(`Copied ${file.name} to ${targetPath}`);
        }
      }
      
      // Mark the version as current in the target environment
      console.log(`Marking ${sourceVersion} as current in ${targetEnv} environment...`);
      
      // Update current version in Cloud Storage
      const targetVersionFile = 'firmware/' + targetEnv + '/current-version';
      await targetBucket.file(targetVersionFile).save(sourceVersion);
      
      // Update current version in Firestore
      const envRef = firestore.collection('environments').doc(targetEnv);
      
      await envRef.set({
        currentVersion: sourceVersion,
        lastUpdated: new Date(),
        lastPromotion: {
          version: sourceVersion,
          timestamp: new Date(),
          promotedFrom: sourceEnv,
          promotedBy: process.env.USER || 'unknown'
        }
      }, { merge: true });
      
      // Create a rollout config for the promoted version
      const rolloutConfig = {
        version: sourceVersion,
        environment: targetEnv,
        canaryPercentage: targetEnv === 'production' ? 10 : 0, // Use canary for production
        rolloutPercentage: targetEnv === 'production' ? 10 : 100, // Gradual rollout for production
        deployTime: new Date().toISOString(),
        deployedBy: process.env.USER || 'unknown',
        promotedFrom: sourceEnv
      };
      
      // Save the rollout config
      const tmpFile = '/tmp/rollout-config.json';
      fs.writeFileSync(tmpFile, JSON.stringify(rolloutConfig, null, 2));
      
      await targetBucket.upload(tmpFile, {
        destination: `firmware/${targetEnv}/${sourceVersion}/rollout-config.json`,
        metadata: {
          contentType: 'application/json'
        }
      });
      
      fs.unlinkSync(tmpFile);
      
      console.log(`Successfully promoted ${sourceVersion} from ${sourceEnv} to ${targetEnv}`);
      
      if (targetEnv === 'production') {
        console.log('Note: For production, a canary rollout of 10% has been configured.');
        console.log('Use the deployment script to manage the full rollout when ready.');
      }
      
    } catch (error) {
      console.error('Error promoting version:', error);
      process.exit(1);
    }
  } catch (error) {
    console.error('Error promoting version:', error);
    process.exit(1);
  }
}

// Validate that a version exists
async function validateVersion(bucket, environment, version, verbose = true) {
  if (verbose) {
    console.log(`Validating version ${version} in ${environment} environment...`);
  }
  
  const versionPath = `firmware/${environment}/${version}/`;
  
  try {
    // Check if the version directory exists
    const [files] = await bucket.getFiles({ prefix: versionPath, maxResults: 1 });
    
    if (files.length === 0) {
      if (verbose) {
        console.error(`Error: Version ${version} not found in ${environment} environment`);
      }
      return false;
    }
    
    // Check if the firmware file exists
    const firmwarePath = `${versionPath}attiny85.hex`;
    const [exists] = await bucket.file(firmwarePath).exists();
    
    if (!exists) {
      if (verbose) {
        console.error(`Error: Firmware file not found for version ${version} in ${environment} environment`);
      }
      return false;
    }
    
    if (verbose) {
      console.log(`Version ${version} is valid in ${environment} environment`);
    }
    
    return true;
  } catch (error) {
    if (verbose) {
      console.error('Error validating version:', error);
    }
    return false;
  }
}

// Display detailed information about a version
async function versionInfo(bucket, firestore, environment, version) {
  console.log(`Getting information for version ${version} in ${environment} environment...`);
  
  // Validate the version exists
  const versionExists = await validateVersion(bucket, environment, version, false);
  
  if (!versionExists) {
    console.error(`Error: Version ${version} does not exist in ${environment} environment`);
    process.exit(1);
  }
  
  try {
    // Get all files for the version
    const versionPath = `firmware/${environment}/${version}/`;
    const [files] = await bucket.getFiles({ prefix: versionPath });
    
    console.log(`\nVersion: ${version} (${environment})`);
    
    // Check if this is the current version
    const currentVersionFile = 'firmware/' + environment + '/current-version';
    let isCurrent = false;
    
    try {
      const [currentVersionContent] = await bucket.file(currentVersionFile).download();
      const currentVersion = currentVersionContent.toString().trim();
      isCurrent = (version === currentVersion);
    } catch (error) {
      // No current version set
    }
    
    console.log(`Current Version: ${isCurrent ? 'Yes' : 'No'}`);
    
    // Get rollout configuration if available
    const rolloutConfigPath = `${versionPath}rollout-config.json`;
    
    try {
      const [rolloutConfigContent] = await bucket.file(rolloutConfigPath).download();
      const rolloutConfig = JSON.parse(rolloutConfigContent.toString());
      
      console.log('\nRollout Configuration:');
      console.log(`- Deploy Time: ${new Date(rolloutConfig.deployTime).toLocaleString()}`);
      console.log(`- Deployed By: ${rolloutConfig.deployedBy}`);
      
      if (rolloutConfig.canaryPercentage > 0) {
        console.log(`- Canary Percentage: ${rolloutConfig.canaryPercentage}%`);
      }
      
      console.log(`- Rollout Percentage: ${rolloutConfig.rolloutPercentage}%`);
      
      if (rolloutConfig.isRollback) {
        console.log(`- Rollback From: ${rolloutConfig.rolledBackFrom}`);
      }
      
      if (rolloutConfig.promotedFrom) {
        console.log(`- Promoted From: ${rolloutConfig.promotedFrom}`);
      }
    } catch (error) {
      console.log('No rollout configuration found');
    }
    
    // Get metadata for the firmware file
    const firmwarePath = `${versionPath}attiny85.hex`;
    
    try {
      const [metadata] = await bucket.file(firmwarePath).getMetadata();
      
      console.log('\nFirmware File:');
      console.log(`- Size: ${(metadata.size / 1024).toFixed(2)} KB`);
      console.log(`- Created: ${new Date(metadata.timeCreated).toLocaleString()}`);
      console.log(`- Updated: ${new Date(metadata.updated).toLocaleString()}`);
      console.log(`- MD5 Hash: ${metadata.md5Hash}`);
    } catch (error) {
      console.log('Error getting firmware file metadata');
    }
    
    // Find deployment records in Firestore
    const deploymentsQuery = await firestore.collection('deployments')
      .where('version', '==', version)
      .where('environment', '==', environment)
      .orderBy('timestamp', 'desc')
      .limit(5)
      .get();
    
    if (!deploymentsQuery.empty) {
      console.log('\nDeployment History:');
      
      deploymentsQuery.forEach(doc => {
        const data = doc.data();
        console.log(`- ${data.timestamp.toDate().toLocaleString()}: ${data.status}`);
        
        if (data.updatedBy) {
          console.log(`  By: ${data.updatedBy}`);
        }
        
        if (data.replacedBy) {
          console.log(`  Replaced By: ${data.replacedBy}`);
        }
        
        if (data.reason) {
          console.log(`  Reason: ${data.reason}`);
        }
      });
    } else {
      console.log('\nNo deployment records found in Firestore');
    }
    
    // List other files in the version directory
    console.log('\nOther Files:');
    let hasOtherFiles = false;
    
    for (const file of files) {
      const filename = path.basename(file.name);
      
      if (filename !== 'attiny85.hex' && filename !== 'rollout-config.json') {
        console.log(`- ${filename} (${(file.metadata.size / 1024).toFixed(2)} KB)`);
        hasOtherFiles = true;
      }
    }
    
    if (!hasOtherFiles) {
      console.log('No additional files found');
    }
    
  } catch (error) {
    console.error('Error getting version information:', error);
    process.exit(1);
  }
}

// Execute the requested action
executeAction(); 