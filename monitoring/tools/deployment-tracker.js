#!/usr/bin/env node

/**
 * ATTINY Control System - Deployment Tracker
 * 
 * This script records deployment events in Firestore for tracking and monitoring.
 * It is used by both the deployment and rollback scripts.
 */

const { Firestore } = require('@google-cloud/firestore');
const { BigQuery } = require('@google-cloud/bigquery');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const os = require('os');

// Default values
const DEFAULT_PROJECT_ID = 'cannasol-automation-suite';

// Parse command line arguments
const argv = require('minimist')(process.argv.slice(2), {
  string: ['version', 'environment', 'status', 'replaced-by', 'reason', 'project'],
  boolean: ['dry-run'],
  default: {
    'dry-run': false,
    'project': DEFAULT_PROJECT_ID,
  },
  alias: {
    v: 'version',
    e: 'environment',
    s: 'status',
    r: 'reason',
    p: 'project',
    d: 'dry-run',
  }
});

// Validate required arguments
function validateArgs() {
  if (!argv.version) {
    console.error('Error: --version is required');
    printUsage();
    process.exit(1);
  }
  
  if (!argv.environment) {
    console.error('Error: --environment is required');
    printUsage();
    process.exit(1);
  }
  
  if (!argv.status) {
    console.error('Error: --status is required');
    printUsage();
    process.exit(1);
  }
  
  const validStatuses = ['deployed', 'deploying', 'failed', 'rolled-back', 'canceled'];
  if (!validStatuses.includes(argv.status)) {
    console.error(`Error: --status must be one of: ${validStatuses.join(', ')}`);
    process.exit(1);
  }
  
  const validEnvironments = ['development', 'staging', 'production'];
  if (!validEnvironments.includes(argv.environment)) {
    console.error(`Error: --environment must be one of: ${validEnvironments.join(', ')}`);
    process.exit(1);
  }
  
  // If status is rolled-back, replaced-by is required
  if (argv.status === 'rolled-back' && !argv['replaced-by']) {
    console.error('Error: --replaced-by is required when status is rolled-back');
    printUsage();
    process.exit(1);
  }
}

// Print usage information
function printUsage() {
  console.log(`
Usage: deployment-tracker.js [options]

Options:
  -v, --version <version>       Firmware version (required, format: v1.x.x)
  -e, --environment <env>       Deployment environment: development, staging, production (required)
  -s, --status <status>         Deployment status: deployed, deploying, failed, rolled-back, canceled (required)
  --replaced-by <version>       Version that replaced this one (required for rolled-back status)
  -r, --reason <reason>         Reason for status change (optional)
  -p, --project <project-id>    Google Cloud project ID (default: ${DEFAULT_PROJECT_ID})
  -d, --dry-run                 Show what would be done without making changes
  -h, --help                    Show this help message
  
Examples:
  deployment-tracker.js --version=v1.2.0 --environment=production --status=deployed
  deployment-tracker.js --version=v1.2.0 --environment=production --status=rolled-back --replaced-by=v1.1.0
  `);
}

// Show help if requested
if (argv.h || argv.help) {
  printUsage();
  process.exit(0);
}

// Run main function
async function main() {
  try {
    // Validate arguments
    validateArgs();
    
    // Format the data for Firestore
    const deploymentData = {
      version: argv.version,
      environment: argv.environment,
      status: argv.status,
      timestamp: new Date(),
      updatedBy: `${os.userInfo().username}@${os.hostname()}`,
    };
    
    // Add optional fields if provided
    if (argv['replaced-by']) {
      deploymentData.replacedBy = argv['replaced-by'];
    }
    
    if (argv.reason) {
      deploymentData.reason = argv.reason;
    }
    
    console.log('Recording deployment event:');
    console.log(JSON.stringify(deploymentData, null, 2));
    
    // If dry run, don't actually save to Firestore
    if (argv['dry-run']) {
      console.log('DRY RUN: Would save to Firestore');
      process.exit(0);
    }
    
    // Initialize Firestore client
    const firestore = new Firestore({
      projectId: argv.project,
    });
    
    // Create a unique deployment ID if needed
    const deploymentId = `${deploymentData.version}-${deploymentData.environment}-${Date.now()}`;
    
    // Check if deployment record already exists
    const deploymentRef = firestore.collection('deployments').doc(deploymentId);
    
    // Create or update the deployment record
    await deploymentRef.set(deploymentData, { merge: true });
    console.log(`Successfully recorded deployment event with ID: ${deploymentId}`);
    
    // If status is rolled-back, update the status of the replacing version
    if (argv.status === 'rolled-back' && argv['replaced-by']) {
      // Find the deployment record for the replacing version
      const replacementQuery = await firestore.collection('deployments')
        .where('version', '==', argv['replaced-by'])
        .where('environment', '==', argv.environment)
        .orderBy('timestamp', 'desc')
        .limit(1)
        .get();
      
      if (!replacementQuery.empty) {
        const replacementDoc = replacementQuery.docs[0];
        const updateData = {
          rollbackOf: argv.version,
          rollbackTimestamp: new Date(),
        };
        
        await replacementDoc.ref.update(updateData);
        console.log(`Updated replacement version ${argv['replaced-by']} with rollback information`);
      }
    }
    
    // Record deployment metrics in BigQuery if available
    try {
      const bigquery = new BigQuery({
        projectId: argv.project,
      });
      
      const row = {
        version: deploymentData.version,
        environment: deploymentData.environment, 
        status: deploymentData.status,
        timestamp: deploymentData.timestamp.toISOString(),
        updated_by: deploymentData.updatedBy
      };
      
      if (deploymentData.replacedBy) {
        row.replaced_by = deploymentData.replacedBy;
      }
      
      if (deploymentData.reason) {
        row.reason = deploymentData.reason;
      }
      
      await bigquery.dataset('device_metrics').table('deployments').insert([row]);
      console.log('Deployment metrics recorded successfully in BigQuery');
    } catch (err) {
      console.warn('Warning: Failed to record deployment metrics:', err.message);
    }
    
    // Update environments collection for quick access to current version 
    if (argv.status === 'deployed') {
      try {
        const envRef = firestore.collection('environments').doc(argv.environment);
        await envRef.set({
          currentVersion: argv.version,
          lastUpdated: new Date(),
          lastDeployment: {
            version: argv.version,
            timestamp: deploymentData.timestamp,
            updatedBy: deploymentData.updatedBy
          }
        }, { merge: true });
        
        console.log(`Updated current version in ${argv.environment} to ${argv.version}`);
      } catch (err) {
        console.warn('Warning: Failed to update environments collection:', err.message);
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error recording deployment event:', error);
    process.exit(1);
  }
}

main(); 