const fs = require('fs');
const { Storage } = require('@google-cloud/storage');
const { BigQuery } = require('@google-cloud/bigquery');

/**
 * Records build metrics to BigQuery for monitoring and analysis
 */
async function recordBuildMetrics() {
  // Initialize clients
  const storage = new Storage();
  const bigquery = new BigQuery();
  
  // Extract build information from environment variables
  const buildId = process.env.GITHUB_RUN_ID;
  const buildNumber = process.env.GITHUB_RUN_NUMBER;
  const commit = process.env.GITHUB_SHA;
  const branch = process.env.GITHUB_REF.replace('refs/heads/', '');
  const startTime = new Date(process.env.BUILD_START_TIME);
  const endTime = new Date();
  const duration = (endTime - startTime) / 1000; // in seconds
  
  console.log(`Recording metrics for build ${buildNumber} (${buildId})`);
  console.log(`Commit: ${commit}, Branch: ${branch}`);
  console.log(`Duration: ${duration} seconds`);
  
  try {
    // Read test results if they exist
    let testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      coverage: 0
    };
    
    if (fs.existsSync('build/test-results.json')) {
      testResults = JSON.parse(fs.readFileSync('build/test-results.json'));
      console.log(`Test results: ${testResults.passed}/${testResults.total} passed, ${testResults.coverage}% coverage`);
    } else {
      console.log('No test results file found, using default values');
    }
    
    // Record metrics to BigQuery
    await bigquery.dataset('ci_metrics').table('builds').insert([
      {
        build_id: buildId,
        build_number: parseInt(buildNumber),
        commit,
        branch,
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString(),
        duration,
        status: process.env.BUILD_STATUS,
        test_count: testResults.total,
        test_passed: testResults.passed,
        test_failed: testResults.failed,
        test_coverage: testResults.coverage
      }
    ]);
    
    console.log('CI metrics recorded successfully');
    
    // Calculate success rate for recent builds
    // This becomes available as a metric in Cloud Monitoring
    const [rows] = await bigquery.query({
      query: `
        SELECT
          COUNTIF(status = 'success') / COUNT(*) AS success_rate
        FROM
          \`ci_metrics.builds\`
        WHERE
          start_time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
      `
    });
    
    const successRate = rows[0].success_rate;
    console.log(`7-day success rate: ${(successRate * 100).toFixed(2)}%`);
    
  } catch (err) {
    console.error('Error recording CI metrics:', err);
    throw err;
  }
}

// Execute the function
recordBuildMetrics().catch(err => {
  console.error('Error recording CI metrics:', err);
  process.exit(1);
}); 