#!/usr/bin/env node

/**
 * Device Heartbeat Simulator
 * 
 * This script simulates device heartbeats by sending HTTP requests to the device-monitor
 * Cloud Function. It's useful for testing the monitoring system without real devices.
 */

const https = require('https');
const { v4: uuidv4 } = require('uuid');

// Configuration
const DEFAULT_CONFIG = {
  projectId: 'cannasol-automation-suite',
  region: 'us-central1',
  deviceCount: 5,
  intervalSeconds: 60,
  runDurationMinutes: 10,
  firmwareVersions: ['1.0.0', '1.1.0', '1.2.0'],
  regions: ['us-west', 'us-east', 'eu-west', 'asia-east']
};

// Parse command line arguments
const args = process.argv.slice(2);
const config = { ...DEFAULT_CONFIG };

for (let i = 0; i < args.length; i += 2) {
  const key = args[i].replace('--', '');
  const value = args[i + 1];
  
  if (key === 'deviceCount' || key === 'intervalSeconds' || key === 'runDurationMinutes') {
    config[key] = parseInt(value, 10);
  } else if (key === 'firmwareVersions' || key === 'regions') {
    config[key] = value.split(',');
  } else {
    config[key] = value;
  }
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Generate a set of simulated devices
const devices = Array.from({ length: config.deviceCount }, () => ({
  deviceId: uuidv4(),
  firmwareVersion: getRandomItem(config.firmwareVersions),
  region: getRandomItem(config.regions),
  createdAt: new Date()
}));

console.log(`${colors.blue}Device Heartbeat Simulator${colors.reset}`);
console.log(`${colors.blue}=======================${colors.reset}`);
console.log(`Project: ${config.projectId}`);
console.log(`Function region: ${config.region}`);
console.log(`Simulating ${colors.green}${config.deviceCount}${colors.reset} devices`);
console.log(`Heartbeat interval: ${colors.green}${config.intervalSeconds}${colors.reset} seconds`);
console.log(`Run duration: ${colors.green}${config.runDurationMinutes}${colors.reset} minutes`);
console.log(`Firmware versions: ${colors.green}${config.firmwareVersions.join(', ')}${colors.reset}`);
console.log(`Regions: ${colors.green}${config.regions.join(', ')}${colors.reset}`);
console.log('');
console.log(`${colors.blue}Simulated Devices:${colors.reset}`);

devices.forEach((device, index) => {
  console.log(`${index + 1}. ${colors.cyan}${device.deviceId}${colors.reset} - ${colors.yellow}${device.firmwareVersion}${colors.reset} - ${colors.magenta}${device.region}${colors.reset}`);
});

console.log('');
console.log(`${colors.blue}Starting simulation...${colors.reset}`);
console.log('Press Ctrl+C to stop');
console.log('');

// Start the simulation
let heartbeatCount = 0;
const startTime = new Date();
const endTime = new Date(startTime.getTime() + (config.runDurationMinutes * 60 * 1000));

const interval = setInterval(() => {
  // Check if we should stop
  if (new Date() > endTime) {
    clearInterval(interval);
    console.log(`\n${colors.green}Simulation complete. Sent ${heartbeatCount} heartbeats.${colors.reset}`);
    process.exit(0);
  }
  
  // Send heartbeats for each device
  devices.forEach(device => {
    sendHeartbeat(device)
      .then(success => {
        if (success) {
          heartbeatCount++;
          const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
          console.log(`${timestamp} - ${colors.green}✓${colors.reset} Heartbeat sent for device ${colors.cyan}${device.deviceId}${colors.reset}`);
        }
      })
      .catch(error => {
        const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
        console.error(`${timestamp} - ${colors.red}✗${colors.reset} Error sending heartbeat for device ${colors.cyan}${device.deviceId}${colors.reset}: ${error.message}`);
      });
  });
}, config.intervalSeconds * 1000);

/**
 * Send a heartbeat for a device
 * @param {Object} device - Device information
 * @returns {Promise<boolean>} - Success status
 */
function sendHeartbeat(device) {
  return new Promise((resolve, reject) => {
    // Generate random metrics
    const metrics = {
      temperature: parseFloat((Math.random() * 30 + 10).toFixed(1)),
      voltage: parseFloat((Math.random() * 1 + 3).toFixed(2)),
      uptime: Math.floor(Math.random() * 86400),
      signalStrength: Math.floor(Math.random() * 100)
    };
    
    const data = JSON.stringify({
      deviceId: device.deviceId,
      firmwareVersion: device.firmwareVersion,
      region: device.region,
      status: Math.random() > 0.05 ? 'online' : 'degraded', // 5% chance of degraded status
      metrics
    });
    
    const options = {
      hostname: `${config.region}-${config.projectId}.cloudfunctions.net`,
      port: 443,
      path: '/device-monitor',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };
    
    const req = https.request(options, res => {
      let response = '';
      
      res.on('data', chunk => {
        response += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(true);
        } else {
          reject(new Error(`HTTP status ${res.statusCode}: ${response}`));
        }
      });
    });
    
    req.on('error', error => {
      reject(error);
    });
    
    req.write(data);
    req.end();
  });
}

/**
 * Get a random item from an array
 * @param {Array} array - The array to choose from
 * @returns {*} - A random item from the array
 */
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Handle Ctrl+C to gracefully exit
process.on('SIGINT', () => {
  clearInterval(interval);
  console.log(`\n${colors.yellow}Simulation interrupted. Sent ${heartbeatCount} heartbeats.${colors.reset}`);
  process.exit(0);
}); 