#!/usr/bin/env python3
# monitoring/tools/cppcheck_report.py
"""
Converts cppcheck XML output to HTML for better visualization.
"""

import sys
import xml.etree.ElementTree as ET

def convert_to_html(xml_file, html_file):
    """
    Converts cppcheck XML output to an HTML report
    
    Args:
        xml_file: Path to the cppcheck XML output
        html_file: Path to write the HTML report
    """
    tree = ET.parse(xml_file)
    root = tree.getroot()
    
    with open(html_file, 'w') as f:
        f.write('<!DOCTYPE html>\n')
        f.write('<html lang="en">\n')
        f.write('<head>\n')
        f.write('  <meta charset="UTF-8">\n')
        f.write('  <title>ATTINY Control - Cppcheck Analysis Results</title>\n')
        f.write('  <style>\n')
        f.write('    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }\n')
        f.write('    h1 { color: #333; }\n')
        f.write('    .summary { margin-bottom: 20px; }\n')
        f.write('    .error { color: #d9534f; }\n')
        f.write('    .warning { color: #f0ad4e; }\n')
        f.write('    .style { color: #5bc0de; }\n')
        f.write('    .performance { color: #5cb85c; }\n')
        f.write('    .information { color: #337ab7; }\n')
        f.write('    table { border-collapse: collapse; width: 100%; }\n')
        f.write('    th, td { text-align: left; padding: 8px; }\n')
        f.write('    tr:nth-child(even) { background-color: #f2f2f2; }\n')
        f.write('    th { background-color: #4CAF50; color: white; }\n')
        f.write('  </style>\n')
        f.write('</head>\n')
        f.write('<body>\n')
        f.write('  <h1>Cppcheck Analysis Results</h1>\n')
        
        # Count issues by severity
        error_count = 0
        warning_count = 0
        style_count = 0
        performance_count = 0
        information_count = 0
        
        for error in root.findall('.//error'):
            severity = error.get('severity')
            if severity == 'error':
                error_count += 1
            elif severity == 'warning':
                warning_count += 1
            elif severity == 'style':
                style_count += 1
            elif severity == 'performance':
                performance_count += 1
            elif severity == 'information':
                information_count += 1
        
        # Write summary
        f.write('  <div class="summary">\n')
        f.write('    <h2>Summary</h2>\n')
        f.write('    <p>\n')
        f.write(f'      <span class="error">Errors: {error_count}</span><br>\n')
        f.write(f'      <span class="warning">Warnings: {warning_count}</span><br>\n')
        f.write(f'      <span class="style">Style Issues: {style_count}</span><br>\n')
        f.write(f'      <span class="performance">Performance Issues: {performance_count}</span><br>\n')
        f.write(f'      <span class="information">Information: {information_count}</span><br>\n')
        f.write('    </p>\n')
        f.write('  </div>\n')
        
        # Write details table
        f.write('  <h2>Detailed Analysis</h2>\n')
        f.write('  <table>\n')
        f.write('    <tr><th>File</th><th>Line</th><th>Type</th><th>Message</th></tr>\n')
        
        for error in root.findall('.//error'):
            severity = error.get('severity')
            msg = error.get('msg')
            
            for location in error.findall('location'):
                file = location.get('file')
                line = location.get('line')
                
                f.write(f'    <tr class="{severity}">\n')
                f.write(f'      <td>{file}</td>\n')
                f.write(f'      <td>{line}</td>\n')
                f.write(f'      <td>{severity}</td>\n')
                f.write(f'      <td>{msg}</td>\n')
                f.write('    </tr>\n')
        
        f.write('  </table>\n')
        f.write('<script>\n')
        f.write('  document.addEventListener("DOMContentLoaded", function() {\n')
        f.write('    if (document.querySelectorAll("table tr").length <= 1) {\n')
        f.write('      document.body.innerHTML += "<h3>No issues found.</h3>";\n')
        f.write('    }\n')
        f.write('  });\n')
        f.write('</script>\n')
        f.write('</body>\n')
        f.write('</html>\n')

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print('Usage: python3 cppcheck_report.py <xml_file> <html_file>')
        sys.exit(1)
    
    convert_to_html(sys.argv[1], sys.argv[2]) 