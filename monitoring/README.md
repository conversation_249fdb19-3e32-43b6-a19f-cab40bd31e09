# Monitoring and Alerting System for ATTINY Control

This directory contains the tools and configuration for comprehensive monitoring and alerting of the ATTINY Control System development process, CI/CD pipeline, and deployed devices.

## Components

- **Code Quality Monitoring**: Static analysis with cppcheck
- **Build Metrics**: Tracking build performance and success rates
- **Deployment Tracking**: Monitoring firmware deployment status
- **Device Heartbeats**: Tracking device status and health
- **Alert Configuration**: Email and Slack notifications for critical events

## Setup

1. **Prerequisites**:
   - Google Cloud SDK installed locally
   - Access to Google Cloud project `cannasol-automation-suite`
   - `gcloud` authenticated with appropriate permissions

2. **Environment Setup**:
   ```bash
   # Run the setup script to configure monitoring resources
   ./setup.sh
   ```

3. **Deploy Device Monitor Function**:
   ```bash
   # Deploy device monitor function
   cd functions/device-monitor
   gcloud functions deploy device-monitor \
     --runtime nodejs14 \
     --trigger-http \
     --allow-unauthenticated \
     --entry-point recordDeviceHeartbeat
   ```

4. **Configure GitHub Secrets**:
   The following secrets must be configured in your GitHub repository:
   - `GCP_SA_KEY`: Service account key with access to GCP resources
   - `SLACK_WEBHOOK_URL`: Webhook URL for Slack notifications (optional)
   - `EMAIL_USERNAME`: Gmail username for email notifications (optional)
   - `EMAIL_PASSWORD`: Gmail password or app password (optional)

## Monitoring Dashboard

The monitoring dashboard provides a real-time view of system health and performance metrics:

- **Build Success Rate**: Percentage of successful builds over time
- **Test Coverage**: Code coverage trends for the project
- **Build Duration**: Time taken for builds to complete
- **Device Status**: Current status of deployed devices
- **Firmware Versions**: Distribution of firmware versions in the field
- **Regional Distribution**: Geographic distribution of devices

## Alert Configuration

Alerts are configured for the following conditions:

1. **Device Offline**: Triggered when a device fails to send a heartbeat for more than 5 minutes
2. **Build Failures**: Triggered when the build success rate drops below 80% over 1 hour
3. **Deployment Failures**: Triggered when a deployment to production fails

## Tools

- **cppcheck_report.py**: Converts cppcheck XML output to HTML reports
- **ci-metrics.js**: Records build metrics to BigQuery for analysis
- **deployment-tracker.js**: Tracks firmware deployments across environments
- **device-monitor**: Cloud Function for recording device heartbeats

## Device Heartbeat API

Devices can send heartbeat signals using the following endpoint:

```
POST https://us-central1-[PROJECT_ID].cloudfunctions.net/device-monitor
```

Request body:
```json
{
  "deviceId": "unique-device-id",
  "firmwareVersion": "1.0.0",
  "status": "online",
  "metrics": {
    "temperature": 25.5,
    "voltage": 3.3,
    "uptime": 3600
  }
}
```

## Integration with CI/CD

The monitoring system is integrated with the CI/CD pipeline:

1. **Static Analysis**: Run during the `code-analysis` job
2. **Build Metrics**: Recorded at the end of the `build` job
3. **Deployment Tracking**: Executed as part of the `deploy` job
4. **Notifications**: Sent via the `notifications` job 