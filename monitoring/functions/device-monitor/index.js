const { BigQuery } = require('@google-cloud/bigquery');
const { Firestore } = require('@google-cloud/firestore');
const { v4: uuidv4 } = require('uuid');

/**
 * Cloud Function that records device heartbeats for monitoring
 * and stores the data in both Firestore (real-time) and BigQuery (analytics)
 */
exports.recordDeviceHeartbeat = async (req, res) => {
  // Initialize clients
  const bigquery = new BigQuery();
  const firestore = new Firestore();
  
  // Enable CORS
  res.set('Access-Control-Allow-Origin', '*');
  
  if (req.method === 'OPTIONS') {
    res.set('Access-Control-Allow-Methods', 'POST');
    res.set('Access-Control-Allow-Headers', 'Content-Type');
    res.status(204).send('');
    return;
  }
  
  // Validate request
  const {deviceId, firmwareVersion, status, metrics} = req.body;
  if (!deviceId || !firmwareVersion) {
    console.error('Missing required parameters');
    res.status(400).send('Missing required parameters: deviceId and firmwareVersion are required');
    return;
  }
  
  const timestamp = new Date();
  const heartbeatId = uuidv4();
  
  console.log(`Received heartbeat from device ${deviceId} running firmware ${firmwareVersion}`);
  console.log(`Status: ${status || 'online'}, Timestamp: ${timestamp.toISOString()}`);
  
  try {
    // Record heartbeat in Firestore for real-time tracking
    await firestore.collection('devices').doc(deviceId).set({
      lastHeartbeat: timestamp,
      firmwareVersion,
      status: status || 'online',
      metrics: metrics || {},
      lastSeen: timestamp,
      ipAddress: req.ip || 'unknown'
    }, {merge: true});
    
    // Record device details if not already present
    const deviceDoc = await firestore.collection('devices').doc(deviceId).get();
    if (!deviceDoc.exists || !deviceDoc.data().registered) {
      await firestore.collection('devices').doc(deviceId).set({
        firstSeen: timestamp,
        registered: true,
        registrationDate: timestamp
      }, {merge: true});
      console.log(`Registered new device: ${deviceId}`);
    }
    
    // Record heartbeat in BigQuery for analytics
    await bigquery.dataset('device_metrics').table('heartbeats').insert([{
      heartbeat_id: heartbeatId,
      device_id: deviceId,
      firmware_version: firmwareVersion,
      timestamp: timestamp.toISOString(),
      status: status || 'online',
      ip_address: req.ip || 'unknown',
      ...(metrics ? flattenMetrics(metrics, 'metric_') : {})
    }]);
    
    // Send a successful response
    res.status(200).json({
      success: true,
      timestamp: timestamp.toISOString(),
      heartbeatId
    });
    
    console.log(`Successfully recorded heartbeat ${heartbeatId} for device ${deviceId}`);
    
  } catch (error) {
    console.error('Error recording device heartbeat:', error);
    res.status(500).send('Failed to record heartbeat');
  }
};

/**
 * Helper function to flatten nested metrics object for BigQuery
 * 
 * @param {Object} obj The metrics object to flatten
 * @param {string} prefix Prefix to add to keys
 * @returns {Object} Flattened object
 */
function flattenMetrics(obj, prefix = '') {
  return Object.keys(obj).reduce((acc, key) => {
    const newKey = prefix + key;
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      Object.assign(acc, flattenMetrics(obj[key], newKey + '_'));
    } else if (Array.isArray(obj[key])) {
      acc[newKey] = JSON.stringify(obj[key]);
    } else {
      acc[newKey] = obj[key];
    }
    return acc;
  }, {});
} 