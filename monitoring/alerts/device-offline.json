{"displayName": "Device Offline Alert", "combiner": "OR", "conditions": [{"displayName": "<PERSON><PERSON> has not sent heartbeat", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/device/heartbeat_count\" resource.type=\"global\"", "aggregations": [{"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_COUNT"}], "comparison": "COMPARISON_LT", "thresholdValue": 1, "duration": "300s", "trigger": {"count": 1}}}], "alertStrategy": {"autoClose": "604800s", "notificationRateLimit": {"period": "3600s"}}, "notificationChannels": ["EMAIL_CHANNEL_ID", "SLACK_CHANNEL_ID"], "documentation": {"content": "## Device Offline Alert\n\nThis alert is triggered when a device has not sent a heartbeat for 5 minutes.\n\n### Troubleshooting\n\n1. Check device power supply\n2. Verify network connectivity\n3. Check if the device is in maintenance mode\n4. Review device logs in Cloud Logging\n\n### Escalation\n\nIf unable to resolve within 30 minutes, escalate to on-call engineer.", "mimeType": "text/markdown"}}