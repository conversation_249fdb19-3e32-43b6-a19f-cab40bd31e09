{"displayName": "Deployment Failure Alert", "combiner": "OR", "conditions": [{"displayName": "Deployment status is failure", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/deployment/failure_count\" resource.type=\"global\"", "aggregations": [{"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_SUM"}], "comparison": "COMPARISON_GT", "thresholdValue": 0, "duration": "0s", "trigger": {"count": 1}}}], "alertStrategy": {"autoClose": "86400s", "notificationRateLimit": {"period": "3600s"}}, "notificationChannels": ["EMAIL_CHANNEL_ID", "SLACK_CHANNEL_ID"], "documentation": {"content": "## Deployment Failure Alert\n\nThis alert is triggered when a deployment to any environment fails.\n\n### Troubleshooting\n\n1. Check the deployment logs in GitHub Actions\n2. Verify that the GCP service account has sufficient permissions\n3. Confirm that the firmware builds correctly\n4. Verify connectivity to Cloud Storage\n\n### Remediation\n\n1. Fix the issue identified in the logs\n2. Retry the deployment manually if necessary\n3. Update permissions if it's an authentication issue\n\n### Escalation\n\nIf the deployment cannot be fixed within 2 hours, escalate to the system administrator.", "mimeType": "text/markdown"}}