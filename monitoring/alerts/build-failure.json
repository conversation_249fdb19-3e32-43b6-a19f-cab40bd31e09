{"displayName": "Build Failure Rate Alert", "combiner": "OR", "conditions": [{"displayName": "Build success rate below threshold", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/ci/build_success_rate\" resource.type=\"global\"", "aggregations": [{"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN"}], "comparison": "COMPARISON_LT", "thresholdValue": 0.8, "duration": "3600s", "trigger": {"count": 1}}}], "alertStrategy": {"autoClose": "86400s", "notificationRateLimit": {"period": "7200s"}}, "notificationChannels": ["EMAIL_CHANNEL_ID", "SLACK_CHANNEL_ID"], "documentation": {"content": "## Build Failure Rate Alert\n\nThis alert is triggered when the build success rate drops below 80% over a 1-hour period.\n\n### Troubleshooting\n\n1. Check recent commits for potential issues\n2. Review build logs in GitHub Actions\n3. Verify that all dependencies are available and correctly versioned\n4. Check for external service dependencies that might be unavailable\n\n### Remediation\n\n1. Revert recent changes if they are causing the issues\n2. Fix the failing tests or build steps\n3. If external dependencies are the issue, implement retry logic or fallbacks\n\n### Escalation\n\nIf unable to resolve within 1 hour, escalate to the lead developer.", "mimeType": "text/markdown"}}