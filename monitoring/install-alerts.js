#!/usr/bin/env node

/**
 * Alert Policy Installation Script
 * 
 * This script reads JSON alert policy configurations from the alerts directory
 * and creates or updates them in Cloud Monitoring.
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

// Configuration
const PROJECT_ID = 'cannasol-automation-suite';
const ALERTS_DIR = path.join(__dirname, 'alerts');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m'
};

/**
 * Read a JSON file and return its contents as an object
 * @param {string} filePath - Path to the JSON file
 * @returns {Object} - Parsed JSON content
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`${colors.red}Error reading file ${filePath}:${colors.reset}`, error);
    return null;
  }
}

/**
 * Create or update an alert policy
 * @param {string} filePath - Path to the alert policy JSON file
 */
async function installAlertPolicy(filePath) {
  const fileName = path.basename(filePath);
  console.log(`${colors.blue}Processing ${fileName}...${colors.reset}`);
  
  const policy = readJsonFile(filePath);
  if (!policy) return;
  
  // Create a temporary file with processed policy content
  const tempFilePath = path.join(__dirname, `temp_${Date.now()}.json`);
  
  try {
    // Get notification channel IDs
    const { stdout: channelsOutput } = await execPromise(
      `gcloud monitoring channels list --project=${PROJECT_ID} --format=json`
    );
    
    const channels = JSON.parse(channelsOutput);
    const emailChannel = channels.find(c => c.type === 'email');
    const slackChannel = channels.find(c => c.type === 'slack');
    
    // Replace placeholder channel IDs with actual IDs
    const processedPolicy = { ...policy };
    if (processedPolicy.notificationChannels) {
      processedPolicy.notificationChannels = processedPolicy.notificationChannels.map(channel => {
        if (channel === 'EMAIL_CHANNEL_ID' && emailChannel) {
          return emailChannel.name;
        } else if (channel === 'SLACK_CHANNEL_ID' && slackChannel) {
          return slackChannel.name;
        }
        return channel;
      }).filter(Boolean);
    }
    
    // Write processed policy to temp file
    fs.writeFileSync(tempFilePath, JSON.stringify(processedPolicy, null, 2));
    
    // Check if the policy already exists
    const { stdout: existingPolicies } = await execPromise(
      `gcloud alpha monitoring policies list --project=${PROJECT_ID} --filter="display_name='${policy.displayName}'" --format=json`
    );
    
    const policies = JSON.parse(existingPolicies);
    
    if (policies.length > 0) {
      // Update existing policy
      const policyName = policies[0].name;
      await execPromise(
        `gcloud alpha monitoring policies update ${policyName} --project=${PROJECT_ID} --policy-from-file=${tempFilePath}`
      );
      console.log(`${colors.yellow}Alert policy '${policy.displayName}' updated.${colors.reset}`);
    } else {
      // Create new policy
      await execPromise(
        `gcloud alpha monitoring policies create --project=${PROJECT_ID} --policy-from-file=${tempFilePath}`
      );
      console.log(`${colors.green}Alert policy '${policy.displayName}' created.${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}Error installing alert policy ${fileName}:${colors.reset}`, error.message);
  } finally {
    // Clean up temp file
    if (fs.existsSync(tempFilePath)) {
      fs.unlinkSync(tempFilePath);
    }
  }
}

/**
 * Main function to install all alert policies
 */
async function main() {
  console.log(`${colors.blue}Installing alert policies for project ${PROJECT_ID}...${colors.reset}`);
  
  try {
    // Ensure alerts directory exists
    if (!fs.existsSync(ALERTS_DIR)) {
      console.error(`${colors.red}Alerts directory not found: ${ALERTS_DIR}${colors.reset}`);
      process.exit(1);
    }
    
    // Get all JSON files in the alerts directory
    const files = fs.readdirSync(ALERTS_DIR)
      .filter(file => file.endsWith('.json'))
      .map(file => path.join(ALERTS_DIR, file));
    
    if (files.length === 0) {
      console.log(`${colors.yellow}No alert policy files found in ${ALERTS_DIR}${colors.reset}`);
      process.exit(0);
    }
    
    // Process each alert policy file
    for (const file of files) {
      await installAlertPolicy(file);
    }
    
    console.log(`${colors.green}Alert policy installation complete.${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}Error installing alert policies:${colors.reset}`, error);
    process.exit(1);
  }
}

// Run the main function
main(); 