#!/bin/bash
# monitoring/setup.sh
# Script to set up monitoring and alerts for the ATTINY control system

# Exit on error
set -e

# Configuration
PROJECT_ID="cannasol-automation-suite"
REGION="us-central1"

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up monitoring and alerts for ATTINY Control System...${NC}"

# Ensure gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" > /dev/null 2>&1; then
  echo -e "${RED}Error: No active gcloud account found. Please run 'gcloud auth login' first.${NC}"
  exit 1
fi

# Set default project
echo -e "${GREEN}Setting default project to ${PROJECT_ID}...${NC}"
gcloud config set project ${PROJECT_ID}

# Create BigQuery dataset for CI metrics
echo -e "${GREEN}Creating BigQuery dataset for CI metrics...${NC}"
if ! bq ls --project_id=${PROJECT_ID} | grep -q ci_metrics; then
  bq --location=${REGION} mk \
    --dataset \
    --description="ATTINY CI/CD Metrics" \
    ${PROJECT_ID}:ci_metrics
else
  echo -e "${YELLOW}BigQuery dataset already exists, skipping creation.${NC}"
fi

# Create BigQuery tables
echo -e "${GREEN}Creating BigQuery tables for CI metrics...${NC}"
if ! bq ls --project_id=${PROJECT_ID} ci_metrics | grep -q builds; then
  bq mk \
    --table \
    --description="CI/CD Build Metrics" \
    ${PROJECT_ID}:ci_metrics.builds \
    build_id:STRING,build_number:INTEGER,commit:STRING,branch:STRING,start_time:TIMESTAMP,end_time:TIMESTAMP,duration:FLOAT,status:STRING,test_count:INTEGER,test_passed:INTEGER,test_failed:INTEGER,test_coverage:FLOAT
  echo -e "${GREEN}Created builds table${NC}"
fi

if ! bq ls --project_id=${PROJECT_ID} ci_metrics | grep -q deployments; then
  bq mk \
    --table \
    --description="CI/CD Deployment Metrics" \
    ${PROJECT_ID}:ci_metrics.deployments \
    version:STRING,environment:STRING,timestamp:TIMESTAMP,build_id:STRING,status:STRING,commit:STRING,deployer:STRING,repository:STRING
  echo -e "${GREEN}Created deployments table${NC}"
fi

# Create BigQuery dataset for device metrics
echo -e "${GREEN}Creating BigQuery dataset for device metrics...${NC}"
if ! bq ls --project_id=${PROJECT_ID} | grep -q device_metrics; then
  bq --location=${REGION} mk \
    --dataset \
    --description="ATTINY Device Metrics" \
    ${PROJECT_ID}:device_metrics
else
  echo -e "${YELLOW}BigQuery dataset already exists, skipping creation.${NC}"
fi

# Create table for device heartbeats
echo -e "${GREEN}Creating BigQuery table for device heartbeats...${NC}"
if ! bq ls --project_id=${PROJECT_ID} device_metrics | grep -q heartbeats; then
  bq mk \
    --table \
    --description="Device Heartbeat Metrics" \
    ${PROJECT_ID}:device_metrics.heartbeats \
    heartbeat_id:STRING,device_id:STRING,firmware_version:STRING,timestamp:TIMESTAMP,status:STRING,ip_address:STRING,region:STRING
  echo -e "${GREEN}Created heartbeats table${NC}"
fi

# Install Node.js dependencies for tools
echo -e "${GREEN}Installing Node.js dependencies for monitoring tools...${NC}"
(cd tools && npm install)

# Deploy device monitor Cloud Function
echo -e "${GREEN}Deploying device monitor Cloud Function...${NC}"
(cd functions/device-monitor && gcloud functions deploy device-monitor \
  --runtime nodejs14 \
  --trigger-http \
  --allow-unauthenticated \
  --entry-point recordDeviceHeartbeat)

# Create Cloud Monitoring dashboard
echo -e "${GREEN}Creating Cloud Monitoring dashboard...${NC}"
gcloud monitoring dashboards create --config-from-file=dashboard.json

# Setup email notification channel for alerts
echo -e "${GREEN}Creating email notification channel...${NC}"
EMAIL_CHANNEL_ID=$(gcloud alpha monitoring channels create \
  --display-name="ATTINY Engineering Email" \
  --type=email \
  --channel-labels=email_address=<EMAIL> \
  --format="value(name)")

echo -e "${GREEN}Email notification channel created with ID: ${EMAIL_CHANNEL_ID}${NC}"

# Setup Slack notification channel for alerts
echo -e "${GREEN}Creating Slack notification channel...${NC}"
if [ -n "${SLACK_WEBHOOK_URL}" ]; then
  SLACK_CHANNEL_ID=$(gcloud alpha monitoring channels create \
    --display-name="ATTINY Slack Notifications" \
    --type=slack \
    --channel-labels=url="${SLACK_WEBHOOK_URL}" \
    --format="value(name)")
  echo -e "${GREEN}Slack notification channel created with ID: ${SLACK_CHANNEL_ID}${NC}"
else
  echo -e "${YELLOW}SLACK_WEBHOOK_URL not set, skipping Slack channel creation.${NC}"
fi

# Install alert policies using the Node.js script
echo -e "${GREEN}Installing alert policies...${NC}"
chmod +x install-alerts.js
./install-alerts.js

echo -e "${GREEN}Monitoring and alerts setup complete.${NC}"
echo -e "${YELLOW}To simulate device heartbeats, run: ./tools/simulate-device.js${NC}" 