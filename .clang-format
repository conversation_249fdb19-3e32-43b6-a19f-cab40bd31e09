---
Language: Cpp
# Base style for our project
BasedOnStyle: Google
# Indentation settings
IndentWidth: 4
TabWidth: 4
UseTab: Never
# Bracket settings
BreakBeforeBraces: Allman
# Line length and breaks
ColumnLimit: 100
# Parameter and argument formatting
AllowAllParametersOfDeclarationOnNextLine: true
BinPackArguments: false
BinPackParameters: false
# Pointer alignment
PointerAlignment: Left
# Comments
AlignTrailingComments: true
# Class and namespace formatting
NamespaceIndentation: None
AccessModifierOffset: -4
# Templates
AlwaysBreakTemplateDeclarations: Yes
# Includes
IncludeBlocks: Regroup
IncludeCategories:
  - Regex:           '^<.*\.h>'
    Priority:        1
  - Regex:           '^<.*'
    Priority:        2
  - Regex:           '"modules/.*"'
    Priority:        3
  - Regex:           '".*"'
    Priority:        4
# Function declaration formatting
AllowShortFunctionsOnASingleLine: Empty
AlignAfterOpenBracket: Align
# Class formatting
BreakConstructorInitializers: BeforeColon
# Control flow spacing
SpaceBeforeParens: ControlStatements
# Preprocessor directives
IndentPPDirectives: AfterHash
# Misc
SortIncludes: true
--- 