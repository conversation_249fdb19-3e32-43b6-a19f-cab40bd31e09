/*
  ArduinoISP
  Modified for Arduino R4 WiFi

  This sketch turns the Arduino into a AVRISP using the following Arduino pins:
    D10: slave reset (needed for Arduino as ISP)
    D11: MOSI -> ATtiny pin 5
    D12: MISO -> ATtiny pin 6
    D13: SCK  -> ATtiny pin 7
*/

// Standard Arduino ISP pinout
#define RESET     10
#define LED_HB    9
#define LED_ERR   8
#define LED_PMODE 7

// Configure heartbeat LED to show the programmer is running
#define HEARTBEAT_STEP 40
#define HEARTBEAT_CYCLE 2

// Configure programming parameters
#define PROG_FLICKER true
#define SPI_CLOCK (1000000/6)
#define BAUDRATE 19200 // Try 19200 first, then 9600 if issues

#include "Arduino.h"
#include "SPI.h"

// Blink functions for visual feedback
void heartbeat() {
  static unsigned long last_time = 0;
  static unsigned char hbval = 128;
  static unsigned char hbdelta = HEARTBEAT_STEP;
  unsigned long now = millis();
  if ((now - last_time) < 40)
    return;
  last_time = now;
  
  if (hbval > (255 - HEARTBEAT_STEP))
    hbdelta = -HEARTBEAT_STEP;
  else if (hbval < HEARTBEAT_STEP)
    hbdelta = HEARTBEAT_STEP;
  hbval += hbdelta;
  analogWrite(LED_HB, hbval);
}

void setup() {
  // Configure LED pin outputs
  pinMode(LED_PMODE, OUTPUT);
  pinMode(LED_ERR, OUTPUT);
  pinMode(LED_HB, OUTPUT);

  // Configure RESET pin
  pinMode(RESET, OUTPUT);
  digitalWrite(RESET, HIGH);

  // Setup serial communication
  Serial.begin(BAUDRATE);
  while(!Serial); // Wait for serial port to connect
  Serial.println("Arduino R4 WiFi ISP Ready");

  // Initialize SPI
  SPI.begin();
  // Slow down the SPI clock for more reliable programming
  SPI.setClockDivider(SPI_CLOCK_DIV128);

  // Display ready signal
  digitalWrite(LED_PMODE, HIGH);
  delay(100);
  digitalWrite(LED_PMODE, LOW);
  delay(100);
  digitalWrite(LED_PMODE, HIGH);
  delay(100);
  digitalWrite(LED_PMODE, LOW);
  
  // Flash onboard LEDs to indicate ISP is ready
  for (int i = 0; i < 3; i++) {
    digitalWrite(LED_PMODE, HIGH);
    delay(50);
    digitalWrite(LED_PMODE, LOW);
    delay(50);
  }
}

void loop() {
  // Display heartbeat to show the programmer is running
  heartbeat();
  
  // Check if the serial port has any data waiting to be processed
  if (Serial.available()) {
    digitalWrite(LED_PMODE, HIGH);
    avrisp();
    digitalWrite(LED_PMODE, LOW);
  }
}

// Define the SPI protocol commands
#define HVSP_INSTR_SIZE 3
#define CRC_EOP 0x20
#define STK_OK 0x10
#define STK_FAILED 0x11
#define STK_UNKNOWN 0x12
#define STK_INSYNC 0x14
#define STK_NOSYNC 0x15
#define STK_GET_SYNC 0x30
#define STK_GET_PARAMETER 0x41
#define STK_SET_DEVICE 0x42
#define STK_SET_DEVICE_EXT 0x45
#define STK_ENTER_PROGMODE 0x50
#define STK_LEAVE_PROGMODE 0x51
#define STK_CHIP_ERASE 0x52
#define STK_CHECK_AUTOINC 0x53
#define STK_LOAD_ADDRESS 0x55
#define STK_UNIVERSAL 0x56
#define STK_PROG_FLASH 0x60
#define STK_PROG_DATA 0x61
#define STK_PROG_FUSE 0x62
#define STK_PROG_LOCK 0x63
#define STK_PROG_PAGE 0x64
#define STK_PROG_FUSE_EXT 0x65
#define STK_READ_FLASH 0x70
#define STK_READ_DATA 0x71
#define STK_READ_FUSE 0x72
#define STK_READ_LOCK 0x73
#define STK_READ_PAGE 0x74
#define STK_READ_SIGN 0x75
#define STK_READ_OSCCAL 0x76
#define STK_READ_FUSE_EXT 0x77
#define STK_READ_OSCCAL_EXT 0x78

#define STK_HW_VER 0x80
#define STK_SW_MAJOR 0x81
#define STK_SW_MINOR 0x82

// ISP state variables
static uint8_t here;
static uint8_t buff[256];
static uint16_t currentAddr;
static uint8_t pmode = 0;
static bool rst_active_high;

// Functions to communicate with the target microcontroller
void reset_target(bool reset) {
  digitalWrite(RESET, reset ? LOW : HIGH);
}

void spi_transaction(uint8_t a, uint8_t b, uint8_t c, uint8_t d) {
  SPI.transfer(a);
  SPI.transfer(b);
  SPI.transfer(c);
  SPI.transfer(d);
}

uint8_t spi_transaction(uint8_t a, uint8_t b, uint8_t c) {
  SPI.transfer(a);
  SPI.transfer(b);
  SPI.transfer(c);
  return SPI.transfer(0);
}

void empty_reply() {
  if (Serial.available() > 0) {
    Serial.read();
  }
  Serial.write(STK_NOSYNC);
}

void breply(uint8_t b) {
  if (Serial.available() > 0) {
    Serial.read();
  }
  Serial.write(STK_INSYNC);
  Serial.write(b);
  Serial.write(STK_OK);
}

// Utility function to get command bytes from serial
uint8_t getch() {
  while (!Serial.available());
  return Serial.read();
}

void fill(int n) {
  for (int x = 0; x < n; x++) {
    buff[x] = getch();
  }
}

// Reset functions
void pulse(int pin, int times) {
  do {
    digitalWrite(pin, HIGH);
    delay(30);
    digitalWrite(pin, LOW);
    delay(30);
  } while (--times);
}

void prog_lamp(int state) {
  if (PROG_FLICKER) {
    digitalWrite(LED_PMODE, state);
  }
}

// Target device programming functions
void start_pmode() {
  // Reset target before programming
  SPI.begin();
  SPI.setClockDivider(SPI_CLOCK_DIV128);
  digitalWrite(RESET, HIGH);
  pinMode(RESET, OUTPUT);
  delay(50);
  reset_target(true);
  delay(50);
  SPI.transfer(0xAC);
  SPI.transfer(0x53);
  SPI.transfer(0x00);
  SPI.transfer(0x00);
  pmode = 1;
}

void end_pmode() {
  SPI.end();
  reset_target(false);
  pmode = 0;
}

// Main AVR ISP function
void avrisp() {
  uint8_t ch = getch();
  switch (ch) {
    case STK_GET_SYNC:
      if (getch() == CRC_EOP) {
        Serial.write(STK_INSYNC);
        Serial.write(STK_OK);
      } else {
        digitalWrite(LED_ERR, HIGH);
        empty_reply();
        digitalWrite(LED_ERR, LOW);
      }
      break;
      
    case STK_GET_PARAMETER:
      {
        uint8_t param = getch();
        if (getch() == CRC_EOP) {
          uint8_t value;
          switch(param) {
            case STK_HW_VER:
              value = 2;
              break;
            case STK_SW_MAJOR:
              value = 1;
              break;
            case STK_SW_MINOR:
              value = 20;
              break;
            default:
              value = 0;
              break;
          }
          breply(value);
        } else {
          empty_reply();
        }
      }
      break;
      
    case STK_SET_DEVICE:
      fill(20);
      if (getch() == CRC_EOP) {
        breply(STK_OK);
      } else {
        empty_reply();
      }
      break;
      
    case STK_SET_DEVICE_EXT:
      fill(5);
      if (getch() == CRC_EOP) {
        breply(STK_OK);
      } else {
        empty_reply();
      }
      break;
      
    case STK_ENTER_PROGMODE:
      if (getch() == CRC_EOP) {
        start_pmode();
        breply(STK_OK);
      } else {
        empty_reply();
      }
      break;
      
    case STK_LEAVE_PROGMODE:
      if (getch() == CRC_EOP) {
        end_pmode();
        breply(STK_OK);
      } else {
        empty_reply();
      }
      break;
      
    case STK_LOAD_ADDRESS:
      currentAddr = getch();
      currentAddr += getch() << 8;
      if (getch() == CRC_EOP) {
        breply(STK_OK);
      } else {
        empty_reply();
      }
      break;
      
    case STK_UNIVERSAL:
      {
        uint8_t a = getch();
        uint8_t b = getch();
        uint8_t c = getch();
        uint8_t d = getch();
        if (getch() == CRC_EOP) {
          uint8_t res = spi_transaction(a, b, c, d);
          breply(res);
        } else {
          empty_reply();
        }
      }
      break;
      
    case STK_PROG_PAGE:
      {
        uint8_t desttype = getch();
        uint16_t length = getch() << 8;
        length += getch();
        uint8_t memtype = getch();
        
        // Flash memory @currentAddr, (length) bytes
        if (getch() == CRC_EOP) {
          for (uint16_t i = 0; i < length; i++) {
            uint8_t cmd;
            switch (memtype) {
              case 'F': // flash memory
                cmd = (i & 1) ? 0x48 : 0x40;
                break;
              case 'E': // EEPROM
                cmd = 0xC0;
                break;
              default:
                cmd = 0x00;
            }
            spi_transaction(cmd, currentAddr >> 8, currentAddr & 0xFF, buff[i]);
            currentAddr++;
          }
          // Read command terminator, start reply
          breply(STK_OK);
        } else {
          empty_reply();
        }
      }
      break;
      
    case STK_READ_PAGE:
      {
        uint8_t desttype = getch();
        uint16_t length = getch() << 8;
        length += getch();
        uint8_t memtype = getch();
        
        if (getch() == CRC_EOP) {
          Serial.write(STK_INSYNC);
          for (uint16_t i = 0; i < length; i++) {
            uint8_t cmd;
            switch (memtype) {
              case 'F': // flash memory
                cmd = 0x20;
                break;
              case 'E': // EEPROM
                cmd = 0xA0;
                break;
              default:
                cmd = 0x00;
            }
            Serial.write(spi_transaction(cmd, currentAddr >> 8, currentAddr & 0xFF, 0));
            currentAddr++;
          }
          Serial.write(STK_OK);
        } else {
          empty_reply();
        }
      }
      break;
      
    case STK_READ_SIGN:
      if (getch() == CRC_EOP) {
        Serial.write(STK_INSYNC);
        Serial.write(spi_transaction(0x30, 0x00, 0x00, 0x00));
        Serial.write(spi_transaction(0x30, 0x00, 0x01, 0x00));
        Serial.write(spi_transaction(0x30, 0x00, 0x02, 0x00));
        Serial.write(STK_OK);
      } else {
        empty_reply();
      }
      break;
      
    default:
      digitalWrite(LED_ERR, HIGH);
      if (getch() == CRC_EOP) {
        Serial.write(STK_INSYNC);
        Serial.write(STK_UNKNOWN);
        Serial.write(STK_OK);
      } else {
        empty_reply();
      }
      digitalWrite(LED_ERR, LOW);
      break;
  }
} 