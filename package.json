{"name": "attiny-control-ci-monitor", "version": "1.0.0", "description": "CI/CD Pipeline Monitoring for ATtiny85 Control System", "main": "scripts/pipeline_dashboard.js", "scripts": {"start": "node scripts/pipeline_dashboard.js", "store": "node scripts/store_pipeline_run.js", "setup": "npm install && mkdir -p scripts/views scripts/public"}, "dependencies": {"axios": "^1.6.0", "ejs": "^3.1.9", "express": "^4.18.2", "firebase": "^10.6.0", "dotenv": "^16.3.1"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/Cannasol-Tech/attiny-control.git"}, "keywords": ["ci", "cd", "pipeline", "monitoring", "attiny85"], "author": "Cannasol Technologies", "license": "MIT"}