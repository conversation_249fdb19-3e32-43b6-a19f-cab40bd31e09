# CI/CD Pipeline Documentation

This document describes the Continuous Integration and Continuous Deployment (CI/CD) pipeline for the ATtiny85 Control System project.

## Overview

The CI/CD pipeline automatically builds, tests, and publishes firmware for the ATtiny85 Control System. It is triggered on:
- Pushes to the `main` branch
- Pull requests targeting the `main` branch
- Manual trigger via GitHub Actions UI

## Pipeline Stages

The pipeline consists of the following stages:

1. **Unit Tests**: Compiles and runs unit tests in a native environment
2. **Integration Tests**: Runs integration tests using SimulAVR emulator
3. **Build Firmware**: Compiles firmware for the ATtiny85 microcontroller
4. **Publish Test Report**: Aggregates and publishes test reports

## Unit Tests

The unit tests stage now uses a custom testing approach with the following components:

- **Unity Test Framework**: A lightweight C testing framework
- **Custom Mock Implementation**: Mock Arduino and EEPROM functionality for testing
- **Test Runner Script**: A bash script (`test/run_unit_tests.sh`) that compiles and runs the tests

Unit tests are designed to verify individual modules in isolation without requiring actual hardware.

### Running Unit Tests Locally

To run unit tests locally:

```bash
./test/run_unit_tests.sh           # Run all unit tests
./test/run_unit_tests.sh test_name # Run a specific test
```

## Integration Tests

Integration tests verify the behavior of the full system using the SimulAVR emulator. These tests require:

- SimulAVR emulator installed
- PlatformIO with the emulator environment configured

Integration tests take longer to run but provide more comprehensive testing of system behavior.

## Build Process

The build process compiles the firmware for the ATtiny85 microcontroller using:

- PlatformIO build system
- AVR GCC toolchain
- Project configuration from `platformio.ini`

The output is a firmware.hex file that can be programmed onto the ATtiny85.

## Test Reports

After running the tests, the pipeline generates:

- JUnit XML reports for integration with CI/CD systems
- HTML reports for human-readable test results
- Combined reports that aggregate results from all test stages

These reports are published as artifacts and can be accessed from the GitHub Actions UI.

## Troubleshooting

If you encounter issues with the CI/CD pipeline:

1. Check that all dependencies are correctly installed
2. Ensure tests run correctly in your local environment
3. Review the GitHub Actions logs for specific error messages
4. Check the simulator configuration if integration tests are failing

## Recent Improvements

The following improvements have been made to the CI/CD pipeline:

- Replaced PlatformIO test runner with a custom solution for unit tests
- Improved mock implementation for Arduino and EEPROM
- Enhanced test reporting with better HTML output
- Fixed compatibility issues between different test environments
- Added detailed documentation about the CI/CD process 