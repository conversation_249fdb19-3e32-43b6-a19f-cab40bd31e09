# Arduino ISP Troubleshooting Guide

## Programming Issues with ATtiny85

If you're seeing errors like `programmer is not responding` or `not in sync` when trying to program the ATtiny85, try these steps:

## 1. Check the Arduino ISP Sketch

First, ensure the Arduino R4 WiFi has the proper ArduinoISP sketch loaded:

1. Open the Arduino IDE
2. Go to File > Examples > 11.ArduinoISP > ArduinoISP
3. Make the following change to the sketch for newer Arduino boards:

```cpp
// Find this line:
#define RESET SS

// Change it to:
#define RESET 10  // Use pin 10 for RESET
```

4. Upload this sketch to your Arduino R4 WiFi
5. After uploading, wait a few seconds for the Arduino to reset

## 2. Connection Issues

- Double-check all wiring connections between Arduino and ATtiny85
- Ensure stable connections - loose wires are a common cause of programming failures
- Try adding a 10μF capacitor between RESET and GND on the Arduino (positive to RESET)
- Try using a breadboard for more stable connections
- Make sure the ATtiny85 is oriented correctly (pin 1/reset at top left when notch is facing left)

## 3. Hardware Issues

- Try a different USB cable
- Ensure the Arduino is recognized by your computer
- Try a different USB port
- Check that the ATtiny85 is not damaged

## 4. Software Settings

- Try different baud rates (we've already lowered to 9600 from 19200)
- Add the -F flag to force programming (already done)
- Try verbose output for detailed debugging (already added)

## 5. Reset Timing

Sometimes there's a timing issue between the Arduino and the ATtiny85. Try:

1. Pressing the reset button on the Arduino just before uploading
2. Adjusting the capacitor value if you're using one

## 6. Alternative Approach

If all else fails, you can try:

1. Using the Arduino IDE directly with Arduino as ISP
2. Using a dedicated hardware programmer like USBasp 