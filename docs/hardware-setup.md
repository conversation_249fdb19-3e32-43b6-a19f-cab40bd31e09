# ATTINY85 Control System Hardware Setup Guide

This guide provides instructions for setting up the physical hardware for the ATTINY85 Control System.

## Components List

| Component | Quantity | Notes |
|-----------|----------|-------|
| ATtiny85 Microcontroller | 1 | 8-pin DIP or SOIC package |
| 8MHz Crystal or Resonator | 1 | External clock source |
| 22pF Ceramic Capacitors | 2 | For crystal circuit |
| 10kΩ Resistor | 1 | Pull-up for reset pin |
| 1kΩ Resistors | 2 | For LED indicators |
| 10µF Electrolytic Capacitor | 1 | Power supply decoupling |
| 0.1µF Ceramic Capacitor | 1 | Power supply decoupling |
| LEDs | 2 | Status indicators |
| Pump Relay | 1 | Capable of handling your pump's current |
| Air Solenoid Valve | 1 | 5V DC solenoid |
| 2N2222 or similar NPN Transistors | 2 | For driving relay and solenoid |
| 1N4001 Diodes | 2 | Flyback protection for inductive loads |
| Perf Board or PCB | 1 | For mounting components |
| USB-UART Adapter (optional) | 1 | For debugging (if using software UART) |
| Arduino (as ISP programmer) | 1 | For uploading firmware |

## Circuit Diagram

```
                           VCC
                            |
                           10kΩ
                            |
          +----------------+|+-------+
          |                 |        |
          |          Reset o|o VCC   |
     22pF =  8MHz     PB3  o|o PB2   |        Pump Relay
          =  XTAL     PB4  o|o PB1 o-+--XXXXX-|>|-------+
          |                 |               1kΩ  2N2222 |
          +----o--o-o GND  o|o PB0 o-+              |   /
                            |        |             |/  /
                            |       1kΩ     +-----|   /
                           --- 0.1µF |      |     |\  \
                           ---       |      |      |   |
                            |        +------|------|---+
                           GND      LED    GND    GND  VCC
                                                     |
                                   VCC               /
                                    |               /
                                    +-------XXXXX--|
                                          Air       |
                                         Solenoid   /
                                                   /
                                                  +
                                                 GND
```

## Pin Connections

| ATtiny85 Pin | Connection |
|--------------|------------|
| VCC (Pin 8)  | 5V power supply |
| GND (Pin 4)  | Ground |
| PB0 (Pin 5)  | Status LED (active high) |
| PB1 (Pin 6)  | Pump Relay Control (via transistor) |
| PB2 (Pin 7)  | Air Solenoid Control (via transistor) |
| PB3 (Pin 2)  | PWM Input Signal |
| PB4 (Pin 3)  | Spare I/O (can be used for sensors) |
| Reset (Pin 1) | Connect to VCC via 10kΩ resistor, and to programmer when needed |

## Assembly Instructions

1. **Prepare the ATtiny85**:
   - If using a blank ATtiny85, you'll need to set the fuses for external crystal/resonator
   - Use the Arduino as ISP to set fuses: `-U lfuse:w:0xf1:m -U hfuse:w:0xdf:m -U efuse:w:0xff:m`

2. **Assemble the circuit**:
   - Mount the ATtiny85 on your perf board or PCB
   - Connect the crystal and capacitors to pins 2 and 3
   - Add power supply decoupling capacitors (10µF and 0.1µF) between VCC and GND
   - Connect the pull-up resistor to the reset pin
   - Add the transistor circuits for the pump relay and air solenoid
   - Include flyback protection diodes across the relay and solenoid

3. **Connect inputs and outputs**:
   - Connect the PWM input signal to PB3
   - Connect the pump relay to the transistor controlled by PB1
   - Connect the air solenoid to the transistor controlled by PB2
   - Connect the status LED to PB0 through a current-limiting resistor

## Programming the ATtiny85

1. **Connect the programmer**:
   - Connect your Arduino (loaded with ArduinoISP sketch) to the ATtiny85
   - Typical connections include: 
     - Arduino pin 13 to ATtiny SCK (pin 7)
     - Arduino pin 12 to ATtiny MISO (pin 6)  
     - Arduino pin 11 to ATtiny MOSI (pin 5)
     - Arduino pin 10 to ATtiny RESET (pin 1)
     - Arduino 5V to ATtiny VCC (pin 8)
     - Arduino GND to ATtiny GND (pin 4)

2. **Upload the firmware**:
   ```bash
   # Using PlatformIO
   platformio run -e attiny85 -t upload
   
   # Using avrdude directly
   avrdude -c arduino -P [PORT] -b 19200 -p t85 -U flash:w:firmware.hex:i
   ```

3. **Verify the upload**:
   - The status LED should blink according to the programmed pattern
   - If available, monitor the device with a logic analyzer or oscilloscope

## Testing the Hardware

1. **Basic functionality test**:
   - Apply power to the circuit
   - Verify that the status LED illuminates
   - Provide a test PWM signal to the input pin
   - Verify that the pump and air solenoid activate as expected

2. **PWM response test**:
   - Generate PWM signals of varying duty cycles
   - Verify that the system responds correctly to each signal level
   - Test edge cases (very low and very high duty cycles)

3. **Timeout test**:
   - Verify that the air solenoid deactivates after the configured timeout period
   - Ensure that the system enters error state when timeout occurs

## Troubleshooting

### Common Issues

1. **Device not responding**:
   - Check power connections
   - Verify that fuses are set correctly
   - Check that the crystal and capacitors are properly connected

2. **Programming problems**:
   - Ensure the programmer is properly connected
   - Try a slower programming speed: `-B 1` option with avrdude
   - Verify the programmer is recognized by your computer

3. **Relay or solenoid not activating**:
   - Check transistor connections
   - Verify that flyback diodes are correctly oriented
   - Measure the voltage at the transistor base when activated

4. **System resets unexpectedly**:
   - Add additional power supply decoupling
   - Ensure relay and solenoid activations don't cause voltage drops
   - Consider adding a voltage regulator if power supply is unstable

### Electrical Safety

- Ensure all high-voltage connections (if any) are properly insulated
- Use appropriate fusing for power input
- Consider adding transient voltage suppressors for additional protection
- When using relays to control AC loads, ensure proper isolation between low-voltage and high-voltage sides

## Maintenance

- Regularly check connections, especially at the relay and solenoid
- Clean the circuit board of dust and debris
- Monitor for any signs of overheating components
- Test the system periodically to ensure continued proper operation

## Advanced Configuration

For advanced users, the system can be modified for additional functionality:

- Add sensors to monitor system performance
- Implement additional status indicators
- Use the spare I/O pin for expanded functionality
- Connect a serial interface for debugging and monitoring

For further assistance, please file an issue on the GitHub repository. 