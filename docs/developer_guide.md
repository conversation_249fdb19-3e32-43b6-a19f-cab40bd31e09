# Developer Guide

## Development Environment Setup
1. **Required Tools**:
   - PlatformIO IDE for development and building
   - AVR-GCC compiler for ATtiny85
   - Unity for unit testing
   - SimulAVR for emulation testing
   - Doxygen for documentation generation

2. **Building the Project**:
   ```bash
   # Clone the repository
   git clone https://github.com/cannasol-technologies/attiny-control.git
   cd attiny-control
   
   # Build using PlatformIO
   pio run
   ```

3. **Uploading to ATTINY85**:
   ```bash
   # Upload firmware to ATtiny85
   pio run --target upload
   ```

## Code Organization
- **src/**: Source code files
  - **src/modules/**: Module implementation files
  - **src/main.cpp**: Program entry point
- **include/**: Header files
  - **include/modules/**: Module header files
- **test/**: Test code
  - **test/unit/**: Unit tests for each module
  - **test/emulation/**: Emulation tests using SimulAVR
- **docs/**: Documentation files
- **scripts/**: Build and utility scripts

## Test Process
1. **Unit Tests**: Verify individual module behavior
   ```bash
   # Run all unit tests
   pio test -e native
   ```

2. **Emulation Tests**: Test hardware behavior using SimulAVR
   ```bash
   # Run emulation tests
   pio test -e emulation
   ```

## Adding Features
1. Identify the appropriate module for your feature
2. Add function declarations to header file with proper Doxygen documentation
3. Implement functions in source file
4. Create unit and emulation tests for the new functionality
5. Update documentation
6. Submit a pull request

## Code Style Guidelines
- Use clear, descriptive variable and function names
- Document all functions with Doxygen comments
- Keep functions small and focused on a single task
- Follow the existing modular approach
- Use static class methods for module functions
- Add unit tests for all new functionality

## Documentation Standards
All code should include Doxygen documentation:

```cpp
/**
 * @file module_name.h
 * @brief Brief description of the module
 */

/**
 * @brief Function description
 * @param paramName Description of parameter
 * @return Description of return value
 */
```

## CI/CD Pipeline
The project uses GitHub Actions for continuous integration:

1. **Build**: All platforms are built automatically
2. **Test**: Unit and emulation tests are run
3. **Documentation**: Doxygen documentation is generated
4. **Release**: Tagged versions create releases automatically

## Debugging Techniques
1. **Using Serial Output**: When testing on real hardware
   ```cpp
   #ifdef DEBUG_MODE
   Serial.println("Debug message");
   #endif
   ```

2. **Using Emulation**: Output waveform capture files (VCD)
   ```bash
   # Run with VCD output
   pio test -e emulation --vcd
   ```

3. **Using Unit Tests**: Isolate and test specific functionality
   ```cpp
   TEST_CASE("Test specific functionality", "[module]") {
       // Test code here
   }
   ``` 