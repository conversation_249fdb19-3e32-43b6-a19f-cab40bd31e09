# ATtiny85 Programming Checklist

This checklist provides a structured approach to programming the ATtiny85 using an Arduino as ISP. Follow these steps in order for the best chance of success.

## Preparation

- [ ] Verify Arduino IDE is installed
- [ ] Verify PlatformIO is installed and working
- [ ] Ensure all connections are correct:
  - Arduino D13 → ATtiny85 pin 7 (SCK)
  - Arduino D12 → ATtiny85 pin 6 (MISO)
  - Arduino D11 → ATtiny85 pin 5 (MOSI)
  - Arduino D10 → ATtiny85 pin 1 (RESET)
  - Arduino 5V  → ATtiny85 pin 8 (VCC)
  - Arduino GND → ATtiny85 pin 4 (GND)
- [ ] Use a breadboard for stable connections
- [ ] Check orientation of ATtiny85 (pin 1 at top left when notch is facing left)
- [ ] Add a 10μF capacitor between RESET and GND on Arduino (+ to RESET)

## Arduino Setup

- [ ] Open Arduino IDE
- [ ] Go to File > Examples > 11.ArduinoISP > ArduinoISP
- [ ] Modify the sketch for Arduino R4 WiFi:
  ```cpp
  // Find this line:
  #define RESET SS
  
  // Change it to:
  #define RESET 10  // Use pin 10 for RESET
  ```
- [ ] Upload ArduinoISP sketch to Arduino
- [ ] Verify upload was successful (check serial monitor at 19200 baud)

## PlatformIO Configuration

- [ ] Verify platformio.ini has these settings:
  ```ini
  [env:attiny85]
  platform = atmelavr
  board = attiny85
  framework = arduino
  upload_protocol = arduino
  upload_port = /dev/tty.usbmodem* (your Arduino port)
  upload_flags = 
      -b9600  ; Slower baud rate for more reliable programming
      -F      ; Force programming without checking device signature
      -v      ; Verbose output for better troubleshooting
  ```

## Programming Process

- [ ] Build the firmware first: `platformio run`
- [ ] Fix any compilation errors
- [ ] Press reset button on Arduino just before uploading
- [ ] Run upload command: `platformio run -t upload`
- [ ] If the above fails, try direct avrdude command:
  ```
  avrdude -C ~/.platformio/packages/tool-avrdude/avrdude.conf -v -pattiny85 -cstk500v1 -P/dev/tty.usbmodem* -b9600 -e -Uflash:w:.pio/build/attiny85/firmware.hex:i -F
  ```

## Troubleshooting

If programming fails, try these steps in order:

1. **Hardware Issues:**
   - [ ] Re-check all connections
   - [ ] Try a different breadboard
   - [ ] Replace jumper wires
   - [ ] Try a different USB cable
   - [ ] Try a different USB port

2. **Reset Timing:**
   - [ ] Press reset on Arduino just as upload starts
   - [ ] Try different capacitor values (4.7μF, 10μF, 22μF)
   - [ ] Add a manual reset button circuit connecting Arduino RESET to GND

3. **Software Issues:**
   - [ ] Re-upload ArduinoISP sketch to Arduino
   - [ ] Try slower baud rates (9600 → 4800)
   - [ ] Try Arduino IDE instead of PlatformIO
   - [ ] Try the simple_program_attiny.sh script

4. **Alternative Methods:**
   - [ ] Use Arduino IDE with ATtiny board package
   - [ ] Try a dedicated AVR programmer

## Common Error Messages and Solutions

### "Programmer is not responding"
- This typically indicates communication issues between Arduino and computer
- Check USB connections and try a different port
- Re-upload the ArduinoISP sketch

### "Device signature check failed" or "Expected signature 0x1E930B"
- Force programming with -F flag
- Check if ATtiny85 is properly powered
- Verify connections are correct and stable

### "Not in sync" errors
- Reset timing issue - try pressing reset just before upload
- Check capacitor between RESET and GND
- Try lower baud rates

## Final Notes

- Programming AVR chips like the ATtiny85 can be finicky
- Ensure stable power to both Arduino and ATtiny85
- Use high-quality jumper wires and a good breadboard
- Patience is key - sometimes it takes multiple attempts 