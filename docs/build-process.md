# Build Process Verification Guide

This document outlines the processes for verifying the build system for the ATTINY85 Control System.

## Overview

The build process for the ATTINY85 Control System uses PlatformIO to compile the firmware for the ATtiny85 microcontroller. This guide covers verification methods, common issues, and best practices.

## Prerequisites

To build the firmware, you need:

1. **PlatformIO**: A cross-platform build system
   ```bash
   pip install platformio
   ```

2. **AVR Toolchain**: Compiler and tools for AVR microcontrollers
   ```bash
   # Ubuntu/Debian
   sudo apt-get install gcc-avr avr-libc
   ```

3. **Git**: For version control
   ```bash
   # Ubuntu/Debian
   sudo apt-get install git
   ```

## Build Environments

The project includes multiple build environments defined in `platformio.ini`:

1. **attiny85**: For building the production firmware
2. **native**: For running unit tests on the host machine
3. **emulator**: For running emulation tests with SimulAVR

## Verifying the Build Process

### Local Build Verification

1. **Clone the repository**:
   ```bash
   git clone https://github.com/Cannasol-Tech/attiny-control.git
   cd attiny-control
   ```

2. **Install PlatformIO dependencies**:
   ```bash
   platformio pkg install
   ```

3. **Build the firmware**:
   ```bash
   platformio run -e attiny85
   ```

4. **Check memory usage**:
   ```bash
   platformio run -e attiny85 --verbose
   ```
   Look for the memory usage summary at the end of the output:
   ```
   RAM:   [===       ]  27.3% (used 140 bytes from 512 bytes)
   Flash: [==        ]  19.7% (used 1610 bytes from 8192 bytes)
   ```

5. **Verify output files**:
   - Check that the following files exist:
     ```bash
     ls -la .pio/build/attiny85/
     ```
   - Confirm the presence of:
     - `firmware.elf`: ELF binary
     - `firmware.hex`: HEX file for uploading
     - `firmware.bin`: Binary file

### Dependency Verification

1. **Check library dependencies**:
   ```bash
   platformio pkg list
   ```

2. **Verify library compatibility**:
   ```bash
   platformio pkg update
   ```

3. **Check for outdated dependencies**:
   ```bash
   platformio pkg outdated
   ```

### Toolchain Verification

1. **Verify AVR GCC version**:
   ```bash
   avr-gcc --version
   ```
   Ensure it's compatible with our project (GCC 7.3.0 or later recommended).

2. **Check avrdude installation** (for uploading):
   ```bash
   avrdude -v
   ```

## Common Build Issues

### 1. Compilation Errors

**Issue**: Compiler reports syntax or semantic errors.

**Solutions**:
- Check for recent changes that might have introduced errors
- Ensure all required headers are included
- Verify compatibility with the C++ standard being used

### 2. Dependency Issues

**Issue**: Missing or incompatible libraries.

**Solutions**:
- Update PlatformIO: `pip install --upgrade platformio`
- Update library index: `platformio pkg update`
- Check for pinned library versions in `platformio.ini`

### 3. Memory Overflows

**Issue**: Program exceeds ATtiny85's memory constraints.

**Solutions**:
- Optimize code for size using `-Os` compiler flag
- Reduce string constants or move them to PROGMEM
- Consider simplifying logic or splitting functionality

### 4. Linker Issues

**Issue**: Undefined references or duplicate symbols.

**Solutions**:
- Check for circular dependencies
- Ensure proper header guards are used
- Verify that all required modules are included in the build

### 5. Toolchain Problems

**Issue**: Issues with the AVR toolchain.

**Solutions**:
- Reinstall the AVR toolchain
- Try a different toolchain version
- Check if the issue is environment-specific

## Build Verification Script

Add a script to verify the build process:

```bash
#!/bin/bash
# Build verification script

echo "Starting build verification..."

# Check PlatformIO installation
if ! command -v platformio &> /dev/null
then
    echo "PlatformIO not found! Please install it."
    exit 1
fi

# Check AVR toolchain
if ! command -v avr-gcc &> /dev/null
then
    echo "AVR GCC not found! Please install the AVR toolchain."
    exit 1
fi

# Clean previous builds
platformio run --target clean

# Build for each environment
for env in attiny85 native emulator
do
    echo "Building for environment: $env"
    platformio run -e $env || { echo "Build failed for $env!"; exit 1; }
done

# Check firmware size for ATtiny85
SIZE_OUTPUT=$(platformio run -e attiny85 --verbose 2>&1 | grep "RAM:")
echo "Memory usage: $SIZE_OUTPUT"

# Check if firmware fits in memory
if echo "$SIZE_OUTPUT" | grep -q "1[0-9][0-9]\.0%"; then
    echo "WARNING: Program may be too large for the ATtiny85!"
fi

echo "Build verification complete!"
```

Save this script as `scripts/verify_build.sh` and make it executable:
```bash
chmod +x scripts/verify_build.sh
```

## Build Environment Documentation

Create a document that details the build environment specifications to ensure reproducibility:

```markdown
# Build Environment Specification

## Toolchain Requirements
- PlatformIO: >= 6.0.0
- AVR GCC: >= 7.3.0
- AVR Libc: >= 2.0.0

## Library Dependencies
- Unity: 2.5.2
- (Add other dependencies here)

## Required Environment Variables
- None for basic build
- Optional: PLATFORMIO_BUILD_FLAGS for custom build flags

## Build Commands
- Production build: `platformio run -e attiny85`
- Unit tests: `platformio test -e native`
- Integration tests: `platformio test -e emulator`

## Compiler Flags
- Optimization level: -Os (optimize for size)
- C++ standard: C++11
- Warning flags: -Wall -Wextra

## Supported Platforms
- Linux (Ubuntu 20.04+)
- macOS (10.15+)
- Windows (10+) with WSL recommended
```

## Continuous Integration

The CI/CD pipeline automatically verifies the build process on each push to the repository. It:

1. Compiles the firmware for the ATtiny85
2. Runs unit tests
3. Runs integration tests with SimulAVR
4. Collects artifacts and test reports

Review the CI/CD logs in GitHub Actions to verify the build process is working correctly in the CI environment. 