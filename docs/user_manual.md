# Cannasol ATTINY Control System
## User Manual

### Overview
This device controls air pressure and pump functionality based on inputs from a sonicator and PWM signals. The system runs on an ATTINY85 microcontroller.

### Connections
- **PIN PB0 (Air Out)**: Air control output (HIGH = ON, LOW = OFF)
- **PIN PB1 (PWM Out)**: PWM signal output for flow control
- **PIN PB2 (Pump Out)**: Pump control output (LOW = ON, HIGH = OFF)
- **PIN PB3 (Sonic In)**: Sonicator input (LOW = ON, HIGH = OFF)
- **PIN PB4 (PWM In)**: PWM signal input for flow control
- **PIN PB5 (Status Out)**: Status indicator output

### Operation
1. **PWM Flow Control**:
   - Input PWM signal on PIN 4 controls output PWM on PIN 1
   - Output PWM is capped at 77% duty cycle
   - When input exceeds 94% duty cycle, pump is automatically shut off

2. **Air Pressure Control**:
   - When sonicator is ON (LOW signal), air pressure is automatically turned ON
   - When sonicator turns OFF, air pressure remains ON for 5 minutes, then turns OFF
   - Air pressure state is saved to EEPROM for persistence across power cycles

### LED Indicators
- **Status LED**: Indicates system operation status

### Troubleshooting
- If pump doesn't turn on, check PWM input signal is below 94% duty cycle
- If air pressure doesn't turn on, verify sonicator signal is reaching PIN 3
- For persistent problems, check power supply and connections

### Maintenance
- No regular maintenance is required for the control system
- Ensure connections remain secure and free from corrosion
- Keep the device in a dry location protected from dust and debris

### Safety Precautions
- Disconnect power before making any connections to the device
- Do not expose the device to moisture or extreme temperatures
- Ensure proper ventilation around the device during operation
- Follow all local electrical and safety codes during installation 