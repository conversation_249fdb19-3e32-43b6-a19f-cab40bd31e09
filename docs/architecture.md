# System Architecture

## Overview
The ATTINY Control System manages PWM signals, air pressure, and pump control based on sensor inputs. It's designed to operate on an ATTINY85 microcontroller.

## Component Diagram
```mermaid
graph TD
  A[PWM Input] --> B[PWM Control Module]
  B --> C[PWM Output]
  B --> D[Pump Control]
  E[Sonicator Input] --> F[Air Pressure Control]
  F --> G[Air Output]
  H[EEPROM] <--> F
```

## Modules
1. **PWM Control** - Processes input PWM signals and controls output
2. **Air Pressure** - Manages air pressure based on sonicator input
3. **Pump Control** - Controls pump operation based on PWM signals
4. **Timer Manager** - Manages timing functionality for operation
5. **System** - Core system initialization and management
6. **Pin Configuration** - Defines pin assignments and functions

## Signal Flow
1. PWM input signal is read from PIN 4
2. Signal is processed and output to PIN 1 with duty cycle capped at 77%
3. When input duty cycle exceeds 94%, pump is turned off
4. When sonicator is ON (LOW signal), air pressure is turned ON
5. When sonicator is OFF, air pressure is turned OFF after 5 minutes

## Class Structure
- **PwmControl**: Manages PWM signal processing
  - Reads input PWM signals
  - Controls output PWM signals with duty cycle capping
  - Provides duty cycle information to other modules

- **AirControl**: Manages air pressure control
  - Monitors sonicator input
  - Controls air output
  - Uses EEPROM for persistence
  - Implements delayed shutdown

- **PumpControl**: Manages pump operation
  - Turns pump on/off based on PWM thresholds
  - Interfaces with PwmControl for input signals

- **TimerManager**: Handles timing operations
  - Manages the 5-minute delay for air control
  - Sets up and handles timer interrupts

- **System**: Core system functionality
  - Initializes all subsystems
  - Manages system-wide states

- **PinConfig**: Pin configuration and management
  - Defines all pin assignments
  - Provides access functions for pins 