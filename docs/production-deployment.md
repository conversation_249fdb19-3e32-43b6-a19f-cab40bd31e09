# ATTINY Control System Production Deployment

This document outlines the procedures and best practices for deploying the ATTINY Control System to production environments.

## Overview

The ATTINY Control System deployment process involves:

1. Building and testing firmware
2. Version management
3. Deployment to Cloud Storage
4. Device firmware update
5. Monitoring deployment status

## Prerequisites

- Access to Google Cloud Project (`cannasol-automation-suite`)
- Google Cloud SDK installed and configured
- GitHub repository access
- Required IAM permissions for deployment
- Signing keys for firmware

## Deployment Environments

The system supports three deployment environments:

| Environment | Purpose | Access | Approval |
|-------------|---------|--------|----------|
| Development | Active development | Developers | None required |
| Staging | Pre-production testing | QA Team | QA Lead |
| Production | Live devices | Limited Access | Product Owner + Engineering Lead |

## Deployment Process

### 1. Release Preparation

1. Ensure all tests pass in the CI/CD pipeline
2. Update version number in `firmware/version.h`
3. Create a release candidate branch (`release/v1.x.x-rc`)
4. Run the full integration test suite:
   ```
   test/integration/run_integration_tests.sh
   ```
5. Fix any issues and repeat until tests pass

### 2. Release Approval

1. Create a release approval request in the project management system
2. Include:
   - Version number
   - Release notes
   - Test results
   - Risk assessment
3. Required approvals depend on the target environment:
   - Staging: QA Lead approval
   - Production: Product Owner and Engineering Lead approval

### 3. Build Production Firmware

Release builds should be performed on the CI server to ensure reproducibility:

1. Trigger a release build by creating a release tag:
   ```
   git tag -a v1.x.x -m "Release v1.x.x"
   git push origin v1.x.x
   ```

2. The CI/CD pipeline will:
   - Build the firmware with production configuration
   - Run all tests
   - Generate release artifacts
   - Sign the firmware binary

### 4. Deploy to Cloud Storage

1. The automated deployment process uploads the firmware to Google Cloud Storage:
   ```
   ./deploy.sh --environment=production --version=v1.x.x
   ```

2. This script:
   - Uploads the firmware to the production bucket
   - Updates version metadata
   - Records deployment information in Firestore
   - Updates the deployment tracking in BigQuery

### 5. Gradual Rollout

For production deployments, a gradual rollout approach is used:

1. **Canary Deployment (5%)**
   ```
   ./deploy.sh --environment=production --version=v1.x.x --canary=5
   ```

2. **Monitoring Period (24 hours)**
   - Monitor device heartbeats
   - Check for errors or anomalies
   - Verify functionality with test devices

3. **Expanded Rollout (25%)**
   ```
   ./deploy.sh --environment=production --version=v1.x.x --rollout=25
   ```

4. **Full Deployment (100%)**
   ```
   ./deploy.sh --environment=production --version=v1.x.x --rollout=100
   ```

## Post-Deployment Verification

1. **Monitoring Dashboard Review**
   - Check device heartbeat metrics
   - Verify firmware versions reported
   - Monitor for error reports

2. **Sample Device Testing**
   - Test a sample of devices from each region
   - Verify functionality of all features
   - Collect performance metrics

3. **Sign-off Process**
   - QA team verification
   - Engineering sign-off
   - Product owner approval

## Rollback Procedure

If issues are detected during deployment, follow these rollback procedures:

### 1. Immediate Rollback

```
./rollback.sh --version=v1.x.x --target=v1.w.y
```

This script:
- Sets the previous version as the current production version
- Updates the firmware registry
- Notifies all devices to download the previous version

### 2. Partial Rollback

For issues affecting only specific regions or device types:

```
./rollback.sh --version=v1.x.x --target=v1.w.y --region=us-west --device-type=model-b
```

### 3. Post-Rollback Analysis

After a rollback:
1. Conduct root cause analysis
2. Document findings in the issue tracking system
3. Create mitigation plan for future releases
4. Update test cases to catch the issue going forward

## Release Management

### Version Numbering

The ATTINY Control System follows semantic versioning (MAJOR.MINOR.PATCH):

- **MAJOR**: Incompatible API changes
- **MINOR**: Backward-compatible functionality
- **PATCH**: Backward-compatible bug fixes

### Release Notes

Release notes must include:
1. Version number and release date
2. New features
3. Bug fixes
4. Known issues
5. Upgrade notes
6. Dependencies
7. Contributors

### Release Archiving

All releases are archived for future reference:
- Firmware binaries
- Source code (tagged in Git)
- Build artifacts
- Test results
- Deployment logs

## Security Considerations

1. **Firmware Signing**
   - All production firmware is cryptographically signed
   - Devices verify signature before installation

2. **Access Control**
   - Production deployment requires multi-party authorization
   - Audit logging tracks all deployment actions

3. **Credentials Management**
   - Deployment credentials are stored in a secure vault
   - Rotation schedule for all credentials

## Troubleshooting

### Common Deployment Issues

1. **Deployment Timeout**
   - Check network connectivity
   - Verify service account permissions
   - Check Cloud Storage quotas

2. **Verification Failures**
   - Review test logs for specific failures
   - Check device compatibility matrix
   - Verify firmware binary integrity

3. **Rollout Stalled**
   - Check monitoring alerts
   - Review device heartbeat data
   - Verify Cloud Function status

### Support Resources

- **Deployment Team**: <EMAIL>
- **On-call Engineer**: +1-555-123-4567
- **Status Page**: https://status.example.com/attiny-control 