# Technical Reference

## Hardware Specifications
- **Microcontroller**: ATTINY85
- **Clock Speed**: 8 MHz internal oscillator
- **Memory**: 8KB Flash, 512B EEPROM, 512B SRAM
- **Operating Voltage**: 2.7-5.5V

## Pin Assignments
| Pin | Name       | Direction | Description                      |
|-----|------------|-----------|----------------------------------|
| PB0 | AIR_OUT    | Output    | Air control (HIGH=ON, LOW=OFF)   |
| PB1 | PWM_OUT    | Output    | PWM signal output                |
| PB2 | PUMP_OUT   | Output    | Pump control (LOW=ON, HIGH=OFF)  |
| PB3 | SONIC_IN   | Input     | Sonicator input (LOW=ON)         |
| PB4 | PWM_IN     | Input     | PWM signal input                 |
| PB5 | STATUS_OUT | Output    | Status indicator LED             |

## EEPROM Usage
| Address | Size | Description      |
|---------|------|------------------|
| 0       | 1B   | Air status state |

## Timer Usage
- **Timer0**: Used for PWM output generation
- **Timer1**: Used for timing the 5-minute delay for air pressure

## Constants
| Name               | Value | Description                             |
|--------------------|-------|-----------------------------------------|
| INTERRUPT_COUNT_MAX| 579   | 5-minute countdown for air pressure     |
| PWM_DUTY_CYCLE_CAP | 77    | Maximum duty cycle percentage for output|
| PWM_PUMP_THRESHOLD | 94    | Duty cycle threshold to turn pump off   |

## Module Interfaces

### PWM Control Module
```cpp
static void init();                                 // Initialize PWM control
static byte getDutyCycle(byte pin);                 // Get PWM duty cycle from a pin
static void setOutput(byte percentage, byte cappedAt); // Set PWM output duty cycle
static byte processInputOutput();                   // Process input PWM and set output
```

### Air Control Module
```cpp
static void init();                     // Initialize air control system
static void turnOn();                   // Turn air pressure on
static void turnOff();                  // Turn air pressure off
static bool isOn();                     // Check if air pressure is on
static void process();                  // Process air pressure control logic
static void setStatus(bool status);     // Set the air status
```

### Pump Control Module
```cpp
static void init();                              // Initialize pump control
static void turnOn();                            // Turn pump on
static void turnOff();                           // Turn pump off
static bool isOn();                              // Check if pump is on
static void process(byte currentDutyCycle);      // Process pump control logic
```

### Timer Manager Module
```cpp
static void init();                                // Initialize timer manager
static void startAirOffTimer();                    // Start air off timer
static void stopAirOffTimer();                     // Stop air off timer
static bool isAirOffTimerRunning();                // Check if air off timer is running
static void resetAirOffTimerCount();               // Reset air off timer count
```

### System Module
```cpp
static void init();          // Initialize the system
static void process();       // Process system logic
```

## Error Handling
- The system uses implicit error handling with well-defined default behaviors
- No exceptions are thrown due to the embedded nature of the system
- Pin state changes are used to indicate error conditions
- Recovery mechanisms are built into each module for handling unexpected inputs 