# USBASP Programming Guide for ATtiny85

This guide covers programming the ATtiny85 using a USBASP programmer on macOS.

## Overview

USBASP is a USB-based AVR programmer that provides a simple and reliable way to program ATtiny85 microcontrollers. Unlike Arduino-as-ISP, USBASP is a dedicated programmer that doesn't require loading special firmware.

## Hardware Setup

### USBASP to ATtiny85 Connections

| USBASP Pin | ATtiny85 Pin | ATtiny85 Function |
|------------|--------------|-------------------|
| MOSI       | Pin 5        | PB0/MOSI          |
| MISO       | Pin 6        | PB1/MISO          |
| SCK        | Pin 7        | PB2/SCK           |
| RESET      | Pin 1        | RESET             |
| VCC        | Pin 8        | VCC (5V)          |
| GND        | Pin 4        | GND               |

### Important Notes

1. **Power Supply**: Most USBASP programmers can provide 5V power to the target. Check your USBASP's jumper settings.
2. **Voltage Levels**: Ensure USBASP is set for 5V operation (check jumper JP1 or similar).
3. **Connections**: Use short, reliable connections. Breadboard connections can sometimes cause issues.

## Software Setup

### Prerequisites

The following components are required and should already be installed:

- **PlatformIO**: For building and uploading firmware
- **avrdude**: For low-level programming (included with PlatformIO)
- **USBASP**: Connected and recognized by macOS

### Verification

Run the setup checker to verify everything is working:

```bash
./check_usbasp.sh
```

This script will:

- Check if USBASP is detected by macOS
- Verify avrdude is available
- Confirm PlatformIO is installed
- Test basic USBASP communication

## Programming Process

### Method 1: Using the Automated Script (Recommended)

1. **Connect Hardware**:
   - Connect USBASP to your Mac via USB
   - Connect ATtiny85 to USBASP using the pin mapping above
   - Ensure all connections are secure

2. **Run Programming Script**:

   ```bash
   ./program_attiny_usbasp.sh
   ```

   This script will:
   - Verify prerequisites
   - Build the firmware
   - Program the ATtiny85
   - Optionally set fuses

### Method 2: Using PlatformIO Directly

1. **Build Firmware**:

   ```bash
   platformio run -e attiny85_usbasp
   ```

2. **Upload Firmware**:

   ```bash
   platformio run -e attiny85_usbasp -t upload
   ```

### Method 3: Using avrdude Directly

1. **Build Firmware** (if not already built):

   ```bash
   platformio run -e attiny85_usbasp
   ```

2. **Program with avrdude**:

   ```bash
   avrdude -C ~/.platformio/packages/tool-avrdude/avrdude.conf \
           -v -pattiny85 -cusbasp \
           -e -Uflash:w:.pio/build/attiny85_usbasp/firmware.hex:i -F
   ```

## Fuse Settings

The ATtiny85 fuses configure the microcontroller's basic operation:

### Recommended Fuse Values

| Fuse | Value | Description |
|------|-------|-------------|
| Low  | 0xE2  | 8MHz internal oscillator, no prescaler |
| High | 0xD7  | Reset pin enabled, SPI programming enabled |
| Ext  | 0xFF  | Brown-out detection at 2.7V |

### Setting Fuses

```bash
avrdude -C ~/.platformio/packages/tool-avrdude/avrdude.conf \
        -v -pattiny85 -cusbasp \
        -U lfuse:w:0xE2:m -U hfuse:w:0xD7:m -U efuse:w:0xFF:m -F
```

## Troubleshooting

### Common Issues and Solutions

1. **"Device not found" or "Initialization failed"**
   - Check all connections between USBASP and ATtiny85
   - Ensure ATtiny85 has proper power (5V)
   - Try a different USB port or cable
   - Check USBASP jumper settings

2. **"avrdude: can't open config file"**
   - This is usually harmless if programming still works
   - The config file path in the error can be ignored

3. **Programming fails intermittently**
   - Add slower programming speed: `-B 4` flag to avrdude
   - Check for loose connections
   - Try a different ATtiny85 chip

4. **USBASP not detected by macOS**
   - Try a different USB port
   - Check if USBASP LED is on
   - Some USBASP clones may need driver installation

### Advanced Troubleshooting

1. **Test USBASP Connection**:

   ```bash
   avrdude -c usbasp -p attiny85
   ```

2. **Read Device Signature**:

   ```bash
   avrdude -c usbasp -p attiny85 -U signature:r:-:h
   ```

Should return: `0x1e930b` for ATtiny85

3. **Read Current Fuses**:

   ```bash
   avrdude -c usbasp -p attiny85 -U lfuse:r:-:h -U hfuse:r:-:h -U efuse:r:-:h
   ```

## Comparison with Arduino-as-ISP

| Feature | USBASP | Arduino-as-ISP |
|---------|--------|----------------|
| Setup Complexity | Simple | Requires ArduinoISP sketch |
| Reliability | High | Can be affected by Arduino issues |
| Speed | Fast | Slower due to serial communication |
| Cost | Low (~$5) | Requires Arduino board |
| Portability | Excellent | Requires Arduino setup |

## Production Programming

For programming multiple ATtiny85 chips:

1. Use a ZIF socket or programming jig for easy chip insertion/removal
2. Create a batch script that prompts for each chip
3. Consider using a dedicated programming fixture for high-volume production

## Safety Notes

- Always double-check connections before applying power
- Use proper ESD precautions when handling chips
- Verify fuse settings before programming multiple chips
- Keep backup copies of working firmware files

## Additional Resources

- [USBASP Official Documentation](https://www.fischl.de/usbasp/)
- [ATtiny85 Datasheet](https://ww1.microchip.com/downloads/en/DeviceDoc/Atmel-2586-AVR-8-bit-Microcontroller-ATtiny25-ATtiny45-ATtiny85_Datasheet.pdf)
- [avrdude Documentation](https://www.nongnu.org/avrdude/user-manual/avrdude.html)
