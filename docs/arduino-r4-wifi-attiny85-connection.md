# Arduino R4 WiFi to ATtiny85 ISP Connection Guide

For the Arduino R4 WiFi to ATtiny85 connection as an ISP, you'll need to connect these specific pins:

| Arduino R4 WiFi Pin | ATtiny85 Pin | Function  |
|---------------------|--------------|-----------|
| D13 (SCK)           | 7 (PB2)      | SCK       |
| D12 (MISO)          | 6 (PB1)      | MISO      |
| D11 (MOSI)          | 5 (PB0)      | MOSI      |
| D10 (SS)            | 1 (RESET)    | RESET     |
| 5V                  | 8 (VCC)      | Power     |
| GND                 | 4 (GND)      | Ground    |

```
                 ATtiny85
                ┌────U────┐
        RESET  1│•       8│ VCC
 (MOSI) PB3/A3 2│        7│ PB2/SCK
 (MISO) PB4/A2 3│        6│ PB1/MISO
         GND   4│        5│ PB0/MOSI
                └─────────┘

              Arduino R4 WiFi
       ┌───────────────────────┐
       │                       │
       │         D13 ●         │ SCK  → ATtiny pin 7
       │         D12 ●         │ MISO ← ATtiny pin 6
       │         D11 ●         │ MOSI → ATtiny pin 5
       │         D10 ●         │ SS   → ATtiny pin 1
       │          5V ●         │      → ATtiny pin 8
       │         GND ●         │      → ATtiny pin 4
       │                       │
       └───────────────────────┘
```

**Important steps:**

1. First, upload the ArduinoISP sketch to your R4 WiFi:
   - Open Arduino IDE
   - Go to File > Examples > ArduinoISP > ArduinoISP
   - Upload to your R4 WiFi

2. Add a 10μF capacitor between RESET and GND on the Arduino R4 to prevent auto-reset during programming:
   ```
   Arduino RESET pin ─┬─ 10μF Capacitor ─┬─ Arduino GND
                      │                 │
                      └─────────────────┘
   ```

3. When using in platformio.ini, update your port setting:
   ```ini
   upload_port = COM3  # Change to your Arduino's actual port
   ```

4. You may need to adjust the upload_flags in platformio.ini if you encounter timeout issues:
   ```ini
   upload_flags = 
       -P$UPLOAD_PORT
       -b19200
       -v  # For verbose output
   ``` 