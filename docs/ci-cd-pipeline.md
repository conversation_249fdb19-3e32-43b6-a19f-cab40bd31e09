# CI/CD Pipeline Documentation

This document describes the Continuous Integration and Continuous Deployment (CI/CD) pipeline for the ATTINY85 Control System.

## Overview

The CI/CD pipeline automates building, testing, and deployment processes to ensure code quality and reliability. It is implemented using GitHub Actions and consists of several stages:

1. **Unit Testing**: Runs tests for individual components using the native environment
2. **Integration Testing**: Tests system behavior using hardware emulation with SimulAVR
3. **Build**: Compiles firmware for the ATTINY85 microcontroller
4. **Test Report Publication**: Consolidates and publishes test results

## Pipeline Stages

### Unit Testing

Unit tests verify that individual components function correctly in isolation.

- **Environment**: Native (host machine)
- **Test Framework**: Unity
- **Files Tested**: Individual module test files in `test/unit/`
- **Output**: JUnit XML report and HTML report

Unit tests mock Arduino hardware dependencies to isolate module behavior for pure functional testing.

### Integration Testing

Integration tests verify that components work correctly together and interact as expected.

- **Environment**: Emulator (SimulAVR)
- **Test Framework**: Unity with SimulAVR emulation
- **Files Tested**: Integration test files in `test/integration/`
- **Output**: JUnit XML report and HTML report

Integration tests use hardware emulation to test the system in a more realistic environment.

### Build

The build stage compiles the firmware for the ATTINY85 microcontroller:

- **Platform**: AVR (ATtiny85)
- **Framework**: Arduino
- **Output**: Compiled firmware (HEX file)

### Test Report Publication

This stage collects, combines, and publishes test results:

- **Format**: HTML with iframes for test reports
- **Storage**: GitHub Pages
- **Access**: Test reports are accessible via GitHub Pages
- **History**: Reports are stored per-commit for historical tracking

## Running the Pipeline

### Automatic Triggers

The pipeline runs automatically on:
- Push to the `main` branch
- Pull requests against the `main` branch

### Manual Triggering

You can manually trigger the pipeline from GitHub:
1. Go to the "Actions" tab in the GitHub repository
2. Select the "CI/CD Pipeline" workflow
3. Click "Run workflow"

## Viewing Test Results

Test results are available in multiple formats:

1. **GitHub Actions Interface**: Summary results for each stage
2. **Artifacts**: Downloadable test reports from each run
3. **GitHub Pages**: Published HTML reports for all commits

To access published reports, visit:
`https://<username>.github.io/<repo-name>/test-reports/<commit-sha>/`

## Pipeline Configuration

The pipeline is configured in `.github/workflows/ci-cd.yml`. Key configuration files include:

- **PlatformIO Configuration**: `platformio.ini`
- **SimulAVR Integration**: `scripts/simulavr_script.py`
- **Test Files**: 
  - Unit tests: `test/unit/*.cpp`
  - Integration tests: `test/integration/*.cpp`

## Recent Improvements

The CI/CD pipeline has been enhanced to improve reliability and robustness:

1. **Improved Error Handling**: The pipeline now continues even when tests fail, ensuring all stages complete and provide reports.
2. **Enhanced SimulAVR Integration**: Integration tests include SimulAVR version verification and extended timeout configuration.
3. **Parallel Execution**: Removed unnecessary job dependencies to allow parallel execution of independent stages.
4. **Automatic Report Generation**: Test reports are always generated, even if tests fail or no test results are available.
5. **Explicit AVR Toolchain Installation**: Added explicit installation of gcc-avr and avr-libc to ensure build environment consistency.
6. **Artifact Collection**: Improved artifact collection to include test output and VCD trace files for debugging.

## Environment Variables

The pipeline uses the following environment variables and secrets:

| Name | Type | Description | Required |
|------|------|-------------|----------|
| `PLATFORMIO_BUILD_FLAGS` | Environment | Additional build flags for PlatformIO | No |
| `GITHUB_TOKEN` | Secret | GitHub token for artifact uploads | Yes |

## Troubleshooting

### Common Issues

1. **Failed Unit Tests**: 
   - Check the test report for specific failures
   - Fix the failing code or update tests if requirements have changed
   - Test output is saved as artifacts for investigation

2. **Failed Integration Tests**:
   - Check if SimulAVR is correctly installed in the runner
   - Verify that the integration test has the correct delay/timing parameters
   - Examine VCD trace files to debug timing or state machine issues

3. **Build Failures**:
   - Look for compiler errors in the build log
   - Check that all dependencies are correctly specified in `platformio.ini`
   - Ensure AVR toolchain is properly installed

### Continuous Integration Best Practices

1. **Always run tests locally** before pushing changes
2. **Review test reports** for every pipeline run
3. **Monitor build times** to identify performance bottlenecks
4. **Update dependencies** regularly to benefit from security fixes and improvements

### Getting Help

For issues with the CI/CD pipeline, check:
1. GitHub Actions documentation
2. PlatformIO documentation
3. SimulAVR documentation 