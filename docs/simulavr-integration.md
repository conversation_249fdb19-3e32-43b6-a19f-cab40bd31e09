# SimulAVR Integration Guide

This document provides information about the SimulAVR integration for emulation testing of the ATTINY85 Control System.

## Overview

SimulAVR is an AVR microcontroller simulator that allows us to run our firmware in a simulated environment. This provides the following benefits:

1. Testing without physical hardware
2. Reproducible test environment
3. Ability to trace program execution
4. Signal generation and waveform capture

## Prerequisites

To use SimulAVR with the ATTINY85 Control System, you'll need:

1. **SimulAVR**: Install from your package manager or build from source
   ```bash
   # On Ubuntu/Debian
   sudo apt-get install simulavr
   ```

2. **PlatformIO**: Required for building and running tests
   ```bash
   pip install platformio
   ```

3. **AVR Toolchain**: GCC for AVR and AVR libc
   ```bash
   sudo apt-get install gcc-avr avr-libc
   ```

## Configuration

The SimulAVR integration is configured through several files:

### 1. PlatformIO Configuration

In `platformio.ini`, the emulator environment is configured:

```ini
[env:emulator]
platform = atmelavr
board = attiny85
framework = arduino
build_flags =
    -DATTINY85=1
    -DARDUINO_ARCH_AVR=1
    -DEMULATION_MODE=1
    -std=gnu++11
    -fno-exceptions
    -DUNITY_INCLUDE_CONFIG_H
    -I include
lib_deps =
    throwtheswitch/Unity @ ^2.5.2
lib_compat_mode = off
test_build_src = yes
test_filter = integration
extra_scripts = 
    pre:scripts/simulavr_script.py
test_speed = 0 
; Speed 0 allows us to use the SimulAVR timing instead of Unity's
```

### 2. SimulAVR Script

The `scripts/simulavr_script.py` file contains the Python code that integrates PlatformIO with SimulAVR. Key functions:

- `SimulavrRunner`: Class that manages SimulAVR execution
- `on_test`: Callback that runs integration tests with SimulAVR

### 3. Mock Files

The `test/mocks` directory contains mock implementations of hardware-specific functions:

- `Arduino.h` and `Arduino.cpp`: Arduino API mocks
- `ATTINY85_emulator.h` and `ATTINY85_emulator.cpp`: ATTINY85 emulator utilities

## Running Tests with SimulAVR

To run integration tests with SimulAVR:

```bash
# Run all integration tests
platformio test -e emulator

# Run specific integration tests
platformio test -e emulator -f test_integration_basic

# Run with verbose output
platformio test -e emulator -v
```

## Troubleshooting SimulAVR Integration

### Common Issues

1. **SimulAVR not found**
   
   Ensure SimulAVR is installed and available in your PATH.
   ```bash
   which simulavr
   simulavr --version
   ```

2. **Test timeouts**

   Adjust the timeout by setting the `EMULATION_TIMEOUT` value:
   ```bash
   PLATFORMIO_BUILD_FLAGS="-DEMULATION_TIMEOUT=30000" platformio test -e emulator
   ```

3. **Missing VCD trace files**

   Check for a permissions issue or ensure the trace directory exists:
   ```bash
   mkdir -p test-traces
   chmod 755 test-traces
   ```

4. **Integration test failures**

   Examine the VCD trace files to debug timing or state machine issues:
   ```bash
   # Install GTKWave for VCD viewing
   sudo apt-get install gtkwave

   # View the trace
   gtkwave .pio/build/emulator/sim_trace.vcd
   ```

## Extending the Emulator

### Adding New Hardware Features

To add new hardware features to the emulator:

1. Update the `ATTINY85_emulator.h` and `ATTINY85_emulator.cpp` files
2. Add new mock functions to the Arduino mock files if needed
3. Update the SimulAVR script to handle any new requirements

### Debugging with GDB

SimulAVR supports GDB debugging:

```bash
# Run SimulAVR with GDB server
simulavr -d attiny85 -f .pio/build/emulator/firmware.elf -g -p 1212

# Connect with GDB in another terminal
avr-gdb -ex "target remote localhost:1212" .pio/build/emulator/firmware.elf
```

## VCD Waveform Capture

SimulAVR can capture waveforms in VCD format for later analysis:

1. Run tests with VCD capture enabled (default in our setup)
2. Locate the VCD file at `.pio/build/emulator/sim_trace.vcd`
3. Open the file with GTKWave or another VCD viewer
4. Analyze the signal waveforms to debug timing issues

## Performance Considerations

- SimulAVR can be slow when simulating complex behavior
- VCD trace capture increases memory usage and simulation time
- Adjust timeout values for longer tests
- Running with fewer test cases might be necessary for complex tests 