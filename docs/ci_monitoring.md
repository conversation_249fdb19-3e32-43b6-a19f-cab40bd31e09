# CI/CD Pipeline Monitoring System

This documentation describes the CI/CD pipeline monitoring system for the ATtiny85 Control System project, which stores pipeline run data in Firebase Firestore and provides a dashboard for visualization.

## Overview

The monitoring system consists of two main components:

1. **Data Collection**: A Node.js script that fetches GitHub Actions workflow run data using the GitHub API and stores it in Firebase Firestore.
2. **Dashboard**: A web application built with Express and EJS that visualizes the pipeline run data from Firestore.

This system allows you to:
- Track CI/CD pipeline performance over time
- Quickly identify failing jobs and their causes
- Monitor build success rates
- Analyze trends in build times and failure patterns

## Prerequisites

Before using the monitoring system, you need:

1. A Firebase project with Firestore enabled
2. A GitHub personal access token with `repo` scope
3. Node.js (v14 or later)
4. npm (v6 or later)

## Setup

### 1. Configure Firebase

1. Create a Firebase project at [https://console.firebase.google.com](https://console.firebase.google.com)
2. Enable Firestore in your project
3. Create a web app in your Firebase project
4. Get your Firebase configuration (apiKey, authDomain, etc.)

### 2. Configure the Monitoring System

Use the setup script to configure the system:

```bash
# Make the script executable
chmod +x scripts/setup_firebase.js

# Run the setup script
node scripts/setup_firebase.js
```

The script will ask for your Firebase configuration and GitHub personal access token, then create a `.env` file with your settings.

Alternatively, you can copy the `.env.example` file to `.env` and fill in the values manually:

```bash
cp .env.example .env
# Edit .env with your preferred text editor
```

### 3. Install Dependencies

Install the Node.js dependencies:

```bash
npm install
```

## Usage

### Storing Pipeline Runs

To store a GitHub Actions workflow run in Firestore:

```bash
# Store the latest workflow run
npm run store

# Store a specific workflow run by ID
npm run store 1234567890
```

This will fetch the workflow run data from GitHub, including all jobs, and store it in Firestore.

### Running the Dashboard

To start the dashboard:

```bash
npm start
```

This will start a web server on port 3000 (or the port specified in your `.env` file). Open your browser and go to `http://localhost:3000` to view the dashboard.

## Automation

### Automatic Data Collection

To automatically collect pipeline run data after each workflow run, you can add a step to your GitHub Actions workflow:

```yaml
# Add to .github/workflows/ci-cd.yml
jobs:
  # ... existing jobs ...
  
  record-pipeline-run:
    name: Record Pipeline Run
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, build, code-quality, code-coverage]
    if: always()
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Store pipeline run
        run: node scripts/store_pipeline_run.js ${{ github.run_id }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
          FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
          FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID }}
```

You'll need to add these secrets to your GitHub repository.

## Data Structure

The system stores pipeline run data in Firestore with the following structure:

### `pipeline-runs` Collection

Each document in the `pipeline-runs` collection represents a single workflow run:

```
pipeline-runs/
└── {run_id}/
    ├── id: string
    ├── repo: string
    ├── workflow: {
    │   ├── id: string
    │   └── name: string
    │ }
    ├── status: string
    ├── conclusion: string
    ├── branch: string
    ├── commit: {
    │   ├── id: string
    │   ├── message: string
    │   └── author: string
    │ }
    ├── created_at: timestamp
    ├── updated_at: timestamp
    ├── run_number: number
    ├── url: string
    ├── run_attempt: number
    ├── stored_at: timestamp
    ├── jobs: [
    │   {
    │     ├── id: string
    │     ├── name: string
    │     ├── status: string
    │     ├── conclusion: string
    │     ├── started_at: timestamp
    │     ├── completed_at: timestamp
    │     ├── duration: number
    │     ├── url: string
    │     └── errors: [
    │         {
    │           ├── name: string
    │           ├── number: number
    │           ├── status: string
    │           └── conclusion: string
    │         }
    │       ]
    │   }
    │ ]
    └── stats: {
        ├── total_jobs: number
        ├── successful_jobs: number
        ├── failed_jobs: number
        ├── skipped_jobs: number
        └── success_rate: number
      }
```

## Dashboard Features

The dashboard provides the following features:

1. **Pipeline Success Rate Chart**: Visualizes the success rate of pipeline runs over time
2. **Job Status Distribution Chart**: Shows the distribution of job statuses (success, failure, skipped)
3. **Recent Pipeline Runs Table**: Displays a list of recent pipeline runs with their status, branch, commit, and other details
4. **Run Details Page**: Shows detailed information about a specific pipeline run, including all jobs and any errors that occurred

## Troubleshooting

### Firebase Connection Issues

If you experience issues connecting to Firebase:

1. Verify your Firebase configuration in the `.env` file
2. Ensure your Firebase project has Firestore enabled
3. Check if your Firebase security rules allow read/write access

### GitHub API Rate Limiting

If you encounter GitHub API rate limiting:

1. Use a personal access token with higher rate limits
2. Implement caching to reduce the number of API calls
3. Add error handling to retry failed requests

### Dashboard Not Showing Data

If the dashboard is not showing any data:

1. Verify that you have successfully stored pipeline runs in Firestore
2. Check the browser console for any JavaScript errors
3. Ensure your Firestore security rules allow reading the data 