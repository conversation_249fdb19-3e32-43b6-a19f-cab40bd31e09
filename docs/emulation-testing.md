# ATTINY85 Control System Emulation Testing Guide

This guide explains how to use the SimulAVR emulation testing features to test the ATTINY85 Control System firmware without physical hardware.

## Prerequisites

Before running emulation tests, ensure you have the following:

1. **SimulAVR** installed on your system
   - Linux: `sudo apt-get install simulavr`
   - macOS: `brew install simulavr` (using Homebrew)
   - Windows: Use WSL or follow the build instructions at [SimulAVR GitHub](https://github.com/Traumflug/simulavr)

2. **PlatformIO** with the AVR toolchain installed

## Running Emulation Tests

To run the emulation tests:

```bash
# Run all integration tests using the emulator
platformio test -e emulator

# Run a specific integration test
platformio test -e emulator -f test_integration_basic
```

## Understanding Test Output

The test output will show:

1. SimulAVR initialization
2. Test execution progress
3. Unity test results
4. Waveform file location (for signal analysis)

Example output:
```
Executing task: python -m platformio test -e emulator -f test_integration_basic

Processing emulator (platform: atmelavr; board: attiny85; framework: arduino)
----------------------------------------------------
Verbose mode can be enabled via `-v, --verbose` option
Running SimulAVR with command: simulavr -d attiny85 -f .pio/build/emulator/firmware.elf -W 0x22,sim_trace.vcd -T exit
===== TEST RESULTS =====
TEST(Unity, test_system_initialization) PASS
TEST(Unity, test_pwm_input_response) PASS
TEST(Unity, test_air_pressure_timeout) PASS
TEST(Unity, test_error_recovery) PASS
=======================
Test 'test_integration_basic' success
Environment    Status    Duration
-------------  --------  ------------
emulator       SUCCESS   00:00:03.102
==================================== 1 succeeded in 00:00:03.102 ========
```

## Analyzing Waveforms

The emulation tests generate VCD (Value Change Dump) files that capture the state of all pins and registers during the test run. These files can be analyzed with a waveform viewer:

1. **Install a waveform viewer**:
   - GTKWave (cross-platform): `sudo apt-get install gtkwave` or download from [GTKWave website](http://gtkwave.sourceforge.net/)
   - WaveDrom (browser-based): [WaveDrom website](https://wavedrom.com/)

2. **Open the VCD file**:
   The VCD file is located at `.pio/build/emulator/sim_trace.vcd`

3. **Analyze the waveforms**:
   - Add signals of interest to the viewer
   - Look for specific events like state transitions
   - Analyze timing relationships between signals

## Creating Custom Emulation Tests

To create a new emulation test:

1. Create a new test file in the `test/integration/` directory
2. Use the `ATTINY85_emulator.h` utilities to control pin states and timing
3. Structure your test with proper `setUp()` and `tearDown()` functions
4. Call the necessary controller functions to test system behavior

Example test structure:
```cpp
#include <unity.h>
#include "mocks/ATTINY85_emulator.h"
#include "main_controller.h"

void setUp(void) {
    EmulatorUtils::initEmulator();
    main_controller_init();
}

void tearDown(void) {
    // Clean up
}

void test_my_feature(void) {
    // Set up initial conditions
    EmulatorUtils::setPinValue(PIN_SENSOR, HIGH);
    
    // Trigger behavior
    EmulatorUtils::advanceTime(100);
    
    // Verify expected outcome
    TEST_ASSERT_EQUAL(EXPECTED_STATE, getCurrentState());
}

#ifdef ARDUINO
void setup() {
    UNITY_BEGIN();
    RUN_TEST(test_my_feature);
    UNITY_END();
}

void loop() {}
#else
int main(void) {
    UNITY_BEGIN();
    RUN_TEST(test_my_feature);
    return UNITY_END();
}
#endif
```

## Advanced Emulation Features

The emulation framework supports several advanced features:

- **Pin Change Simulation**: `EmulatorUtils::generatePWM()` to generate PWM signals
- **Time Control**: `EmulatorUtils::advanceTime()` to simulate the passage of time
- **Interrupt Triggering**: `EmulatorUtils::triggerInterrupt()` to manually trigger ISRs
- **State Inspection**: `EmulatorUtils::getPinValue()` and `EmulatorUtils::getPinMode()`
- **Debug Tracing**: VCD files capture all pin and register changes

## Troubleshooting

Common issues and solutions:

1. **"simulavr: command not found"**
   - Ensure SimulAVR is properly installed
   - Check your PATH environment variable

2. **"Failed to open ELF file"**
   - Make sure the build completed successfully
   - Check file permissions

3. **"Tests pass but real hardware fails"**
   - The emulator may not perfectly match real hardware behavior
   - Check timing values and hardware-specific behaviors

4. **"Test hangs or times out"**
   - Look for infinite loops in your code
   - Check interrupt handling logic

## Integration with CI/CD

The emulation tests automatically run as part of the CI/CD pipeline through GitHub Actions:

1. They execute after the unit tests pass
2. Generated VCD files are archived as artifacts
3. Test results are published to GitHub Pages
4. Failures block the build workflow

To view results in the CI/CD pipeline, go to the Actions tab in GitHub and select the relevant workflow run. 