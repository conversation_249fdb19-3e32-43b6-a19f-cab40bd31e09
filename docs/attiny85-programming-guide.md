# ATtiny85 Programming Guide

This document provides detailed instructions for programming ATtiny85 microcontrollers with the firmware for the ATTINY85 Control System.

## Table of Contents

1. [Required Hardware](#required-hardware)
2. [Software Setup](#software-setup)
3. [Hardware Connections](#hardware-connections)
4. [Programming Procedures](#programming-procedures)
5. [Fuse Settings](#fuse-settings)
6. [Verification](#verification)
7. [Troubleshooting](#troubleshooting)
8. [Production Programming](#production-programming)

## Required Hardware

To program the ATtiny85, you will need:

- **ATtiny85 microcontroller** (8-pin DIP or SOIC package)
- **Arduino board** (Uno, Nano, or other Arduino board)
- **Programming adapter** for connecting to the ATtiny85
  - For DIP package: Breadboard with jumper wires
  - For SOIC package: SOIC test clip or adapter board
- **USB cable** for connecting Arduino to your computer
- **5V power supply** (can use USB power in most cases)
- **LED with current-limiting resistor** for verification (optional)

### Recommended Additional Items

- **Breadboard** for easy connections
- **Multimeter** for troubleshooting
- **Magnifying glass** for inspecting connections (especially with SOIC packages)

## Software Setup

### Installing PlatformIO

1. **Install Visual Studio Code**:
   - Download and install from [https://code.visualstudio.com/](https://code.visualstudio.com/)

2. **Install PlatformIO Extension**:
   - Open VS Code
   - Go to Extensions tab (or press Ctrl+Shift+X)
   - Search for "PlatformIO IDE"
   - Click Install

3. **Verify Installation**:
   - Open PlatformIO Home by clicking the PlatformIO icon in the sidebar
   - PlatformIO should automatically install required dependencies

### Installing avrdude (Alternative)

If you prefer using avrdude directly:

1. **Windows**:
   - Install WinAVR from [https://sourceforge.net/projects/winavr/](https://sourceforge.net/projects/winavr/)
   - Add the installation directory to your PATH

2. **Linux**:
   ```bash
   sudo apt-get update
   sudo apt-get install avrdude
   ```

3. **macOS**:
   ```bash
   brew install avrdude
   ```

### Setting Up Arduino as ISP

1. **Upload Arduino as ISP Sketch**:
   - Open Arduino IDE
   - Go to File > Examples > ArduinoISP > ArduinoISP
   - Upload this sketch to your Arduino board

2. **Driver Installation**:
   - Standard Arduino drivers should be sufficient
   - Most operating systems will detect Arduino automatically
   - For Windows, drivers are typically installed with Arduino IDE

## Hardware Connections

### Arduino to ATtiny85 (DIP Package)

| Arduino Pin | ATtiny85 Pin | Function  |
|-------------|--------------|-----------|
| 13 (SCK)    | 7 (PB2)      | SCK       |
| 12 (MISO)   | 6 (PB1)      | MISO      |
| 11 (MOSI)   | 5 (PB0)      | MOSI      |
| 10 (SS)     | 1 (RESET)    | RESET     |
| 5V          | 8 (VCC)      | Power     |
| GND         | 4 (GND)      | Ground    |

### Wiring Diagram

```
                 ATtiny85
                ┌────U────┐
        RESET  1│•       8│ VCC
 (MOSI) PB3/A3 2│        7│ PB2/SCK
 (MISO) PB4/A2 3│        6│ PB1/MISO
         GND   4│        5│ PB0/MOSI
                └─────────┘

                   Arduino
                ┌─────────────┐
                │             │
                │         13  │ SCK  → ATtiny pin 7
                │         12  │ MISO ← ATtiny pin 6
                │         11  │ MOSI → ATtiny pin 5
                │         10  │ SS   → ATtiny pin 1
                │         5V  │      → ATtiny pin 8
                │         GND │      → ATtiny pin 4
                │             │
                └─────────────┘
```

### Optional: Adding a 10μF Capacitor

Adding a 10μF capacitor between RESET and GND on the Arduino can help prevent auto-reset issues:

```
Arduino RESET pin ─┬─ 10μF Capacitor ─┬─ Arduino GND
                   │                 │
                   └─────────────────┘
```

### Connection Steps

1. Make sure the Arduino has the ArduinoISP sketch loaded
2. Place the ATtiny85 on a breadboard
3. Connect wires between Arduino and ATtiny85 according to the table above
4. Double-check all connections before powering on
5. Connect the Arduino to your computer via USB

## Programming Procedures

### Using PlatformIO

1. **Clone or Open the Project**:
   ```bash
   git clone https://github.com/user/attiny-control.git
   cd attiny-control
   ```
   Alternatively, open the project folder in VS Code with PlatformIO

2. **Configure platformio.ini**:
   Ensure the file contains the proper programmer settings:
   ```ini
   [env:attiny85]
   platform = atmelavr
   board = attiny85
   framework = arduino
   upload_protocol = arduino
   upload_flags = 
       -P$UPLOAD_PORT
       -b19200
   upload_port = COM3  # Change to your Arduino's port (e.g., /dev/ttyUSB0 for Linux)
   ```

3. **Build the Firmware**:
   - In PlatformIO, click the "Build" button, or
   - Run the following command:
     ```bash
     platformio run -e attiny85
     ```

4. **Upload the Firmware**:
   - In PlatformIO, click the "Upload" button, or
   - Run the following command:
     ```bash
     platformio run -e attiny85 -t upload
     ```

### Using avrdude Directly

1. **Build the Firmware** (if not already built):
   ```bash
   platformio run -e attiny85
   ```

2. **Program the ATtiny85**:
   ```bash
   avrdude -c arduino -P COM3 -b 19200 -p t85 -U flash:w:.pio/build/attiny85/firmware.hex:i
   ```
   Replace `COM3` with your Arduino's port (e.g., `/dev/ttyUSB0` on Linux).

3. **Set Fuses** (if needed):
   ```bash
   avrdude -c arduino -P COM3 -b 19200 -p t85 -U lfuse:w:0xE2:m -U hfuse:w:0xD7:m -U efuse:w:0xFF:m
   ```

## Fuse Settings

The ATtiny85 has three fuse bytes that configure its operation:

### Recommended Fuse Settings

| Fuse  | Value | Description |
|-------|-------|-------------|
| Low   | 0xE2  | 8MHz internal oscillator, no clock divider |
| High  | 0xD7  | Reset pin enabled, SPI programming enabled |
| Ext   | 0xFF  | Brown-out detection at 2.7V |

### Setting Fuses With PlatformIO

In platformio.ini:
```ini
board_fuses.lfuse = 0xE2
board_fuses.hfuse = 0xD7
board_fuses.efuse = 0xFF
```

Then run:
```bash
platformio run -e attiny85 -t fuses
```

### Setting Fuses With avrdude

```bash
avrdude -c arduino -P COM3 -b 19200 -p t85 -U lfuse:w:0xE2:m -U hfuse:w:0xD7:m -U efuse:w:0xFF:m
```

## Verification

### Basic Verification

1. **Read Fuses**:
   ```bash
   avrdude -c arduino -P COM3 -b 19200 -p t85 -U lfuse:r:-:h -U hfuse:r:-:h -U efuse:r:-:h
   ```

2. **Verify Flash Content**:
   ```bash
   avrdude -c arduino -P COM3 -b 19200 -p t85 -U flash:v:.pio/build/attiny85/firmware.hex:i
   ```

### Functional Testing

Connect an LED with a 220Ω resistor to PB0 (pin 5) and GND to see the status indicator blinking. The exact behavior will depend on the firmware, but you should see some activity.

## Troubleshooting

### Common Issues

| Problem | Possible Causes | Solutions |
|---------|----------------|-----------|
| "Device signature doesn't match" | Wrong chip selected, damaged chip, poor connections | Verify connections, try slower programming speed with `-b9600` flag |
| "Programmer not responding" | Arduino not properly set up, wrong port | Ensure ArduinoISP is loaded, check port, try re-uploading ArduinoISP sketch |
| "Programming failed" | Clock speed too high, unstable connections | Use slower baud rate with `-b9600` flag |
| Chip not responding | No power, incorrect wiring, chip damaged | Check voltage across VCC and GND, verify all connections |
| Verified OK but not working | Wrong fuse settings, application bug | Verify fuses, test with simple blink program |

### Debugging Commands

1. **Detect Programmer**:
   ```bash
   avrdude -c arduino -P COM3 -b 19200
   ```

2. **Read Chip Signature**:
   ```bash
   avrdude -c arduino -P COM3 -b 19200 -p t85
   ```

3. **Slower Programming Speed**:
   ```bash
   avrdude -c arduino -P COM3 -b 9600 -p t85 -U flash:w:firmware.hex:i
   ```

4. **Read Entire Flash Memory**:
   ```bash
   avrdude -c arduino -P COM3 -b 19200 -p t85 -U flash:r:flash_dump.bin:r
   ```

## Production Programming

For programming multiple ATtiny85 chips for production:

### Batch Programming Script

Create a file called `program_attiny.sh` (Linux/macOS) or `program_attiny.bat` (Windows):

Linux/macOS:
```bash
#!/bin/bash
echo "ATtiny85 Production Programmer"
echo "Connect the chip and press Enter to program, Ctrl+C to exit"

while true; do
    read -p "Ready to program next chip: " response
    avrdude -c arduino -P /dev/ttyUSB0 -b 19200 -p t85 -U flash:w:.pio/build/attiny85/firmware.hex:i -U lfuse:w:0xE2:m -U hfuse:w:0xD7:m -U efuse:w:0xFF:m
    
    if [ $? -eq 0 ]; then
        echo "Programming successful!"
    else
        echo "Programming failed!"
    fi
    echo "------------------------------"
done
```

Windows:
```batch
@echo off
echo ATtiny85 Production Programmer
echo Connect the chip and press Enter to program, Ctrl+C to exit

:loop
set /p response=Ready to program next chip: 
avrdude -c arduino -P COM3 -b 19200 -p t85 -U flash:w:.pio/build/attiny85/firmware.hex:i -U lfuse:w:0xE2:m -U hfuse:w:0xD7:m -U efuse:w:0xFF:m

if %ERRORLEVEL% EQU 0 (
    echo Programming successful!
) else (
    echo Programming failed!
)
echo ------------------------------
goto loop
```

### Setting Up a Production Jig

For high-volume production, consider creating a programming jig:

1. Use pogo pins to make temporary connections to the ATtiny85
2. Design a 3D-printed holder to align the chips
3. Connect the pogo pins to your Arduino programmer
4. Use the batch programming script to program each chip

## Additional Resources

- [ATtiny85 Datasheet](https://ww1.microchip.com/downloads/en/DeviceDoc/Atmel-2586-AVR-8-bit-Microcontroller-ATtiny25-ATtiny45-ATtiny85_Datasheet.pdf)
- [AVR Fuse Calculator](https://www.engbedded.com/fusecalc/)
- [PlatformIO Documentation](https://docs.platformio.org/)
- [Arduino as ISP Guide](https://docs.arduino.cc/built-in-examples/arduino-isp/ArduinoISP) 