# cloudbuild.yaml
steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'gcr.io/cannasol-automation-suite/attiny-builder', '.']
  
- name: 'gcr.io/cannasol-automation-suite/attiny-builder'
  args: ['sh', '-c', 'mkdir -p build && cd build && platformio run -e attiny85']
  
- name: 'gcr.io/cannasol-automation-suite/attiny-builder'
  args: ['sh', '-c', 'platformio test -e native']
  
- name: 'gcr.io/cloud-builders/gsutil'
  args: ['-m', 'cp', '.pio/build/attiny85/firmware.hex', 'gs://cannasol-firmware/attiny-control/firmware-$TAG_NAME.hex']
  
- name: 'gcr.io/cloud-builders/gsutil'
  args: ['cp', '.pio/build/attiny85/firmware.hex', 'gs://cannasol-firmware/attiny-control/firmware-latest.hex']
  
artifacts:
  objects:
    location: 'gs://cannasol-firmware/attiny-control/builds/'
    paths: ['.pio/build/attiny85/firmware.hex', '.pio/build/attiny85/firmware.elf'] 